import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app/core/database/app_database.dart';
import 'package:drift/native.dart';
import 'package:drift/drift.dart' as drift;

void main() {
  group('Database Initialization Tests', () {
    late AppDatabase db;

    setUp(() async {
      db = AppDatabase(NativeDatabase.memory());
    });

    tearDown(() async {
      await db.close();
    });

    test('should initialize database with cash account and categories', () async {
      // Initialize the database
      await db.initializeDatabase();

      // Check if cash account exists
      final cashAccount = await (db.select(db.bankAccounts)..where((tbl) => tbl.bankName.equals('Cash'))).getSingleOrNull();
      expect(cashAccount, isNotNull);
      expect(cashAccount!.bankName, equals('Cash'));
      expect(cashAccount.accountType, equals('Cash'));

      // Check if categories are seeded
      final categories = await db.select(db.categories).get();
      expect(categories.length, greaterThan(0));

      // Check for specific categories
      final expenseCategories = categories.where((cat) => cat.type == 'Expense').toList();
      final incomeCategories = categories.where((cat) => cat.type == 'Income').toList();

      expect(expenseCategories.length, greaterThan(0));
      expect(incomeCategories.length, greaterThan(0));

      // Check for specific categories
      final foodCategory = categories.where((cat) => cat.name == 'Food & Dining').firstOrNull;
      final salaryCategory = categories.where((cat) => cat.name == 'Salary').firstOrNull;

      expect(foodCategory, isNotNull);
      expect(salaryCategory, isNotNull);
    });

    test('should not create duplicate cash accounts', () async {
      // Initialize the database twice
      await db.initializeDatabase();
      await db.initializeDatabase();

      // Check that only one cash account exists
      final cashAccounts = await (db.select(db.bankAccounts)..where((tbl) => tbl.bankName.equals('Cash'))).get();
      expect(cashAccounts.length, equals(1));
    });

    test('should not create duplicate categories', () async {
      // Initialize the database twice
      await db.initializeDatabase();
      await db.initializeDatabase();

      // Check that categories are not duplicated
      final categories = await db.select(db.categories).get();
      final uniqueCategories = categories.map((cat) => cat.name).toSet();
      expect(categories.length, equals(uniqueCategories.length));
    });

    test('should reset and reinitialize database', () async {
      // Initialize the database
      await db.initializeDatabase();

      // Add some test data
      await db.into(db.transactions).insert(TransactionsCompanion.insert(
        date: DateTime.now(),
        amount: 100.0,
        type: const drift.Value('expense'),
        description: const drift.Value('Test transaction'),
      ));

      // Verify data exists
      final transactions = await db.select(db.transactions).get();
      expect(transactions.length, equals(1));

      // Reset and reinitialize
      await db.resetAndInitializeDatabase();

      // Verify data is cleared
      final transactionsAfterReset = await db.select(db.transactions).get();
      expect(transactionsAfterReset.length, equals(0));

      // Verify cash account and categories are recreated
      final cashAccount = await (db.select(db.bankAccounts)..where((tbl) => tbl.bankName.equals('Cash'))).getSingleOrNull();
      expect(cashAccount, isNotNull);

      final categories = await db.select(db.categories).get();
      expect(categories.length, greaterThan(0));
    });
  });
} 