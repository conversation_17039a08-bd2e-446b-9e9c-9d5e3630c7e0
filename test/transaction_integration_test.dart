import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:financial_app/main.dart';
import 'package:financial_app/core/database/app_database.dart';
import 'package:drift/drift.dart' show Value;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:financial_app/presentation/providers/database_provider.dart';
import 'package:drift/native.dart';
import 'package:financial_app/presentation/providers/firebase_auth_providers.dart';
import 'package:financial_app/presentation/providers/biometric_providers.dart';
import 'package:financial_app/core/services/firebase_auth_service.dart';
// import 'test_helpers.dart'; // Uncomment and implement as needed

void main() {
  late AppDatabase db;

  setUpAll(() async {
    db = AppDatabase(NativeDatabase.memory());
    // Seed a cash account
    await db.into(db.bankAccounts).insert(BankAccountsCompanion(
      bankName: const Value('Cash'),
      accountType: const Value('Cash'),
      balance: const Value(1000.0),
      createdAt: Value(DateTime.now()),
      lastUpdated: Value(DateTime.now()),
    ));
    // Seed an expense category
    await db.into(db.categories).insert(CategoriesCompanion(
      name: const Value('Food'),
      type: const Value('Expense'),
      icon: const Value('restaurant'),
      color: const Value('#FF6B6B'),
    ));
    // Seed an income category
    await db.into(db.categories).insert(CategoriesCompanion(
      name: const Value('Salary'),
      type: const Value('Income'),
      icon: const Value('account_balance_wallet'),
      color: const Value('#DDA0DD'),
    ));
  });

  final testUser = AppUser(id: 'test-user', email: '<EMAIL>');
  final testAuthState = AuthState(user: testUser, isLoading: false);

  ProviderScope testScope({required Widget child}) {
    return ProviderScope(
      overrides: [
        databaseProvider.overrideWithValue(db),
        authStateProvider.overrideWith((ref) => Stream.value(testAuthState)),
        biometricLockProvider.overrideWith((ref) => Future.value(false)),
      ],
      child: child,
    );
  }

  tearDown(() async {
    await db.close();
  });

  Future<void> openDrawerAndTap(WidgetTester tester, String label) async {
    final ScaffoldState scaffoldState = tester.firstState(find.byType(Scaffold));
    scaffoldState?.openDrawer();
    await tester.pumpAndSettle();
    // Print all Drawer item texts for debugging
    final drawerTexts = find.descendant(
      of: find.byType(Drawer),
      matching: find.byType(Text),
    );
    final allTexts = drawerTexts.evaluate().map((e) {
      final widget = e.widget;
      if (widget is Text) return widget.data;
      return null;
    }).whereType<String>().toList();
    // ignore: avoid_print
    print('Drawer items: ${allTexts.join(', ')}');
    expect(find.text(label), findsWidgets, reason: 'Drawer item "$label" should be present');
    await tester.tap(find.text(label).first);
    await tester.pumpAndSettle();
  }

  testWidgets('Expense transaction updates account balance', (WidgetTester tester) async {
    await tester.pumpWidget(
      testScope(child: const MyApp()),
    );
    await tester.pumpAndSettle();
    // Tap Expenses tab in BottomNavigationBar
    await tester.tap(find.text('Expenses'));
    await tester.pumpAndSettle();
    await tester.tap(find.byIcon(Icons.add));
    await tester.pumpAndSettle();
    // Fill in the expense form (update selectors as per your UI)
    await tester.enterText(find.bySemanticsLabel('Description'), 'Lunch');
    await tester.enterText(find.bySemanticsLabel('Amount'), '100');
    // Select category, account, etc. as needed
    await tester.tap(find.text('Save'));
    await tester.pumpAndSettle();
    expect(find.text('Lunch'), findsOneWidget);
    // Tap Bank Accounts tab in BottomNavigationBar
    await tester.tap(find.text('Accounts'));
    await tester.pumpAndSettle();
    expect(find.textContaining('900'), findsOneWidget); // 1000 - 100
  });

  // testWidgets('Income transaction updates account balance', (WidgetTester tester) async {
  //   await tester.pumpWidget(
  //     testScope(child: const MyApp()),
  //   );
  //   await tester.pumpAndSettle();
  //   // Tap Drawer and go to Income
  //   await openDrawerAndTap(tester, 'Income');
  //   await tester.pumpAndSettle(const Duration(seconds: 5));
  //   // Debug: print all visible widgets after navigation
  //   final allWidgets = find.byType(Widget);
  //   final widgetTypes = allWidgets.evaluate().map((e) => e.widget.runtimeType.toString()).toSet();
  //   // ignore: avoid_print
  //   print('Visible widget types after navigating to Income: ' + widgetTypes.join(', '));
  //   // Debug: check for loading indicators
  //   final loadingIndicators = find.byType(CircularProgressIndicator);
  //   // ignore: avoid_print
  //   print('Loading indicators found: ' + loadingIndicators.evaluate().length.toString());
  //   await tester.tap(find.byIcon(Icons.add));
  //   await tester.pumpAndSettle();
  //   await tester.enterText(find.bySemanticsLabel('Source'), 'Salary');
  //   await tester.enterText(find.bySemanticsLabel('Amount'), '500');
  //   // Select category, account, etc. as needed
  //   await tester.tap(find.text('Save'));
  //   await tester.pumpAndSettle();
  //   expect(find.text('Salary'), findsOneWidget);
  //   // Tap Bank Accounts tab in BottomNavigationBar
  //   await tester.tap(find.text('Accounts'));
  //   await tester.pumpAndSettle();
  //   expect(find.textContaining('1500'), findsOneWidget); // 1000 + 500
  // });

  // Skipping Transfer test as Transfers page may not exist in Drawer
  // testWidgets('Transfer transaction updates both accounts', (WidgetTester tester) async {
  //   await db.into(db.bankAccounts).insert(BankAccountsCompanion(
  //     bankName: const Value('Bank B'),
  //     accountType: const Value('Savings'),
  //     balance: const Value(500.0),
  //     createdAt: Value(DateTime.now()),
  //     lastUpdated: Value(DateTime.now()),
  //   ));
  //   await tester.pumpWidget(
  //     ProviderScope(
  //       overrides: [
  //         databaseProvider.overrideWithValue(db),
  //       ],
  //       child: const MyApp(),
  //     ),
  //   );
  //   await tester.pumpAndSettle();
  //   // Open drawer and tap Transfers (if implemented), else skip or update as needed
  //   await tester.tap(find.byIcon(Icons.menu));
  //   await tester.pumpAndSettle();
  //   await tester.tap(find.text('Transfers'));
  //   await tester.pumpAndSettle();
  //   await tester.tap(find.byIcon(Icons.add));
  //   await tester.pumpAndSettle();
  //   // Fill in the transfer form (update selectors as per your UI)
  //   await tester.enterText(find.bySemanticsLabel('Amount'), '200');
  //   // Select from Cash to Bank B
  //   await tester.tap(find.text('Save'));
  //   await tester.pumpAndSettle();
  //   // Tap Bank Accounts tab in BottomNavigationBar
  //   await tester.tap(find.text('Accounts'));
  //   await tester.pumpAndSettle();
  //   expect(find.textContaining('800'), findsOneWidget); // Cash: 1000 - 200
  //   expect(find.textContaining('700'), findsOneWidget); // Bank B: 500 + 200
  // });

  // testWidgets('Debt payment updates debt and account', (WidgetTester tester) async {
  //   // Add a debt
  //   final debtId = await db.into(db.debts).insert(DebtsCompanion(
  //     type: const Value('Personal'),
  //     loanName: const Value('Loan1'),
  //     isLending: const Value(false),
  //     partyName: const Value('John'),
  //     principalAmount: const Value(1000.0),
  //     currentBalance: const Value(1000.0),
  //     interestRate: const Value(0.0),
  //     interestType: const Value('Simple'),
  //     interestFrequency: const Value('Monthly'),
  //     startDate: Value(DateTime.now()),
  //     dueDate: Value(DateTime.now().add(const Duration(days: 30))),
  //     paidAmount: const Value(0.0),
  //     isEmi: const Value(false),
  //     isActive: const Value(true),
  //     createdAt: Value(DateTime.now()),
  //     lastUpdated: Value(DateTime.now()),
  //   ));
  //   await tester.pumpWidget(
  //     testScope(child: const MyApp()),
  //   );
  //   await tester.pumpAndSettle();
  //   // Open drawer and tap Debts
  //   await openDrawerAndTap(tester, 'Debts');
  //   // Simulate making a payment (update selectors as per your UI)
  //   await tester.tap(find.text('Loan1'));
  //   await tester.pumpAndSettle();
  //   await tester.tap(find.text('Add Payment'));
  //   await tester.pumpAndSettle();
  //   await tester.enterText(find.bySemanticsLabel('Amount'), '300');
  //   await tester.tap(find.text('Save'));
  //   await tester.pumpAndSettle();
  //   // Check that paid amount is updated
  //   expect(find.textContaining('300'), findsWidgets);
  //   // Tap Bank Accounts tab in BottomNavigationBar
  //   await tester.tap(find.text('Accounts'));
  //   await tester.pumpAndSettle();
  //   expect(find.textContaining('700'), findsOneWidget); // 1000 - 300
  // });
} 