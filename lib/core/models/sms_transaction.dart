import 'package:drift/drift.dart';

part 'sms_transaction.g.dart';

class SmsTransaction extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get message => text()();
  RealColumn get amount => real()();
  TextColumn get vpa => text().nullable()();
  TextColumn get bankName => text()();
  TextColumn get accountNumber => text().nullable()();
  DateTimeColumn get transactionDate => dateTime()();
  TextColumn get transactionType => text()(); // 'debit' or 'credit'
  TextColumn get refNumber => text().nullable()();
  BoolColumn get isProcessed => boolean().withDefault(const Constant(false))();
  IntColumn get linkedExpenseId => integer().nullable()();
  DateTimeColumn get createdAt => dateTime()();
}

class VpaMapping extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get vpa => text().unique()();
  TextColumn get categoryName => text()();
  IntColumn get categoryId => integer()();
  TextColumn get description => text().nullable()();
  BoolColumn get autoAdd => boolean().withDefault(const Constant(false))();
  TextColumn get bankName => text().nullable()();
  TextColumn get accountNumber => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
}

class BankAccount extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get bankName => text()();
  TextColumn get accountNumber => text().nullable()();
  TextColumn get accountType => text()(); // 'savings', 'current', 'credit'
  RealColumn get balance => real().withDefault(const Constant(0))();
  TextColumn get lastFourDigits => text().nullable()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  DateTimeColumn get createdAt => dateTime()();
}

@DriftDatabase(tables: [SmsTransaction, VpaMapping, BankAccount])
class AppDatabase extends _$AppDatabase {
  AppDatabase(super.e);

  @override
  int get schemaVersion => 4;

  // SMS Transaction methods
  Future<List<SmsTransactionData>> getAllSmsTransactions() {
    return select(smsTransaction).get();
  }

  Future<List<SmsTransactionData>> getUnprocessedSmsTransactions() {
    return (select(smsTransaction)..where((t) => t.isProcessed.equals(false))).get();
  }

  Future<int> insertSmsTransaction(SmsTransactionCompanion transaction) {
    return into(smsTransaction).insert(transaction);
  }

  Future<void> markSmsTransactionAsProcessed(int id, int? expenseId) {
    return (update(smsTransaction)..where((t) => t.id.equals(id))).write(
      SmsTransactionCompanion(
        isProcessed: const Value(true),
        linkedExpenseId: Value(expenseId),
      ),
    );
  }

  // VPA Mapping methods
  Future<List<VpaMappingData>> getAllVpaMappings() {
    return select(vpaMapping).get();
  }

  Future<VpaMappingData?> getVpaMapping(String vpa) {
    return (select(vpaMapping)..where((v) => v.vpa.equals(vpa))).getSingleOrNull();
  }

  Future<int> insertVpaMapping(VpaMappingCompanion mapping) {
    return into(vpaMapping).insert(mapping);
  }

  Future<void> updateVpaMapping(int id, VpaMappingCompanion mapping) {
    return (update(vpaMapping)..where((v) => v.id.equals(id))).write(mapping);
  }

  // Bank Account methods
  Future<List<BankAccountData>> getAllBankAccounts() {
    return select(bankAccount).get();
  }

  Future<List<BankAccountData>> getBankAccountsByBank(String bankName) {
    return (select(bankAccount)..where((b) => b.bankName.equals(bankName))).get();
  }

  Future<int> insertBankAccount(BankAccountCompanion account) {
    return into(bankAccount).insert(account);
  }

  Future<void> updateBankAccount(int id, BankAccountCompanion account) {
    return (update(bankAccount)..where((b) => b.id.equals(id))).write(account);
  }
} 