// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sms_transaction.dart';

// ignore_for_file: type=lint
class $SmsTransactionTable extends SmsTransaction
    with TableInfo<$SmsTransactionTable, SmsTransactionData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SmsTransactionTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _messageMeta =
      const VerificationMeta('message');
  @override
  late final GeneratedColumn<String> message = GeneratedColumn<String>(
      'message', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _amountMeta = const VerificationMeta('amount');
  @override
  late final GeneratedColumn<double> amount = GeneratedColumn<double>(
      'amount', aliasedName, false,
      type: DriftSqlType.double, requiredDuringInsert: true);
  static const VerificationMeta _vpaMeta = const VerificationMeta('vpa');
  @override
  late final GeneratedColumn<String> vpa = GeneratedColumn<String>(
      'vpa', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _bankNameMeta =
      const VerificationMeta('bankName');
  @override
  late final GeneratedColumn<String> bankName = GeneratedColumn<String>(
      'bank_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _accountNumberMeta =
      const VerificationMeta('accountNumber');
  @override
  late final GeneratedColumn<String> accountNumber = GeneratedColumn<String>(
      'account_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _transactionDateMeta =
      const VerificationMeta('transactionDate');
  @override
  late final GeneratedColumn<DateTime> transactionDate =
      GeneratedColumn<DateTime>('transaction_date', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _transactionTypeMeta =
      const VerificationMeta('transactionType');
  @override
  late final GeneratedColumn<String> transactionType = GeneratedColumn<String>(
      'transaction_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _refNumberMeta =
      const VerificationMeta('refNumber');
  @override
  late final GeneratedColumn<String> refNumber = GeneratedColumn<String>(
      'ref_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isProcessedMeta =
      const VerificationMeta('isProcessed');
  @override
  late final GeneratedColumn<bool> isProcessed = GeneratedColumn<bool>(
      'is_processed', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_processed" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _linkedExpenseIdMeta =
      const VerificationMeta('linkedExpenseId');
  @override
  late final GeneratedColumn<int> linkedExpenseId = GeneratedColumn<int>(
      'linked_expense_id', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        message,
        amount,
        vpa,
        bankName,
        accountNumber,
        transactionDate,
        transactionType,
        refNumber,
        isProcessed,
        linkedExpenseId,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sms_transaction';
  @override
  VerificationContext validateIntegrity(Insertable<SmsTransactionData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('message')) {
      context.handle(_messageMeta,
          message.isAcceptableOrUnknown(data['message']!, _messageMeta));
    } else if (isInserting) {
      context.missing(_messageMeta);
    }
    if (data.containsKey('amount')) {
      context.handle(_amountMeta,
          amount.isAcceptableOrUnknown(data['amount']!, _amountMeta));
    } else if (isInserting) {
      context.missing(_amountMeta);
    }
    if (data.containsKey('vpa')) {
      context.handle(
          _vpaMeta, vpa.isAcceptableOrUnknown(data['vpa']!, _vpaMeta));
    }
    if (data.containsKey('bank_name')) {
      context.handle(_bankNameMeta,
          bankName.isAcceptableOrUnknown(data['bank_name']!, _bankNameMeta));
    } else if (isInserting) {
      context.missing(_bankNameMeta);
    }
    if (data.containsKey('account_number')) {
      context.handle(
          _accountNumberMeta,
          accountNumber.isAcceptableOrUnknown(
              data['account_number']!, _accountNumberMeta));
    }
    if (data.containsKey('transaction_date')) {
      context.handle(
          _transactionDateMeta,
          transactionDate.isAcceptableOrUnknown(
              data['transaction_date']!, _transactionDateMeta));
    } else if (isInserting) {
      context.missing(_transactionDateMeta);
    }
    if (data.containsKey('transaction_type')) {
      context.handle(
          _transactionTypeMeta,
          transactionType.isAcceptableOrUnknown(
              data['transaction_type']!, _transactionTypeMeta));
    } else if (isInserting) {
      context.missing(_transactionTypeMeta);
    }
    if (data.containsKey('ref_number')) {
      context.handle(_refNumberMeta,
          refNumber.isAcceptableOrUnknown(data['ref_number']!, _refNumberMeta));
    }
    if (data.containsKey('is_processed')) {
      context.handle(
          _isProcessedMeta,
          isProcessed.isAcceptableOrUnknown(
              data['is_processed']!, _isProcessedMeta));
    }
    if (data.containsKey('linked_expense_id')) {
      context.handle(
          _linkedExpenseIdMeta,
          linkedExpenseId.isAcceptableOrUnknown(
              data['linked_expense_id']!, _linkedExpenseIdMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  SmsTransactionData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SmsTransactionData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      message: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}message'])!,
      amount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}amount'])!,
      vpa: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}vpa']),
      bankName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bank_name'])!,
      accountNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_number']),
      transactionDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}transaction_date'])!,
      transactionType: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}transaction_type'])!,
      refNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}ref_number']),
      isProcessed: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_processed'])!,
      linkedExpenseId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}linked_expense_id']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $SmsTransactionTable createAlias(String alias) {
    return $SmsTransactionTable(attachedDatabase, alias);
  }
}

class SmsTransactionData extends DataClass
    implements Insertable<SmsTransactionData> {
  final int id;
  final String message;
  final double amount;
  final String? vpa;
  final String bankName;
  final String? accountNumber;
  final DateTime transactionDate;
  final String transactionType;
  final String? refNumber;
  final bool isProcessed;
  final int? linkedExpenseId;
  final DateTime createdAt;
  const SmsTransactionData(
      {required this.id,
      required this.message,
      required this.amount,
      this.vpa,
      required this.bankName,
      this.accountNumber,
      required this.transactionDate,
      required this.transactionType,
      this.refNumber,
      required this.isProcessed,
      this.linkedExpenseId,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['message'] = Variable<String>(message);
    map['amount'] = Variable<double>(amount);
    if (!nullToAbsent || vpa != null) {
      map['vpa'] = Variable<String>(vpa);
    }
    map['bank_name'] = Variable<String>(bankName);
    if (!nullToAbsent || accountNumber != null) {
      map['account_number'] = Variable<String>(accountNumber);
    }
    map['transaction_date'] = Variable<DateTime>(transactionDate);
    map['transaction_type'] = Variable<String>(transactionType);
    if (!nullToAbsent || refNumber != null) {
      map['ref_number'] = Variable<String>(refNumber);
    }
    map['is_processed'] = Variable<bool>(isProcessed);
    if (!nullToAbsent || linkedExpenseId != null) {
      map['linked_expense_id'] = Variable<int>(linkedExpenseId);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  SmsTransactionCompanion toCompanion(bool nullToAbsent) {
    return SmsTransactionCompanion(
      id: Value(id),
      message: Value(message),
      amount: Value(amount),
      vpa: vpa == null && nullToAbsent ? const Value.absent() : Value(vpa),
      bankName: Value(bankName),
      accountNumber: accountNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(accountNumber),
      transactionDate: Value(transactionDate),
      transactionType: Value(transactionType),
      refNumber: refNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(refNumber),
      isProcessed: Value(isProcessed),
      linkedExpenseId: linkedExpenseId == null && nullToAbsent
          ? const Value.absent()
          : Value(linkedExpenseId),
      createdAt: Value(createdAt),
    );
  }

  factory SmsTransactionData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return SmsTransactionData(
      id: serializer.fromJson<int>(json['id']),
      message: serializer.fromJson<String>(json['message']),
      amount: serializer.fromJson<double>(json['amount']),
      vpa: serializer.fromJson<String?>(json['vpa']),
      bankName: serializer.fromJson<String>(json['bankName']),
      accountNumber: serializer.fromJson<String?>(json['accountNumber']),
      transactionDate: serializer.fromJson<DateTime>(json['transactionDate']),
      transactionType: serializer.fromJson<String>(json['transactionType']),
      refNumber: serializer.fromJson<String?>(json['refNumber']),
      isProcessed: serializer.fromJson<bool>(json['isProcessed']),
      linkedExpenseId: serializer.fromJson<int?>(json['linkedExpenseId']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'message': serializer.toJson<String>(message),
      'amount': serializer.toJson<double>(amount),
      'vpa': serializer.toJson<String?>(vpa),
      'bankName': serializer.toJson<String>(bankName),
      'accountNumber': serializer.toJson<String?>(accountNumber),
      'transactionDate': serializer.toJson<DateTime>(transactionDate),
      'transactionType': serializer.toJson<String>(transactionType),
      'refNumber': serializer.toJson<String?>(refNumber),
      'isProcessed': serializer.toJson<bool>(isProcessed),
      'linkedExpenseId': serializer.toJson<int?>(linkedExpenseId),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  SmsTransactionData copyWith(
          {int? id,
          String? message,
          double? amount,
          Value<String?> vpa = const Value.absent(),
          String? bankName,
          Value<String?> accountNumber = const Value.absent(),
          DateTime? transactionDate,
          String? transactionType,
          Value<String?> refNumber = const Value.absent(),
          bool? isProcessed,
          Value<int?> linkedExpenseId = const Value.absent(),
          DateTime? createdAt}) =>
      SmsTransactionData(
        id: id ?? this.id,
        message: message ?? this.message,
        amount: amount ?? this.amount,
        vpa: vpa.present ? vpa.value : this.vpa,
        bankName: bankName ?? this.bankName,
        accountNumber:
            accountNumber.present ? accountNumber.value : this.accountNumber,
        transactionDate: transactionDate ?? this.transactionDate,
        transactionType: transactionType ?? this.transactionType,
        refNumber: refNumber.present ? refNumber.value : this.refNumber,
        isProcessed: isProcessed ?? this.isProcessed,
        linkedExpenseId: linkedExpenseId.present
            ? linkedExpenseId.value
            : this.linkedExpenseId,
        createdAt: createdAt ?? this.createdAt,
      );
  SmsTransactionData copyWithCompanion(SmsTransactionCompanion data) {
    return SmsTransactionData(
      id: data.id.present ? data.id.value : this.id,
      message: data.message.present ? data.message.value : this.message,
      amount: data.amount.present ? data.amount.value : this.amount,
      vpa: data.vpa.present ? data.vpa.value : this.vpa,
      bankName: data.bankName.present ? data.bankName.value : this.bankName,
      accountNumber: data.accountNumber.present
          ? data.accountNumber.value
          : this.accountNumber,
      transactionDate: data.transactionDate.present
          ? data.transactionDate.value
          : this.transactionDate,
      transactionType: data.transactionType.present
          ? data.transactionType.value
          : this.transactionType,
      refNumber: data.refNumber.present ? data.refNumber.value : this.refNumber,
      isProcessed:
          data.isProcessed.present ? data.isProcessed.value : this.isProcessed,
      linkedExpenseId: data.linkedExpenseId.present
          ? data.linkedExpenseId.value
          : this.linkedExpenseId,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SmsTransactionData(')
          ..write('id: $id, ')
          ..write('message: $message, ')
          ..write('amount: $amount, ')
          ..write('vpa: $vpa, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('transactionDate: $transactionDate, ')
          ..write('transactionType: $transactionType, ')
          ..write('refNumber: $refNumber, ')
          ..write('isProcessed: $isProcessed, ')
          ..write('linkedExpenseId: $linkedExpenseId, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      message,
      amount,
      vpa,
      bankName,
      accountNumber,
      transactionDate,
      transactionType,
      refNumber,
      isProcessed,
      linkedExpenseId,
      createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SmsTransactionData &&
          other.id == this.id &&
          other.message == this.message &&
          other.amount == this.amount &&
          other.vpa == this.vpa &&
          other.bankName == this.bankName &&
          other.accountNumber == this.accountNumber &&
          other.transactionDate == this.transactionDate &&
          other.transactionType == this.transactionType &&
          other.refNumber == this.refNumber &&
          other.isProcessed == this.isProcessed &&
          other.linkedExpenseId == this.linkedExpenseId &&
          other.createdAt == this.createdAt);
}

class SmsTransactionCompanion extends UpdateCompanion<SmsTransactionData> {
  final Value<int> id;
  final Value<String> message;
  final Value<double> amount;
  final Value<String?> vpa;
  final Value<String> bankName;
  final Value<String?> accountNumber;
  final Value<DateTime> transactionDate;
  final Value<String> transactionType;
  final Value<String?> refNumber;
  final Value<bool> isProcessed;
  final Value<int?> linkedExpenseId;
  final Value<DateTime> createdAt;
  const SmsTransactionCompanion({
    this.id = const Value.absent(),
    this.message = const Value.absent(),
    this.amount = const Value.absent(),
    this.vpa = const Value.absent(),
    this.bankName = const Value.absent(),
    this.accountNumber = const Value.absent(),
    this.transactionDate = const Value.absent(),
    this.transactionType = const Value.absent(),
    this.refNumber = const Value.absent(),
    this.isProcessed = const Value.absent(),
    this.linkedExpenseId = const Value.absent(),
    this.createdAt = const Value.absent(),
  });
  SmsTransactionCompanion.insert({
    this.id = const Value.absent(),
    required String message,
    required double amount,
    this.vpa = const Value.absent(),
    required String bankName,
    this.accountNumber = const Value.absent(),
    required DateTime transactionDate,
    required String transactionType,
    this.refNumber = const Value.absent(),
    this.isProcessed = const Value.absent(),
    this.linkedExpenseId = const Value.absent(),
    required DateTime createdAt,
  })  : message = Value(message),
        amount = Value(amount),
        bankName = Value(bankName),
        transactionDate = Value(transactionDate),
        transactionType = Value(transactionType),
        createdAt = Value(createdAt);
  static Insertable<SmsTransactionData> custom({
    Expression<int>? id,
    Expression<String>? message,
    Expression<double>? amount,
    Expression<String>? vpa,
    Expression<String>? bankName,
    Expression<String>? accountNumber,
    Expression<DateTime>? transactionDate,
    Expression<String>? transactionType,
    Expression<String>? refNumber,
    Expression<bool>? isProcessed,
    Expression<int>? linkedExpenseId,
    Expression<DateTime>? createdAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (message != null) 'message': message,
      if (amount != null) 'amount': amount,
      if (vpa != null) 'vpa': vpa,
      if (bankName != null) 'bank_name': bankName,
      if (accountNumber != null) 'account_number': accountNumber,
      if (transactionDate != null) 'transaction_date': transactionDate,
      if (transactionType != null) 'transaction_type': transactionType,
      if (refNumber != null) 'ref_number': refNumber,
      if (isProcessed != null) 'is_processed': isProcessed,
      if (linkedExpenseId != null) 'linked_expense_id': linkedExpenseId,
      if (createdAt != null) 'created_at': createdAt,
    });
  }

  SmsTransactionCompanion copyWith(
      {Value<int>? id,
      Value<String>? message,
      Value<double>? amount,
      Value<String?>? vpa,
      Value<String>? bankName,
      Value<String?>? accountNumber,
      Value<DateTime>? transactionDate,
      Value<String>? transactionType,
      Value<String?>? refNumber,
      Value<bool>? isProcessed,
      Value<int?>? linkedExpenseId,
      Value<DateTime>? createdAt}) {
    return SmsTransactionCompanion(
      id: id ?? this.id,
      message: message ?? this.message,
      amount: amount ?? this.amount,
      vpa: vpa ?? this.vpa,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      transactionDate: transactionDate ?? this.transactionDate,
      transactionType: transactionType ?? this.transactionType,
      refNumber: refNumber ?? this.refNumber,
      isProcessed: isProcessed ?? this.isProcessed,
      linkedExpenseId: linkedExpenseId ?? this.linkedExpenseId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (message.present) {
      map['message'] = Variable<String>(message.value);
    }
    if (amount.present) {
      map['amount'] = Variable<double>(amount.value);
    }
    if (vpa.present) {
      map['vpa'] = Variable<String>(vpa.value);
    }
    if (bankName.present) {
      map['bank_name'] = Variable<String>(bankName.value);
    }
    if (accountNumber.present) {
      map['account_number'] = Variable<String>(accountNumber.value);
    }
    if (transactionDate.present) {
      map['transaction_date'] = Variable<DateTime>(transactionDate.value);
    }
    if (transactionType.present) {
      map['transaction_type'] = Variable<String>(transactionType.value);
    }
    if (refNumber.present) {
      map['ref_number'] = Variable<String>(refNumber.value);
    }
    if (isProcessed.present) {
      map['is_processed'] = Variable<bool>(isProcessed.value);
    }
    if (linkedExpenseId.present) {
      map['linked_expense_id'] = Variable<int>(linkedExpenseId.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SmsTransactionCompanion(')
          ..write('id: $id, ')
          ..write('message: $message, ')
          ..write('amount: $amount, ')
          ..write('vpa: $vpa, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('transactionDate: $transactionDate, ')
          ..write('transactionType: $transactionType, ')
          ..write('refNumber: $refNumber, ')
          ..write('isProcessed: $isProcessed, ')
          ..write('linkedExpenseId: $linkedExpenseId, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }
}

class $VpaMappingTable extends VpaMapping
    with TableInfo<$VpaMappingTable, VpaMappingData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $VpaMappingTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _vpaMeta = const VerificationMeta('vpa');
  @override
  late final GeneratedColumn<String> vpa = GeneratedColumn<String>(
      'vpa', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('UNIQUE'));
  static const VerificationMeta _categoryNameMeta =
      const VerificationMeta('categoryName');
  @override
  late final GeneratedColumn<String> categoryName = GeneratedColumn<String>(
      'category_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _categoryIdMeta =
      const VerificationMeta('categoryId');
  @override
  late final GeneratedColumn<int> categoryId = GeneratedColumn<int>(
      'category_id', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _autoAddMeta =
      const VerificationMeta('autoAdd');
  @override
  late final GeneratedColumn<bool> autoAdd = GeneratedColumn<bool>(
      'auto_add', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("auto_add" IN (0, 1))'),
      defaultValue: const Constant(false));
  static const VerificationMeta _bankNameMeta =
      const VerificationMeta('bankName');
  @override
  late final GeneratedColumn<String> bankName = GeneratedColumn<String>(
      'bank_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _accountNumberMeta =
      const VerificationMeta('accountNumber');
  @override
  late final GeneratedColumn<String> accountNumber = GeneratedColumn<String>(
      'account_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        vpa,
        categoryName,
        categoryId,
        description,
        autoAdd,
        bankName,
        accountNumber,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'vpa_mapping';
  @override
  VerificationContext validateIntegrity(Insertable<VpaMappingData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('vpa')) {
      context.handle(
          _vpaMeta, vpa.isAcceptableOrUnknown(data['vpa']!, _vpaMeta));
    } else if (isInserting) {
      context.missing(_vpaMeta);
    }
    if (data.containsKey('category_name')) {
      context.handle(
          _categoryNameMeta,
          categoryName.isAcceptableOrUnknown(
              data['category_name']!, _categoryNameMeta));
    } else if (isInserting) {
      context.missing(_categoryNameMeta);
    }
    if (data.containsKey('category_id')) {
      context.handle(
          _categoryIdMeta,
          categoryId.isAcceptableOrUnknown(
              data['category_id']!, _categoryIdMeta));
    } else if (isInserting) {
      context.missing(_categoryIdMeta);
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('auto_add')) {
      context.handle(_autoAddMeta,
          autoAdd.isAcceptableOrUnknown(data['auto_add']!, _autoAddMeta));
    }
    if (data.containsKey('bank_name')) {
      context.handle(_bankNameMeta,
          bankName.isAcceptableOrUnknown(data['bank_name']!, _bankNameMeta));
    }
    if (data.containsKey('account_number')) {
      context.handle(
          _accountNumberMeta,
          accountNumber.isAcceptableOrUnknown(
              data['account_number']!, _accountNumberMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  VpaMappingData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return VpaMappingData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      vpa: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}vpa'])!,
      categoryName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category_name'])!,
      categoryId: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}category_id'])!,
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      autoAdd: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}auto_add'])!,
      bankName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bank_name']),
      accountNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_number']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $VpaMappingTable createAlias(String alias) {
    return $VpaMappingTable(attachedDatabase, alias);
  }
}

class VpaMappingData extends DataClass implements Insertable<VpaMappingData> {
  final int id;
  final String vpa;
  final String categoryName;
  final int categoryId;
  final String? description;
  final bool autoAdd;
  final String? bankName;
  final String? accountNumber;
  final DateTime createdAt;
  const VpaMappingData(
      {required this.id,
      required this.vpa,
      required this.categoryName,
      required this.categoryId,
      this.description,
      required this.autoAdd,
      this.bankName,
      this.accountNumber,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['vpa'] = Variable<String>(vpa);
    map['category_name'] = Variable<String>(categoryName);
    map['category_id'] = Variable<int>(categoryId);
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['auto_add'] = Variable<bool>(autoAdd);
    if (!nullToAbsent || bankName != null) {
      map['bank_name'] = Variable<String>(bankName);
    }
    if (!nullToAbsent || accountNumber != null) {
      map['account_number'] = Variable<String>(accountNumber);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  VpaMappingCompanion toCompanion(bool nullToAbsent) {
    return VpaMappingCompanion(
      id: Value(id),
      vpa: Value(vpa),
      categoryName: Value(categoryName),
      categoryId: Value(categoryId),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      autoAdd: Value(autoAdd),
      bankName: bankName == null && nullToAbsent
          ? const Value.absent()
          : Value(bankName),
      accountNumber: accountNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(accountNumber),
      createdAt: Value(createdAt),
    );
  }

  factory VpaMappingData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return VpaMappingData(
      id: serializer.fromJson<int>(json['id']),
      vpa: serializer.fromJson<String>(json['vpa']),
      categoryName: serializer.fromJson<String>(json['categoryName']),
      categoryId: serializer.fromJson<int>(json['categoryId']),
      description: serializer.fromJson<String?>(json['description']),
      autoAdd: serializer.fromJson<bool>(json['autoAdd']),
      bankName: serializer.fromJson<String?>(json['bankName']),
      accountNumber: serializer.fromJson<String?>(json['accountNumber']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'vpa': serializer.toJson<String>(vpa),
      'categoryName': serializer.toJson<String>(categoryName),
      'categoryId': serializer.toJson<int>(categoryId),
      'description': serializer.toJson<String?>(description),
      'autoAdd': serializer.toJson<bool>(autoAdd),
      'bankName': serializer.toJson<String?>(bankName),
      'accountNumber': serializer.toJson<String?>(accountNumber),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  VpaMappingData copyWith(
          {int? id,
          String? vpa,
          String? categoryName,
          int? categoryId,
          Value<String?> description = const Value.absent(),
          bool? autoAdd,
          Value<String?> bankName = const Value.absent(),
          Value<String?> accountNumber = const Value.absent(),
          DateTime? createdAt}) =>
      VpaMappingData(
        id: id ?? this.id,
        vpa: vpa ?? this.vpa,
        categoryName: categoryName ?? this.categoryName,
        categoryId: categoryId ?? this.categoryId,
        description: description.present ? description.value : this.description,
        autoAdd: autoAdd ?? this.autoAdd,
        bankName: bankName.present ? bankName.value : this.bankName,
        accountNumber:
            accountNumber.present ? accountNumber.value : this.accountNumber,
        createdAt: createdAt ?? this.createdAt,
      );
  VpaMappingData copyWithCompanion(VpaMappingCompanion data) {
    return VpaMappingData(
      id: data.id.present ? data.id.value : this.id,
      vpa: data.vpa.present ? data.vpa.value : this.vpa,
      categoryName: data.categoryName.present
          ? data.categoryName.value
          : this.categoryName,
      categoryId:
          data.categoryId.present ? data.categoryId.value : this.categoryId,
      description:
          data.description.present ? data.description.value : this.description,
      autoAdd: data.autoAdd.present ? data.autoAdd.value : this.autoAdd,
      bankName: data.bankName.present ? data.bankName.value : this.bankName,
      accountNumber: data.accountNumber.present
          ? data.accountNumber.value
          : this.accountNumber,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('VpaMappingData(')
          ..write('id: $id, ')
          ..write('vpa: $vpa, ')
          ..write('categoryName: $categoryName, ')
          ..write('categoryId: $categoryId, ')
          ..write('description: $description, ')
          ..write('autoAdd: $autoAdd, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, vpa, categoryName, categoryId,
      description, autoAdd, bankName, accountNumber, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is VpaMappingData &&
          other.id == this.id &&
          other.vpa == this.vpa &&
          other.categoryName == this.categoryName &&
          other.categoryId == this.categoryId &&
          other.description == this.description &&
          other.autoAdd == this.autoAdd &&
          other.bankName == this.bankName &&
          other.accountNumber == this.accountNumber &&
          other.createdAt == this.createdAt);
}

class VpaMappingCompanion extends UpdateCompanion<VpaMappingData> {
  final Value<int> id;
  final Value<String> vpa;
  final Value<String> categoryName;
  final Value<int> categoryId;
  final Value<String?> description;
  final Value<bool> autoAdd;
  final Value<String?> bankName;
  final Value<String?> accountNumber;
  final Value<DateTime> createdAt;
  const VpaMappingCompanion({
    this.id = const Value.absent(),
    this.vpa = const Value.absent(),
    this.categoryName = const Value.absent(),
    this.categoryId = const Value.absent(),
    this.description = const Value.absent(),
    this.autoAdd = const Value.absent(),
    this.bankName = const Value.absent(),
    this.accountNumber = const Value.absent(),
    this.createdAt = const Value.absent(),
  });
  VpaMappingCompanion.insert({
    this.id = const Value.absent(),
    required String vpa,
    required String categoryName,
    required int categoryId,
    this.description = const Value.absent(),
    this.autoAdd = const Value.absent(),
    this.bankName = const Value.absent(),
    this.accountNumber = const Value.absent(),
    required DateTime createdAt,
  })  : vpa = Value(vpa),
        categoryName = Value(categoryName),
        categoryId = Value(categoryId),
        createdAt = Value(createdAt);
  static Insertable<VpaMappingData> custom({
    Expression<int>? id,
    Expression<String>? vpa,
    Expression<String>? categoryName,
    Expression<int>? categoryId,
    Expression<String>? description,
    Expression<bool>? autoAdd,
    Expression<String>? bankName,
    Expression<String>? accountNumber,
    Expression<DateTime>? createdAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (vpa != null) 'vpa': vpa,
      if (categoryName != null) 'category_name': categoryName,
      if (categoryId != null) 'category_id': categoryId,
      if (description != null) 'description': description,
      if (autoAdd != null) 'auto_add': autoAdd,
      if (bankName != null) 'bank_name': bankName,
      if (accountNumber != null) 'account_number': accountNumber,
      if (createdAt != null) 'created_at': createdAt,
    });
  }

  VpaMappingCompanion copyWith(
      {Value<int>? id,
      Value<String>? vpa,
      Value<String>? categoryName,
      Value<int>? categoryId,
      Value<String?>? description,
      Value<bool>? autoAdd,
      Value<String?>? bankName,
      Value<String?>? accountNumber,
      Value<DateTime>? createdAt}) {
    return VpaMappingCompanion(
      id: id ?? this.id,
      vpa: vpa ?? this.vpa,
      categoryName: categoryName ?? this.categoryName,
      categoryId: categoryId ?? this.categoryId,
      description: description ?? this.description,
      autoAdd: autoAdd ?? this.autoAdd,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (vpa.present) {
      map['vpa'] = Variable<String>(vpa.value);
    }
    if (categoryName.present) {
      map['category_name'] = Variable<String>(categoryName.value);
    }
    if (categoryId.present) {
      map['category_id'] = Variable<int>(categoryId.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (autoAdd.present) {
      map['auto_add'] = Variable<bool>(autoAdd.value);
    }
    if (bankName.present) {
      map['bank_name'] = Variable<String>(bankName.value);
    }
    if (accountNumber.present) {
      map['account_number'] = Variable<String>(accountNumber.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('VpaMappingCompanion(')
          ..write('id: $id, ')
          ..write('vpa: $vpa, ')
          ..write('categoryName: $categoryName, ')
          ..write('categoryId: $categoryId, ')
          ..write('description: $description, ')
          ..write('autoAdd: $autoAdd, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }
}

class $BankAccountTable extends BankAccount
    with TableInfo<$BankAccountTable, BankAccountData> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $BankAccountTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _bankNameMeta =
      const VerificationMeta('bankName');
  @override
  late final GeneratedColumn<String> bankName = GeneratedColumn<String>(
      'bank_name', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _accountNumberMeta =
      const VerificationMeta('accountNumber');
  @override
  late final GeneratedColumn<String> accountNumber = GeneratedColumn<String>(
      'account_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _accountTypeMeta =
      const VerificationMeta('accountType');
  @override
  late final GeneratedColumn<String> accountType = GeneratedColumn<String>(
      'account_type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _balanceMeta =
      const VerificationMeta('balance');
  @override
  late final GeneratedColumn<double> balance = GeneratedColumn<double>(
      'balance', aliasedName, false,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      defaultValue: const Constant(0));
  static const VerificationMeta _lastFourDigitsMeta =
      const VerificationMeta('lastFourDigits');
  @override
  late final GeneratedColumn<String> lastFourDigits = GeneratedColumn<String>(
      'last_four_digits', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        bankName,
        accountNumber,
        accountType,
        balance,
        lastFourDigits,
        isActive,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'bank_account';
  @override
  VerificationContext validateIntegrity(Insertable<BankAccountData> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('bank_name')) {
      context.handle(_bankNameMeta,
          bankName.isAcceptableOrUnknown(data['bank_name']!, _bankNameMeta));
    } else if (isInserting) {
      context.missing(_bankNameMeta);
    }
    if (data.containsKey('account_number')) {
      context.handle(
          _accountNumberMeta,
          accountNumber.isAcceptableOrUnknown(
              data['account_number']!, _accountNumberMeta));
    }
    if (data.containsKey('account_type')) {
      context.handle(
          _accountTypeMeta,
          accountType.isAcceptableOrUnknown(
              data['account_type']!, _accountTypeMeta));
    } else if (isInserting) {
      context.missing(_accountTypeMeta);
    }
    if (data.containsKey('balance')) {
      context.handle(_balanceMeta,
          balance.isAcceptableOrUnknown(data['balance']!, _balanceMeta));
    }
    if (data.containsKey('last_four_digits')) {
      context.handle(
          _lastFourDigitsMeta,
          lastFourDigits.isAcceptableOrUnknown(
              data['last_four_digits']!, _lastFourDigitsMeta));
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  BankAccountData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return BankAccountData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      bankName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bank_name'])!,
      accountNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_number']),
      accountType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}account_type'])!,
      balance: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}balance'])!,
      lastFourDigits: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}last_four_digits']),
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $BankAccountTable createAlias(String alias) {
    return $BankAccountTable(attachedDatabase, alias);
  }
}

class BankAccountData extends DataClass implements Insertable<BankAccountData> {
  final int id;
  final String bankName;
  final String? accountNumber;
  final String accountType;
  final double balance;
  final String? lastFourDigits;
  final bool isActive;
  final DateTime createdAt;
  const BankAccountData(
      {required this.id,
      required this.bankName,
      this.accountNumber,
      required this.accountType,
      required this.balance,
      this.lastFourDigits,
      required this.isActive,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    map['bank_name'] = Variable<String>(bankName);
    if (!nullToAbsent || accountNumber != null) {
      map['account_number'] = Variable<String>(accountNumber);
    }
    map['account_type'] = Variable<String>(accountType);
    map['balance'] = Variable<double>(balance);
    if (!nullToAbsent || lastFourDigits != null) {
      map['last_four_digits'] = Variable<String>(lastFourDigits);
    }
    map['is_active'] = Variable<bool>(isActive);
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  BankAccountCompanion toCompanion(bool nullToAbsent) {
    return BankAccountCompanion(
      id: Value(id),
      bankName: Value(bankName),
      accountNumber: accountNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(accountNumber),
      accountType: Value(accountType),
      balance: Value(balance),
      lastFourDigits: lastFourDigits == null && nullToAbsent
          ? const Value.absent()
          : Value(lastFourDigits),
      isActive: Value(isActive),
      createdAt: Value(createdAt),
    );
  }

  factory BankAccountData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return BankAccountData(
      id: serializer.fromJson<int>(json['id']),
      bankName: serializer.fromJson<String>(json['bankName']),
      accountNumber: serializer.fromJson<String?>(json['accountNumber']),
      accountType: serializer.fromJson<String>(json['accountType']),
      balance: serializer.fromJson<double>(json['balance']),
      lastFourDigits: serializer.fromJson<String?>(json['lastFourDigits']),
      isActive: serializer.fromJson<bool>(json['isActive']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'bankName': serializer.toJson<String>(bankName),
      'accountNumber': serializer.toJson<String?>(accountNumber),
      'accountType': serializer.toJson<String>(accountType),
      'balance': serializer.toJson<double>(balance),
      'lastFourDigits': serializer.toJson<String?>(lastFourDigits),
      'isActive': serializer.toJson<bool>(isActive),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  BankAccountData copyWith(
          {int? id,
          String? bankName,
          Value<String?> accountNumber = const Value.absent(),
          String? accountType,
          double? balance,
          Value<String?> lastFourDigits = const Value.absent(),
          bool? isActive,
          DateTime? createdAt}) =>
      BankAccountData(
        id: id ?? this.id,
        bankName: bankName ?? this.bankName,
        accountNumber:
            accountNumber.present ? accountNumber.value : this.accountNumber,
        accountType: accountType ?? this.accountType,
        balance: balance ?? this.balance,
        lastFourDigits:
            lastFourDigits.present ? lastFourDigits.value : this.lastFourDigits,
        isActive: isActive ?? this.isActive,
        createdAt: createdAt ?? this.createdAt,
      );
  BankAccountData copyWithCompanion(BankAccountCompanion data) {
    return BankAccountData(
      id: data.id.present ? data.id.value : this.id,
      bankName: data.bankName.present ? data.bankName.value : this.bankName,
      accountNumber: data.accountNumber.present
          ? data.accountNumber.value
          : this.accountNumber,
      accountType:
          data.accountType.present ? data.accountType.value : this.accountType,
      balance: data.balance.present ? data.balance.value : this.balance,
      lastFourDigits: data.lastFourDigits.present
          ? data.lastFourDigits.value
          : this.lastFourDigits,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('BankAccountData(')
          ..write('id: $id, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('accountType: $accountType, ')
          ..write('balance: $balance, ')
          ..write('lastFourDigits: $lastFourDigits, ')
          ..write('isActive: $isActive, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, bankName, accountNumber, accountType,
      balance, lastFourDigits, isActive, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is BankAccountData &&
          other.id == this.id &&
          other.bankName == this.bankName &&
          other.accountNumber == this.accountNumber &&
          other.accountType == this.accountType &&
          other.balance == this.balance &&
          other.lastFourDigits == this.lastFourDigits &&
          other.isActive == this.isActive &&
          other.createdAt == this.createdAt);
}

class BankAccountCompanion extends UpdateCompanion<BankAccountData> {
  final Value<int> id;
  final Value<String> bankName;
  final Value<String?> accountNumber;
  final Value<String> accountType;
  final Value<double> balance;
  final Value<String?> lastFourDigits;
  final Value<bool> isActive;
  final Value<DateTime> createdAt;
  const BankAccountCompanion({
    this.id = const Value.absent(),
    this.bankName = const Value.absent(),
    this.accountNumber = const Value.absent(),
    this.accountType = const Value.absent(),
    this.balance = const Value.absent(),
    this.lastFourDigits = const Value.absent(),
    this.isActive = const Value.absent(),
    this.createdAt = const Value.absent(),
  });
  BankAccountCompanion.insert({
    this.id = const Value.absent(),
    required String bankName,
    this.accountNumber = const Value.absent(),
    required String accountType,
    this.balance = const Value.absent(),
    this.lastFourDigits = const Value.absent(),
    this.isActive = const Value.absent(),
    required DateTime createdAt,
  })  : bankName = Value(bankName),
        accountType = Value(accountType),
        createdAt = Value(createdAt);
  static Insertable<BankAccountData> custom({
    Expression<int>? id,
    Expression<String>? bankName,
    Expression<String>? accountNumber,
    Expression<String>? accountType,
    Expression<double>? balance,
    Expression<String>? lastFourDigits,
    Expression<bool>? isActive,
    Expression<DateTime>? createdAt,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (bankName != null) 'bank_name': bankName,
      if (accountNumber != null) 'account_number': accountNumber,
      if (accountType != null) 'account_type': accountType,
      if (balance != null) 'balance': balance,
      if (lastFourDigits != null) 'last_four_digits': lastFourDigits,
      if (isActive != null) 'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt,
    });
  }

  BankAccountCompanion copyWith(
      {Value<int>? id,
      Value<String>? bankName,
      Value<String?>? accountNumber,
      Value<String>? accountType,
      Value<double>? balance,
      Value<String?>? lastFourDigits,
      Value<bool>? isActive,
      Value<DateTime>? createdAt}) {
    return BankAccountCompanion(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountType: accountType ?? this.accountType,
      balance: balance ?? this.balance,
      lastFourDigits: lastFourDigits ?? this.lastFourDigits,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (bankName.present) {
      map['bank_name'] = Variable<String>(bankName.value);
    }
    if (accountNumber.present) {
      map['account_number'] = Variable<String>(accountNumber.value);
    }
    if (accountType.present) {
      map['account_type'] = Variable<String>(accountType.value);
    }
    if (balance.present) {
      map['balance'] = Variable<double>(balance.value);
    }
    if (lastFourDigits.present) {
      map['last_four_digits'] = Variable<String>(lastFourDigits.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('BankAccountCompanion(')
          ..write('id: $id, ')
          ..write('bankName: $bankName, ')
          ..write('accountNumber: $accountNumber, ')
          ..write('accountType: $accountType, ')
          ..write('balance: $balance, ')
          ..write('lastFourDigits: $lastFourDigits, ')
          ..write('isActive: $isActive, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $SmsTransactionTable smsTransaction = $SmsTransactionTable(this);
  late final $VpaMappingTable vpaMapping = $VpaMappingTable(this);
  late final $BankAccountTable bankAccount = $BankAccountTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [smsTransaction, vpaMapping, bankAccount];
}

typedef $$SmsTransactionTableCreateCompanionBuilder = SmsTransactionCompanion
    Function({
  Value<int> id,
  required String message,
  required double amount,
  Value<String?> vpa,
  required String bankName,
  Value<String?> accountNumber,
  required DateTime transactionDate,
  required String transactionType,
  Value<String?> refNumber,
  Value<bool> isProcessed,
  Value<int?> linkedExpenseId,
  required DateTime createdAt,
});
typedef $$SmsTransactionTableUpdateCompanionBuilder = SmsTransactionCompanion
    Function({
  Value<int> id,
  Value<String> message,
  Value<double> amount,
  Value<String?> vpa,
  Value<String> bankName,
  Value<String?> accountNumber,
  Value<DateTime> transactionDate,
  Value<String> transactionType,
  Value<String?> refNumber,
  Value<bool> isProcessed,
  Value<int?> linkedExpenseId,
  Value<DateTime> createdAt,
});

class $$SmsTransactionTableFilterComposer
    extends Composer<_$AppDatabase, $SmsTransactionTable> {
  $$SmsTransactionTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get vpa => $composableBuilder(
      column: $table.vpa, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get transactionDate => $composableBuilder(
      column: $table.transactionDate,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get transactionType => $composableBuilder(
      column: $table.transactionType,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get refNumber => $composableBuilder(
      column: $table.refNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isProcessed => $composableBuilder(
      column: $table.isProcessed, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get linkedExpenseId => $composableBuilder(
      column: $table.linkedExpenseId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$SmsTransactionTableOrderingComposer
    extends Composer<_$AppDatabase, $SmsTransactionTable> {
  $$SmsTransactionTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get vpa => $composableBuilder(
      column: $table.vpa, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get transactionDate => $composableBuilder(
      column: $table.transactionDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get transactionType => $composableBuilder(
      column: $table.transactionType,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get refNumber => $composableBuilder(
      column: $table.refNumber, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isProcessed => $composableBuilder(
      column: $table.isProcessed, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get linkedExpenseId => $composableBuilder(
      column: $table.linkedExpenseId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$SmsTransactionTableAnnotationComposer
    extends Composer<_$AppDatabase, $SmsTransactionTable> {
  $$SmsTransactionTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get message =>
      $composableBuilder(column: $table.message, builder: (column) => column);

  GeneratedColumn<double> get amount =>
      $composableBuilder(column: $table.amount, builder: (column) => column);

  GeneratedColumn<String> get vpa =>
      $composableBuilder(column: $table.vpa, builder: (column) => column);

  GeneratedColumn<String> get bankName =>
      $composableBuilder(column: $table.bankName, builder: (column) => column);

  GeneratedColumn<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => column);

  GeneratedColumn<DateTime> get transactionDate => $composableBuilder(
      column: $table.transactionDate, builder: (column) => column);

  GeneratedColumn<String> get transactionType => $composableBuilder(
      column: $table.transactionType, builder: (column) => column);

  GeneratedColumn<String> get refNumber =>
      $composableBuilder(column: $table.refNumber, builder: (column) => column);

  GeneratedColumn<bool> get isProcessed => $composableBuilder(
      column: $table.isProcessed, builder: (column) => column);

  GeneratedColumn<int> get linkedExpenseId => $composableBuilder(
      column: $table.linkedExpenseId, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$SmsTransactionTableTableManager extends RootTableManager<
    _$AppDatabase,
    $SmsTransactionTable,
    SmsTransactionData,
    $$SmsTransactionTableFilterComposer,
    $$SmsTransactionTableOrderingComposer,
    $$SmsTransactionTableAnnotationComposer,
    $$SmsTransactionTableCreateCompanionBuilder,
    $$SmsTransactionTableUpdateCompanionBuilder,
    (
      SmsTransactionData,
      BaseReferences<_$AppDatabase, $SmsTransactionTable, SmsTransactionData>
    ),
    SmsTransactionData,
    PrefetchHooks Function()> {
  $$SmsTransactionTableTableManager(
      _$AppDatabase db, $SmsTransactionTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SmsTransactionTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SmsTransactionTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SmsTransactionTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> message = const Value.absent(),
            Value<double> amount = const Value.absent(),
            Value<String?> vpa = const Value.absent(),
            Value<String> bankName = const Value.absent(),
            Value<String?> accountNumber = const Value.absent(),
            Value<DateTime> transactionDate = const Value.absent(),
            Value<String> transactionType = const Value.absent(),
            Value<String?> refNumber = const Value.absent(),
            Value<bool> isProcessed = const Value.absent(),
            Value<int?> linkedExpenseId = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
          }) =>
              SmsTransactionCompanion(
            id: id,
            message: message,
            amount: amount,
            vpa: vpa,
            bankName: bankName,
            accountNumber: accountNumber,
            transactionDate: transactionDate,
            transactionType: transactionType,
            refNumber: refNumber,
            isProcessed: isProcessed,
            linkedExpenseId: linkedExpenseId,
            createdAt: createdAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String message,
            required double amount,
            Value<String?> vpa = const Value.absent(),
            required String bankName,
            Value<String?> accountNumber = const Value.absent(),
            required DateTime transactionDate,
            required String transactionType,
            Value<String?> refNumber = const Value.absent(),
            Value<bool> isProcessed = const Value.absent(),
            Value<int?> linkedExpenseId = const Value.absent(),
            required DateTime createdAt,
          }) =>
              SmsTransactionCompanion.insert(
            id: id,
            message: message,
            amount: amount,
            vpa: vpa,
            bankName: bankName,
            accountNumber: accountNumber,
            transactionDate: transactionDate,
            transactionType: transactionType,
            refNumber: refNumber,
            isProcessed: isProcessed,
            linkedExpenseId: linkedExpenseId,
            createdAt: createdAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$SmsTransactionTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $SmsTransactionTable,
    SmsTransactionData,
    $$SmsTransactionTableFilterComposer,
    $$SmsTransactionTableOrderingComposer,
    $$SmsTransactionTableAnnotationComposer,
    $$SmsTransactionTableCreateCompanionBuilder,
    $$SmsTransactionTableUpdateCompanionBuilder,
    (
      SmsTransactionData,
      BaseReferences<_$AppDatabase, $SmsTransactionTable, SmsTransactionData>
    ),
    SmsTransactionData,
    PrefetchHooks Function()>;
typedef $$VpaMappingTableCreateCompanionBuilder = VpaMappingCompanion Function({
  Value<int> id,
  required String vpa,
  required String categoryName,
  required int categoryId,
  Value<String?> description,
  Value<bool> autoAdd,
  Value<String?> bankName,
  Value<String?> accountNumber,
  required DateTime createdAt,
});
typedef $$VpaMappingTableUpdateCompanionBuilder = VpaMappingCompanion Function({
  Value<int> id,
  Value<String> vpa,
  Value<String> categoryName,
  Value<int> categoryId,
  Value<String?> description,
  Value<bool> autoAdd,
  Value<String?> bankName,
  Value<String?> accountNumber,
  Value<DateTime> createdAt,
});

class $$VpaMappingTableFilterComposer
    extends Composer<_$AppDatabase, $VpaMappingTable> {
  $$VpaMappingTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get vpa => $composableBuilder(
      column: $table.vpa, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get categoryName => $composableBuilder(
      column: $table.categoryName, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get categoryId => $composableBuilder(
      column: $table.categoryId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get autoAdd => $composableBuilder(
      column: $table.autoAdd, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$VpaMappingTableOrderingComposer
    extends Composer<_$AppDatabase, $VpaMappingTable> {
  $$VpaMappingTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get vpa => $composableBuilder(
      column: $table.vpa, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get categoryName => $composableBuilder(
      column: $table.categoryName,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get categoryId => $composableBuilder(
      column: $table.categoryId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get autoAdd => $composableBuilder(
      column: $table.autoAdd, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$VpaMappingTableAnnotationComposer
    extends Composer<_$AppDatabase, $VpaMappingTable> {
  $$VpaMappingTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get vpa =>
      $composableBuilder(column: $table.vpa, builder: (column) => column);

  GeneratedColumn<String> get categoryName => $composableBuilder(
      column: $table.categoryName, builder: (column) => column);

  GeneratedColumn<int> get categoryId => $composableBuilder(
      column: $table.categoryId, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<bool> get autoAdd =>
      $composableBuilder(column: $table.autoAdd, builder: (column) => column);

  GeneratedColumn<String> get bankName =>
      $composableBuilder(column: $table.bankName, builder: (column) => column);

  GeneratedColumn<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$VpaMappingTableTableManager extends RootTableManager<
    _$AppDatabase,
    $VpaMappingTable,
    VpaMappingData,
    $$VpaMappingTableFilterComposer,
    $$VpaMappingTableOrderingComposer,
    $$VpaMappingTableAnnotationComposer,
    $$VpaMappingTableCreateCompanionBuilder,
    $$VpaMappingTableUpdateCompanionBuilder,
    (
      VpaMappingData,
      BaseReferences<_$AppDatabase, $VpaMappingTable, VpaMappingData>
    ),
    VpaMappingData,
    PrefetchHooks Function()> {
  $$VpaMappingTableTableManager(_$AppDatabase db, $VpaMappingTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$VpaMappingTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$VpaMappingTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$VpaMappingTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> vpa = const Value.absent(),
            Value<String> categoryName = const Value.absent(),
            Value<int> categoryId = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<bool> autoAdd = const Value.absent(),
            Value<String?> bankName = const Value.absent(),
            Value<String?> accountNumber = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
          }) =>
              VpaMappingCompanion(
            id: id,
            vpa: vpa,
            categoryName: categoryName,
            categoryId: categoryId,
            description: description,
            autoAdd: autoAdd,
            bankName: bankName,
            accountNumber: accountNumber,
            createdAt: createdAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String vpa,
            required String categoryName,
            required int categoryId,
            Value<String?> description = const Value.absent(),
            Value<bool> autoAdd = const Value.absent(),
            Value<String?> bankName = const Value.absent(),
            Value<String?> accountNumber = const Value.absent(),
            required DateTime createdAt,
          }) =>
              VpaMappingCompanion.insert(
            id: id,
            vpa: vpa,
            categoryName: categoryName,
            categoryId: categoryId,
            description: description,
            autoAdd: autoAdd,
            bankName: bankName,
            accountNumber: accountNumber,
            createdAt: createdAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$VpaMappingTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $VpaMappingTable,
    VpaMappingData,
    $$VpaMappingTableFilterComposer,
    $$VpaMappingTableOrderingComposer,
    $$VpaMappingTableAnnotationComposer,
    $$VpaMappingTableCreateCompanionBuilder,
    $$VpaMappingTableUpdateCompanionBuilder,
    (
      VpaMappingData,
      BaseReferences<_$AppDatabase, $VpaMappingTable, VpaMappingData>
    ),
    VpaMappingData,
    PrefetchHooks Function()>;
typedef $$BankAccountTableCreateCompanionBuilder = BankAccountCompanion
    Function({
  Value<int> id,
  required String bankName,
  Value<String?> accountNumber,
  required String accountType,
  Value<double> balance,
  Value<String?> lastFourDigits,
  Value<bool> isActive,
  required DateTime createdAt,
});
typedef $$BankAccountTableUpdateCompanionBuilder = BankAccountCompanion
    Function({
  Value<int> id,
  Value<String> bankName,
  Value<String?> accountNumber,
  Value<String> accountType,
  Value<double> balance,
  Value<String?> lastFourDigits,
  Value<bool> isActive,
  Value<DateTime> createdAt,
});

class $$BankAccountTableFilterComposer
    extends Composer<_$AppDatabase, $BankAccountTable> {
  $$BankAccountTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get accountType => $composableBuilder(
      column: $table.accountType, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get lastFourDigits => $composableBuilder(
      column: $table.lastFourDigits,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$BankAccountTableOrderingComposer
    extends Composer<_$AppDatabase, $BankAccountTable> {
  $$BankAccountTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bankName => $composableBuilder(
      column: $table.bankName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get accountType => $composableBuilder(
      column: $table.accountType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get balance => $composableBuilder(
      column: $table.balance, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lastFourDigits => $composableBuilder(
      column: $table.lastFourDigits,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$BankAccountTableAnnotationComposer
    extends Composer<_$AppDatabase, $BankAccountTable> {
  $$BankAccountTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get bankName =>
      $composableBuilder(column: $table.bankName, builder: (column) => column);

  GeneratedColumn<String> get accountNumber => $composableBuilder(
      column: $table.accountNumber, builder: (column) => column);

  GeneratedColumn<String> get accountType => $composableBuilder(
      column: $table.accountType, builder: (column) => column);

  GeneratedColumn<double> get balance =>
      $composableBuilder(column: $table.balance, builder: (column) => column);

  GeneratedColumn<String> get lastFourDigits => $composableBuilder(
      column: $table.lastFourDigits, builder: (column) => column);

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$BankAccountTableTableManager extends RootTableManager<
    _$AppDatabase,
    $BankAccountTable,
    BankAccountData,
    $$BankAccountTableFilterComposer,
    $$BankAccountTableOrderingComposer,
    $$BankAccountTableAnnotationComposer,
    $$BankAccountTableCreateCompanionBuilder,
    $$BankAccountTableUpdateCompanionBuilder,
    (
      BankAccountData,
      BaseReferences<_$AppDatabase, $BankAccountTable, BankAccountData>
    ),
    BankAccountData,
    PrefetchHooks Function()> {
  $$BankAccountTableTableManager(_$AppDatabase db, $BankAccountTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$BankAccountTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$BankAccountTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$BankAccountTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String> bankName = const Value.absent(),
            Value<String?> accountNumber = const Value.absent(),
            Value<String> accountType = const Value.absent(),
            Value<double> balance = const Value.absent(),
            Value<String?> lastFourDigits = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
          }) =>
              BankAccountCompanion(
            id: id,
            bankName: bankName,
            accountNumber: accountNumber,
            accountType: accountType,
            balance: balance,
            lastFourDigits: lastFourDigits,
            isActive: isActive,
            createdAt: createdAt,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            required String bankName,
            Value<String?> accountNumber = const Value.absent(),
            required String accountType,
            Value<double> balance = const Value.absent(),
            Value<String?> lastFourDigits = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            required DateTime createdAt,
          }) =>
              BankAccountCompanion.insert(
            id: id,
            bankName: bankName,
            accountNumber: accountNumber,
            accountType: accountType,
            balance: balance,
            lastFourDigits: lastFourDigits,
            isActive: isActive,
            createdAt: createdAt,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$BankAccountTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $BankAccountTable,
    BankAccountData,
    $$BankAccountTableFilterComposer,
    $$BankAccountTableOrderingComposer,
    $$BankAccountTableAnnotationComposer,
    $$BankAccountTableCreateCompanionBuilder,
    $$BankAccountTableUpdateCompanionBuilder,
    (
      BankAccountData,
      BaseReferences<_$AppDatabase, $BankAccountTable, BankAccountData>
    ),
    BankAccountData,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$SmsTransactionTableTableManager get smsTransaction =>
      $$SmsTransactionTableTableManager(_db, _db.smsTransaction);
  $$VpaMappingTableTableManager get vpaMapping =>
      $$VpaMappingTableTableManager(_db, _db.vpaMapping);
  $$BankAccountTableTableManager get bankAccount =>
      $$BankAccountTableTableManager(_db, _db.bankAccount);
}
