import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'app_database.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'dart:io';

final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase(
    LazyDatabase(() async {
      final dbFolder = await getApplicationDocumentsDirectory();
      final file = File(p.join(dbFolder.path, 'financial_app.db'));
      return NativeDatabase(file);
    }),
  );
}); 