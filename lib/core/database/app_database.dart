import 'package:drift/drift.dart';
import 'package:drift/drift.dart' as drift;

part 'app_database.g.dart';

@DriftDatabase(
  tables: [
    Categories, BankAccounts, Budgets, DailyLimits, Allocations, Savings, Debts, CreditCards, CreditCardTransactions, CreditCardPayments, EmiSchedules, Investments, StockPrices, CurrencySettings, Transactions, RecurringIncomes
  ],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase(super.e);

  @override
  int get schemaVersion => 9;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onUpgrade: (m, from, to) async {
      if (from < 5) {
        await m.createTable(transactions);
      }
      if (from < 6) {
        // Add new columns for transaction-based balance system
        await m.addColumn(bankAccounts, bankAccounts.openingBalance);
        await m.addColumn(bankAccounts, bankAccounts.currentBalance);
      }
      if (from < 7) {
        // Add contact columns to transactions table
        await m.addColumn(transactions, transactions.contactName);
        await m.addColumn(transactions, transactions.contactPhone);
      }
      if (from < 8) {
        // Add status column to transactions table
        await m.addColumn(transactions, transactions.status);
      }
      if (from < 9) {
        // Add RecurringIncomes table
        await m.createTable(recurringIncomes);
      }
    },
    onCreate: (m) async {
      await m.createAll();
    },
  );

  Future<void> seedCategories() async {
    final existing = await select(categories).get();
    if (existing.isEmpty) {
      await batch((batch) {
        batch.insertAll(categories, [
          CategoriesCompanion.insert(
            name: 'Food & Dining',
            type: const Value('Expense'),
            icon: const Value('restaurant'),
            color: const Value('#FF6B6B'),
          ),
          CategoriesCompanion.insert(
            name: 'Transportation',
            type: const Value('Expense'),
            icon: const Value('directions_car'),
            color: const Value('#4ECDC4'),
          ),
          CategoriesCompanion.insert(
            name: 'Shopping',
            type: const Value('Expense'),
            icon: const Value('shopping_bag'),
            color: const Value('#45B7D1'),
          ),
          CategoriesCompanion.insert(
            name: 'Entertainment',
            type: const Value('Expense'),
            icon: const Value('movie'),
            color: const Value('#96CEB4'),
          ),
          CategoriesCompanion.insert(
            name: 'Healthcare',
            type: const Value('Expense'),
            icon: const Value('local_hospital'),
            color: const Value('#FFEAA7'),
          ),
          CategoriesCompanion.insert(
            name: 'Salary',
            type: const Value('Income'),
            icon: const Value('account_balance_wallet'),
            color: const Value('#DDA0DD'),
          ),
          CategoriesCompanion.insert(
            name: 'Freelance',
            type: const Value('Income'),
            icon: const Value('work'),
            color: const Value('#98D8C8'),
          ),
          CategoriesCompanion.insert(
            name: 'Investment',
            type: const Value('Income'),
            icon: const Value('trending_up'),
            color: const Value('#F7DC6F'),
          ),
        ]);
      });
    }
  }

  Future<void> deleteAllData() async {
    await batch((batch) {
      batch.deleteAll(stockPrices);
      batch.deleteAll(investments);
      batch.deleteAll(emiSchedules);
      batch.deleteAll(creditCardTransactions);
      batch.deleteAll(creditCards);
      batch.deleteAll(debts);
      batch.deleteAll(savings);
      batch.deleteAll(allocations);
      batch.deleteAll(dailyLimits);
      batch.deleteAll(budgets);
      batch.deleteAll(transactions);
      batch.deleteAll(bankAccounts);
      batch.deleteAll(categories);
      batch.deleteAll(recurringIncomes);
    });
  }

  Future<void> ensureCashAccountExists() async {
    final cashAccount = await (select(bankAccounts)..where((tbl) => tbl.bankName.equals('Cash'))).getSingleOrNull();
    if (cashAccount == null) {
      into(bankAccounts).insert(BankAccountsCompanion(
        bankName: Value('Cash'),
        accountType: Value('Cash'),
        openingBalance: Value(0.0),
        currentBalance: Value(0.0),
        createdAt: Value(DateTime.now()),
        lastUpdated: Value(DateTime.now()),
      ));
    }
  }

  Future<void> initializeDatabase() async {
    // Ensure cash account exists
    await ensureCashAccountExists();
    
    // Seed categories
    await seedCategories();
    
    print('DEBUG: Database initialization completed - Cash account and categories seeded');
  }

  Future<void> resetAndInitializeDatabase() async {
    // Clear all data
    await deleteAllData();
    
    // Reinitialize
    await initializeDatabase();
    
    print('DEBUG: Database reset and reinitialized');
  }
}

class Categories extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text().unique()();
  TextColumn get type => text().withDefault(const Constant('Expense'))();
  TextColumn get icon => text().withDefault(const Constant('category'))();
  TextColumn get color => text().withDefault(const Constant('#FF6B6B'))();
}

class BankAccounts extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get bankName => text()();
  TextColumn get accountNumber => text().nullable()();
  TextColumn get accountType => text()();
  RealColumn get openingBalance => real().withDefault(const Constant(0.0))();
  RealColumn get currentBalance => real().withDefault(const Constant(0.0))();
  TextColumn get lastFourDigits => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get lastUpdated => dateTime()();
}

class Budgets extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get month => integer()();
  IntColumn get year => integer()();
  IntColumn get categoryId => integer().references(Categories, #id)();
  RealColumn get budgetedAmount => real()();
  RealColumn get actualSpent => real()();
}

class DailyLimits extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get categoryId => integer().references(Categories, #id)();
  RealColumn get maxDailyAmount => real()();
}

class Allocations extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get month => integer()();
  IntColumn get year => integer()();
  TextColumn get category => text()();
  RealColumn get allocatedAmount => real()();
  RealColumn get actualSpent => real()();
}

class Savings extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get month => integer()();
  IntColumn get year => integer()();
  RealColumn get targetAmount => real()();
  RealColumn get actualSaved => real()();
  TextColumn get note => text().nullable()();
}

class Debts extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get type => text()(); // Personal, Gold, Car, Home, Business, Credit Card
  TextColumn get loanName => text()();
  BoolColumn get isLending => boolean()(); // true = lending money, false = borrowing
  TextColumn get partyName => text()();
  TextColumn get partyPhone => text().nullable()();
  TextColumn get partyEmail => text().nullable()();
  RealColumn get principalAmount => real()();
  RealColumn get currentBalance => real()();
  RealColumn get interestRate => real()();
  TextColumn get interestType => text()(); // Simple, Compound, Flat
  TextColumn get interestFrequency => text()(); // Monthly, Quarterly, Yearly
  DateTimeColumn get startDate => dateTime()();
  DateTimeColumn get dueDate => dateTime()();
  RealColumn get paidAmount => real()();
  RealColumn get foreclosureCharges => real().nullable()();
  RealColumn get processingFee => real().nullable()();
  RealColumn get gstAmount => real().nullable()();
  BoolColumn get isEmi => boolean().withDefault(const Constant(false))();
  IntColumn get emiMonths => integer().nullable()();
  RealColumn get monthlyEmi => real().nullable()();
  TextColumn get loanPurpose => text().nullable()();
  TextColumn get collateral => text().nullable()(); // For secured loans
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  IntColumn get linkedCreditCardId => integer().nullable().customConstraint('REFERENCES credit_cards(id)') as IntColumn;
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get lastUpdated => dateTime()();
}

class Transactions extends Table {
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get date => dateTime()();
  RealColumn get amount => real()();
  TextColumn get description => text().nullable()();
  TextColumn get type => text().withDefault(const Constant('expense'))(); // expense, income, debt, repayment, credit_given, credit_received, transfer
  IntColumn get fromAccountId => integer().references(BankAccounts, #id).nullable()();
  IntColumn get toAccountId => integer().references(BankAccounts, #id).nullable()();
  TextColumn get contactId => text().nullable()();
  TextColumn get contactName => text().nullable()();
  TextColumn get contactPhone => text().nullable()();
  IntColumn get categoryId => integer().references(Categories, #id).nullable()();
  TextColumn get note => text().nullable()();
  TextColumn get paymentMethod => text().nullable()(); // Cash, Bank, Credit Card, Credit from Other
  IntColumn get linkedDebtId => integer().nullable()();
  IntColumn get linkedCreditCardId => integer().nullable()();
  TextColumn get status => text().withDefault(const Constant('active'))(); // active, stopped
}

class CreditCards extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get bankName => text()();
  TextColumn get cardNumber => text()();
  TextColumn get lastFourDigits => text().nullable()();
  TextColumn get cardHolderName => text()();
  RealColumn get creditLimit => real()();
  RealColumn get availableCredit => real()();
  RealColumn get outstandingBalance => real()();
  RealColumn get interestRate => real()();
  RealColumn get annualFee => real()();
  DateTimeColumn get dueDate => dateTime()();
  DateTimeColumn get statementDate => dateTime()();
  BoolColumn get isActive => boolean().withDefault(const Constant(true))();
  TextColumn get cardType => text()(); // Visa, MasterCard, Amex, etc.
  TextColumn get cardColor => text()(); // For UI customization
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get lastUpdated => dateTime()();
}

class RecurringIncomes extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get name => text()();
  RealColumn get amount => real()();
  TextColumn get description => text().nullable()();
  IntColumn get categoryId => integer().references(Categories, #id)();
  IntColumn get toAccountId => integer().references(BankAccounts, #id)();
  TextColumn get frequency => text()(); // Weekly, Monthly, Quarterly, Yearly
  IntColumn get dayOfMonth => integer().nullable()(); // For monthly: 1-31
  IntColumn get dayOfWeek => integer().nullable()(); // For weekly: 1-7 (Monday=1, Sunday=7)
  IntColumn get weekOfMonth => integer().nullable()(); // For monthly: 1-5 (1st week, 2nd week, etc.)
  BoolColumn get isAutomatic => boolean().withDefault(const Constant(false))(); // true = auto add, false = manual approval
  DateTimeColumn get startDate => dateTime()();
  DateTimeColumn get endDate => dateTime().nullable()(); // null = no end date
  TextColumn get note => text().nullable()();
  BoolColumn get isRecurringActive => boolean().withDefault(const Constant(true))(); // renamed to avoid conflict
  DateTimeColumn get lastProcessedDate => dateTime().nullable()(); // Track when last processed
  DateTimeColumn get nextDueDate => dateTime()(); // Next date when this should be processed
  DateTimeColumn get recurringCreatedAt => dateTime()(); // renamed to avoid conflict
  DateTimeColumn get recurringUpdatedAt => dateTime()(); // renamed to avoid conflict
}

class CreditCardTransactions extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get cardId => integer().references(CreditCards, #id)();
  RealColumn get amount => real()();
  TextColumn get description => text()();
  TextColumn get category => text()();
  DateTimeColumn get transactionDate => dateTime()();
  BoolColumn get isEmi => boolean().withDefault(const Constant(false))();
  IntColumn get emiMonths => integer().nullable()();
  RealColumn get emiInterestRate => real().nullable()();
  RealColumn get processingFee => real().nullable()();
  RealColumn get gstAmount => real().nullable()();
  TextColumn get transactionType => text()(); // Purchase, Payment, Cash Advance, etc.
  BoolColumn get isPaid => boolean().withDefault(const Constant(false))();
  IntColumn get linkedExpenseId => integer().nullable()();
  TextColumn get merchantName => text().nullable()();
  TextColumn get location => text().nullable()();
}

class CreditCardPayments extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get cardId => integer().references(CreditCards, #id)();
  RealColumn get amount => real()();
  DateTimeColumn get paymentDate => dateTime()();
  IntColumn get fromAccountId => integer().references(BankAccounts, #id)();
  TextColumn get paymentMethod => text()(); // Bank Transfer, UPI, Cash, etc.
  TextColumn get referenceNumber => text().nullable()();
  TextColumn get notes => text().nullable()();
  BoolColumn get isProcessed => boolean().withDefault(const Constant(false))();
}

class EmiSchedules extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get debtId => integer().references(Debts, #id)();
  IntColumn get transactionId => integer().references(CreditCardTransactions, #id)();
  RealColumn get monthlyPayment => real()();
  IntColumn get emiNumber => integer()(); // 1, 2, 3, etc.
  IntColumn get totalEmis => integer()();
  IntColumn get remainingEmis => integer()();
  DateTimeColumn get dueDate => dateTime()();
  RealColumn get principalComponent => real()();
  RealColumn get interestComponent => real()();
  RealColumn get processingFee => real().nullable()();
  RealColumn get gstAmount => real().nullable()();
  BoolColumn get isPaid => boolean().withDefault(const Constant(false))();
  DateTimeColumn get paidDate => dateTime().nullable()();
  TextColumn get paymentMethod => text().nullable()();
  IntColumn get fromAccountId => integer().references(BankAccounts, #id)();
}

class Investments extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get ticker => text()();
  TextColumn get type => text()();
  RealColumn get quantity => real()();
  RealColumn get buyPrice => real()();
  DateTimeColumn get buyDate => dateTime()();
}

class StockPrices extends Table {
  TextColumn get ticker => text()();
  RealColumn get latestPrice => real()();
  DateTimeColumn get lastUpdated => dateTime()();
  @override
  Set<Column> get primaryKey => {ticker};
}

class CurrencySettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get currencyCode => text()(); // e.g., "INR", "USD", "EUR"
  TextColumn get currencySymbol => text()(); // e.g., "₹", "$", "€"
  TextColumn get currencyName => text()(); // e.g., "Indian Rupee", "US Dollar"
  RealColumn get exchangeRate => real().withDefault(const Constant(1.0))(); // Base rate for conversions
  BoolColumn get isDefault => boolean().withDefault(const Constant(false))();
  DateTimeColumn get lastUpdated => dateTime()();
} 