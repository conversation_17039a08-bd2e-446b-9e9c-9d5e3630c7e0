import 'dart:async';
import '../../application/usecases/get_due_recurring_incomes_usecase.dart';
import '../../application/usecases/process_recurring_income_usecase.dart';
import '../../core/database/app_database.dart';

class RecurringIncomeService {
  final GetDueRecurringIncomesUseCase _getDueRecurringIncomesUseCase;
  final ProcessRecurringIncomeUseCase _processRecurringIncomeUseCase;
  Timer? _dailyCheckTimer;

  RecurringIncomeService(this._getDueRecurringIncomesUseCase, this._processRecurringIncomeUseCase);

  /// Start the daily check for recurring incomes
  void startDailyCheck() {
    // Check immediately when service starts
    _checkForDueRecurringIncomes();
    
    // Set up daily timer (check every 24 hours)
    _dailyCheckTimer = Timer.periodic(const Duration(hours: 24), (timer) {
      _checkForDueRecurringIncomes();
    });
  }

  /// Stop the daily check
  void stopDailyCheck() {
    _dailyCheckTimer?.cancel();
    _dailyCheckTimer = null;
  }

  /// Check for due recurring incomes and process them
  Future<void> _checkForDueRecurringIncomes() async {
    try {
      final today = DateTime.now();
      print('RecurringIncomeService: Checking for due recurring incomes on ${today.toString()}');
      
      // Debug: Print all recurring incomes and their isAutomatic values
      final all = await _getDueRecurringIncomesUseCase.repository.getAllRecurringIncomes();
      for (final r in all) {
        print('REPO CHECK: ${r.name} isAutomatic: ${r.isAutomatic}');
      }

      final dueRecurringIncomes = await _getDueRecurringIncomesUseCase(today);
      
      print('RecurringIncomeService: Found ${dueRecurringIncomes.length} due recurring incomes');
      
      for (final recurringIncome in dueRecurringIncomes) {
        print('RecurringIncomeService: Processing recurring income: ${recurringIncome.name}, nextDueDate: ${recurringIncome.nextDueDate}, today: $today');
        await _processRecurringIncomeUseCase(recurringIncome, today);
        print('RecurringIncomeService: Processed recurring income: ${recurringIncome.name}');
      }
    } catch (e) {
      print('RecurringIncomeService: Error checking for due recurring incomes: $e');
    }
  }

  /// Manual check for due recurring incomes (called from UI)
  Future<void> checkForDueRecurringIncomes() async {
    await _checkForDueRecurringIncomes();
  }

  /// Dispose resources
  void dispose() {
    stopDailyCheck();
  }
} 