import 'package:flutter_riverpod/flutter_riverpod.dart';

class LoanCalculationService {
  
  // Calculate EMI for different loan types
  static double calculateEmi({
    required double principal,
    required double interestRate,
    required int months,
    required String interestType,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    
    switch (interestType.toLowerCase()) {
      case 'flat':
        return _calculateFlatRateEmi(principal, interestRate, months);
      case 'simple':
        return _calculateSimpleInterestEmi(principal, interestRate, months);
      case 'compound':
      default:
        return _calculateCompoundInterestEmi(principal, monthlyRate, months);
    }
  }

  // Flat Rate EMI (Common in personal loans)
  static double _calculateFlatRateEmi(double principal, double interestRate, int months) {
    final totalInterest = (principal * interestRate * months) / (12 * 100);
    final totalAmount = principal + totalInterest;
    return totalAmount / months;
  }

  // Simple Interest EMI
  static double _calculateSimpleInterestEmi(double principal, double interestRate, int months) {
    final totalInterest = (principal * interestRate * months) / (12 * 100);
    final totalAmount = principal + totalInterest;
    return totalAmount / months;
  }

  // Compound Interest EMI (Most common)
  static double _calculateCompoundInterestEmi(double principal, double monthlyRate, int months) {
    if (monthlyRate == 0) return principal / months;
    
    final emi = principal * monthlyRate * _power(1 + monthlyRate, months) / 
                (_power(1 + monthlyRate, months) - 1);
    return emi;
  }

  // Calculate Gold Loan EMI (Yearly interest, monthly payments)
  static double calculateGoldLoanEmi({
    required double principal,
    required double yearlyInterestRate,
    required int months,
  }) {
    final monthlyRate = yearlyInterestRate / (12 * 100);
    return _calculateCompoundInterestEmi(principal, monthlyRate, months);
  }

  // Calculate No-Cost EMI with processing fee and GST
  static Map<String, double> calculateNoCostEmi({
    required double principal,
    required int months,
    required double processingFee,
    required double gstRate, // Usually 18%
  }) {
    final gstAmount = processingFee * (gstRate / 100);
    final totalProcessingFee = processingFee + gstAmount;
    final emiAmount = principal / months;
    final totalEmiAmount = emiAmount + (totalProcessingFee / months);
    
    return {
      'monthlyEmi': emiAmount,
      'processingFee': processingFee,
      'gstAmount': gstAmount,
      'totalProcessingFee': totalProcessingFee,
      'totalEmiAmount': totalEmiAmount,
      'totalAmount': principal + totalProcessingFee,
    };
  }

  // Calculate Credit Card EMI
  static Map<String, double> calculateCreditCardEmi({
    required double principal,
    required double interestRate,
    required int months,
    required double processingFee,
    required double gstRate,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    final emiAmount = _calculateCompoundInterestEmi(principal, monthlyRate, months);
    final gstAmount = processingFee * (gstRate / 100);
    final totalProcessingFee = processingFee + gstAmount;
    
    return {
      'monthlyEmi': emiAmount,
      'processingFee': processingFee,
      'gstAmount': gstAmount,
      'totalProcessingFee': totalProcessingFee,
      'totalAmount': (emiAmount * months) + totalProcessingFee,
      'totalInterest': (emiAmount * months) - principal,
    };
  }

  // Calculate Home Loan EMI
  static double calculateHomeLoanEmi({
    required double principal,
    required double interestRate,
    required int years,
  }) {
    final months = years * 12;
    final monthlyRate = interestRate / (12 * 100);
    return _calculateCompoundInterestEmi(principal, monthlyRate, months);
  }

  // Calculate Car Loan EMI
  static double calculateCarLoanEmi({
    required double principal,
    required double interestRate,
    required int years,
  }) {
    final months = years * 12;
    final monthlyRate = interestRate / (12 * 100);
    return _calculateCompoundInterestEmi(principal, monthlyRate, months);
  }

  // Calculate Personal Loan EMI
  static double calculatePersonalLoanEmi({
    required double principal,
    required double interestRate,
    required int months,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    return _calculateCompoundInterestEmi(principal, monthlyRate, months);
  }

  // Calculate Business Loan EMI
  static double calculateBusinessLoanEmi({
    required double principal,
    required double interestRate,
    required int years,
  }) {
    final months = years * 12;
    final monthlyRate = interestRate / (12 * 100);
    return _calculateCompoundInterestEmi(principal, monthlyRate, months);
  }

  // Generate EMI Schedule
  static List<Map<String, dynamic>> generateEmiSchedule({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    final schedule = <Map<String, dynamic>>[];
    
    double remainingPrincipal = principal;
    
    for (int i = 1; i <= months; i++) {
      final interestComponent = remainingPrincipal * monthlyRate;
      final principalComponent = emiAmount - interestComponent;
      remainingPrincipal -= principalComponent;
      
      schedule.add({
        'emiNumber': i,
        'dueDate': DateTime(startDate.year, startDate.month + i, startDate.day),
        'emiAmount': emiAmount,
        'principalComponent': principalComponent,
        'interestComponent': interestComponent,
        'remainingPrincipal': remainingPrincipal > 0 ? remainingPrincipal : 0,
      });
    }
    
    return schedule;
  }

  // Calculate Prepayment Savings
  static Map<String, double> calculatePrepaymentSavings({
    required double remainingPrincipal,
    required double prepaymentAmount,
    required double interestRate,
    required int remainingMonths,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    final originalEmi = _calculateCompoundInterestEmi(remainingPrincipal, monthlyRate, remainingMonths);
    final newPrincipal = remainingPrincipal - prepaymentAmount;
    final newEmi = _calculateCompoundInterestEmi(newPrincipal, monthlyRate, remainingMonths);
    
    final originalTotal = originalEmi * remainingMonths;
    final newTotal = newEmi * remainingMonths;
    final savings = originalTotal - newTotal;
    
    return {
      'originalEmi': originalEmi,
      'newEmi': newEmi,
      'emiReduction': originalEmi - newEmi,
      'totalSavings': savings,
      'interestSavings': savings,
    };
  }

  // Calculate Foreclosure Charges
  static double calculateForeclosureCharges({
    required double remainingPrincipal,
    required double foreclosureRate, // Usually 2-5%
  }) {
    return remainingPrincipal * (foreclosureRate / 100);
  }

  // Calculate Interest for Different Frequencies
  static double calculateInterest({
    required double principal,
    required double interestRate,
    required String frequency,
    required int timePeriod,
  }) {
    switch (frequency.toLowerCase()) {
      case 'monthly':
        return (principal * interestRate * timePeriod) / (12 * 100);
      case 'quarterly':
        return (principal * interestRate * timePeriod * 3) / (12 * 100);
      case 'yearly':
        return (principal * interestRate * timePeriod) / 100;
      default:
        return (principal * interestRate * timePeriod) / (12 * 100);
    }
  }

  // Calculate Total Interest Paid
  static double calculateTotalInterest({
    required double principal,
    required double emiAmount,
    required int months,
  }) {
    return (emiAmount * months) - principal;
  }

  // Calculate Outstanding Balance
  static double calculateOutstandingBalance({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int paidMonths,
    required int totalMonths,
  }) {
    final monthlyRate = interestRate / (12 * 100);
    final remainingMonths = totalMonths - paidMonths;
    
    if (remainingMonths <= 0) return 0;
    
    // Calculate remaining principal using EMI formula
    final remainingPrincipal = emiAmount * 
        ((1 - _power(1 + monthlyRate, -remainingMonths)) / monthlyRate);
    
    return remainingPrincipal;
  }

  // Helper method for power calculation
  static double _power(double base, int exponent) {
    double result = 1;
    for (int i = 0; i < exponent.abs(); i++) {
      result *= base;
    }
    return exponent >= 0 ? result : 1 / result;
  }

  // Get Default Interest Rates for Different Loan Types
  static Map<String, double> getDefaultInterestRates() {
    return {
      'personal': 12.0, // 12% per annum
      'home': 8.5, // 8.5% per annum
      'car': 9.5, // 9.5% per annum
      'gold': 7.5, // 7.5% per annum
      'business': 15.0, // 15% per annum
      'credit_card': 18.0, // 18% per annum
      'education': 10.0, // 10% per annum
    };
  }

  // Get Default Processing Fees
  static Map<String, double> getDefaultProcessingFees() {
    return {
      'personal': 2.0, // 2% of loan amount
      'home': 1.0, // 1% of loan amount
      'car': 1.5, // 1.5% of loan amount
      'gold': 1.0, // 1% of loan amount
      'business': 2.5, // 2.5% of loan amount
      'credit_card': 0.0, // Usually no processing fee for credit cards
      'education': 1.5, // 1.5% of loan amount
    };
  }

  // Calculate Credit Score Impact
  static Map<String, String> calculateCreditScoreImpact({
    required double creditUtilization,
    required int paymentHistory,
    required int creditAge,
    required int creditInquiries,
  }) {
    String impact = 'Good';
    String description = 'Your credit score is in good standing';
    
    if (creditUtilization > 30) {
      impact = 'Poor';
      description = 'High credit utilization may negatively impact your score';
    } else if (paymentHistory < 90) {
      impact = 'Fair';
      description = 'Payment history could be improved';
    } else if (creditAge < 2) {
      impact = 'Fair';
      description = 'Limited credit history';
    } else if (creditInquiries > 5) {
      impact = 'Poor';
      description = 'Too many credit inquiries';
    }
    
    return {
      'impact': impact,
      'description': description,
    };
  }
}

// Provider for loan calculation service
final loanCalculationServiceProvider = Provider<LoanCalculationService>((ref) {
  return LoanCalculationService();
}); 