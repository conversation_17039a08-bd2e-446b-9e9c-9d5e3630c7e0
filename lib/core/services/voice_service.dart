import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'dart:async';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';

class VoiceService {
  static final VoiceService _instance = VoiceService._internal();
  factory VoiceService() => _instance;
  VoiceService._internal();

  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  bool _speechEnabled = false;
  bool _isListening = false;

  Future<bool> requestMicPermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  Future<bool> hasMicPermission() async {
    return await Permission.microphone.isGranted;
  }

  Future<void> initialize() async {
    if (!await hasMicPermission()) {
      await requestMicPermission();
    }
    _speechEnabled = await _speechToText.initialize(
      onError: (error) => print('Speech recognition error: $error'),
      onStatus: (status) => print('Speech recognition status: $status'),
    );

    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
  }

  bool get isAvailable => _speechEnabled;
  bool get isListening => _isListening;

  Future<void> startListening({
    required Function(String text) onResult,
    required Function() onListeningComplete,
  }) async {
    if (!_speechEnabled) {
      print('Speech recognition not available');
      return;
    }
    if (!await hasMicPermission()) {
      final granted = await requestMicPermission();
      if (!granted) {
        print('Microphone permission not granted');
        return;
      }
    }
    _isListening = true;
    await _speechToText.listen(
      onResult: (result) {
        if (result.finalResult) {
          onResult(result.recognizedWords);
          _isListening = false;
          onListeningComplete();
        }
      },
      listenFor: const Duration(seconds: 10),
      pauseFor: const Duration(seconds: 3),
      partialResults: true,
      localeId: "en_US",
      onSoundLevelChange: (level) {
        // Optional: Handle sound level changes for UI feedback
      },
    );
  }

  Future<void> stopListening() async {
    await _speechToText.stop();
    _isListening = false;
  }

  Future<void> speak(String text) async {
    await _flutterTts.speak(text);
  }

  Future<void> stopSpeaking() async {
    await _flutterTts.stop();
  }

  // Parse voice input into expense data
  Map<String, dynamic> parseExpenseFromVoice(String voiceText) {
    final text = voiceText.toLowerCase();
    final amount = _extractAmount(text);
    final category = _extractCategoryFuzzy(text);
    final description = _extractDescription(text);
    final date = _extractDate(text);

    // Infer payment method from text, or default to 'Cash'
    String paymentMethod = 'Cash';
    if (text.contains('bank')) paymentMethod = 'Bank';
    else if (text.contains('credit card')) paymentMethod = 'Credit Card';
    else if (text.contains('borrow') || text.contains('lend')) paymentMethod = 'Credit from Other';

    return {
      'amount': amount,
      'category': category,
      'description': description,
      'date': date,
      'paymentMethod': paymentMethod,
      'accountId': 1, // Default account ID
    };
  }

  double? _extractAmount(String text) {
    // Extract amount using regex
    final amountRegex = RegExp(r'(\d+(?:\.\d{2})?)\s*(?:rupees?|rs?|₹|dollars?|\$)?', caseSensitive: false);
    final match = amountRegex.firstMatch(text);
    if (match != null) {
      return double.tryParse(match.group(1) ?? '');
    }
    return null;
  }

  DateTime _extractDate(String text) {
    final now = DateTime.now();
    if (text.contains('yesterday')) {
      return now.subtract(const Duration(days: 1));
    } else if (text.contains('today')) {
      return now;
    }
    // Try to extract a date like "on 5th May" or "on May 5"
    final dateRegex = RegExp(r'on (\d{1,2})(st|nd|rd|th)? (january|february|march|april|may|june|july|august|september|october|november|december)', caseSensitive: false);
    final match = dateRegex.firstMatch(text);
    if (match != null) {
      final day = int.tryParse(match.group(1) ?? '');
      final monthStr = match.group(3);
      final month = DateFormat('MMMM').parse(monthStr!).month;
      return DateTime(now.year, month, day ?? now.day);
    }
    // Try "on May 5"
    final dateRegex2 = RegExp(r'on (january|february|march|april|may|june|july|august|september|october|november|december) (\d{1,2})', caseSensitive: false);
    final match2 = dateRegex2.firstMatch(text);
    if (match2 != null) {
      final monthStr = match2.group(1);
      final day = int.tryParse(match2.group(2) ?? '');
      final month = DateFormat('MMMM').parse(monthStr!).month;
      return DateTime(now.year, month, day ?? now.day);
    }
    return now;
  }

  String? _extractCategoryFuzzy(String text) {
    final categoryMap = {
      'food': 'Food & Dining',
      'groceries': 'Food & Dining',
      'restaurant': 'Food & Dining',
      'dining': 'Food & Dining',
      'transport': 'Transportation',
      'car': 'Transportation',
      'fuel': 'Transportation',
      'uber': 'Transportation',
      'shopping': 'Shopping',
      'clothes': 'Shopping',
      'entertainment': 'Entertainment',
      'movie': 'Entertainment',
      'health': 'Healthcare',
      'medical': 'Healthcare',
      'salary': 'Salary',
      'income': 'Salary',
      'freelance': 'Freelance',
      'investment': 'Investment',
      'rent': 'Rent',
      'emi': 'EMI',
      'loan': 'Loan',
      'credit card': 'Credit Card',
      'debt': 'Debt',
      'gift': 'Gift',
      'travel': 'Travel',
      'bill': 'Bills',
      'electricity': 'Bills',
      'water': 'Bills',
      'phone': 'Bills',
      'mobile': 'Bills',
      'internet': 'Bills',
      'insurance': 'Insurance',
      'tax': 'Tax',
      'investment': 'Investment',
      'mutual fund': 'Investment',
      'stock': 'Investment',
      'sip': 'Investment',
    };
    for (final entry in categoryMap.entries) {
      if (text.contains(entry.key)) {
        return entry.value;
      }
    }
    // Fuzzy: if any word matches a category word
    for (final entry in categoryMap.entries) {
      for (final word in text.split(' ')) {
        if (word.contains(entry.key) || entry.key.contains(word)) {
          return entry.value;
        }
      }
    }
    return null;
  }

  String _extractDescription(String text) {
    // Remove amount and common words to get description
    final cleanText = text
        .replaceAll(RegExp(r'\d+(?:\.\d{2})?'), '')
        .replaceAll(RegExp(r'\b(spent|paid|bought|purchased|on|for|the|a|an)\b'), '')
        .trim();
    return cleanText.isNotEmpty ? cleanText : 'Voice input expense';
  }
} 