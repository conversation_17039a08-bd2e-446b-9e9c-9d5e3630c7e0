import '../database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class CurrencyService {
  final AppDatabase _database;

  CurrencyService(this._database);

  // Default currencies with their symbols
  static const Map<String, Map<String, String>> defaultCurrencies = {
    'INR': {'symbol': '₹', 'name': 'Indian Rupee'},
    'USD': {'symbol': '\$', 'name': 'US Dollar'},
    'EUR': {'symbol': '€', 'name': 'Euro'},
    'GBP': {'symbol': '£', 'name': 'British Pound'},
    'JPY': {'symbol': '¥', 'name': 'Japanese Yen'},
    'CAD': {'symbol': 'C\$', 'name': 'Canadian Dollar'},
    'AUD': {'symbol': 'A\$', 'name': 'Australian Dollar'},
    'CHF': {'symbol': 'CHF', 'name': 'Swiss Franc'},
    'CNY': {'symbol': '¥', 'name': 'Chinese Yuan'},
    'SGD': {'symbol': 'S\$', 'name': 'Singapore Dollar'},
    'AED': {'symbol': 'د.إ', 'name': 'UAE Dirham'},
    'SAR': {'symbol': 'ر.س', 'name': 'Saudi Riyal'},
    'QAR': {'symbol': 'ر.ق', 'name': 'Qatari Riyal'},
    'KWD': {'symbol': 'د.ك', 'name': 'Kuwaiti Dinar'},
    'BHD': {'symbol': 'د.ب', 'name': 'Bahraini Dinar'},
    'OMR': {'symbol': 'ر.ع.', 'name': 'Omani Rial'},
    'JOD': {'symbol': 'د.ا', 'name': 'Jordanian Dinar'},
    'LBP': {'symbol': 'ل.ل', 'name': 'Lebanese Pound'},
    'EGP': {'symbol': 'ج.م', 'name': 'Egyptian Pound'},
    'TRY': {'symbol': '₺', 'name': 'Turkish Lira'},
    'RUB': {'symbol': '₽', 'name': 'Russian Ruble'},
    'KRW': {'symbol': '₩', 'name': 'South Korean Won'},
    'THB': {'symbol': '฿', 'name': 'Thai Baht'},
    'MYR': {'symbol': 'RM', 'name': 'Malaysian Ringgit'},
    'IDR': {'symbol': 'Rp', 'name': 'Indonesian Rupiah'},
    'PHP': {'symbol': '₱', 'name': 'Philippine Peso'},
    'VND': {'symbol': '₫', 'name': 'Vietnamese Dong'},
    'BRL': {'symbol': 'R\$', 'name': 'Brazilian Real'},
    'MXN': {'symbol': '\$', 'name': 'Mexican Peso'},
    'ARS': {'symbol': '\$', 'name': 'Argentine Peso'},
    'CLP': {'symbol': '\$', 'name': 'Chilean Peso'},
    'COP': {'symbol': '\$', 'name': 'Colombian Peso'},
    'PEN': {'symbol': 'S/', 'name': 'Peruvian Sol'},
    'UYU': {'symbol': '\$', 'name': 'Uruguayan Peso'},
    'PYG': {'symbol': '₲', 'name': 'Paraguayan Guaraní'},
    'BOB': {'symbol': 'Bs', 'name': 'Bolivian Boliviano'},
    'NOK': {'symbol': 'kr', 'name': 'Norwegian Krone'},
    'SEK': {'symbol': 'kr', 'name': 'Swedish Krona'},
    'DKK': {'symbol': 'kr', 'name': 'Danish Krone'},
    'PLN': {'symbol': 'zł', 'name': 'Polish Złoty'},
    'CZK': {'symbol': 'Kč', 'name': 'Czech Koruna'},
    'HUF': {'symbol': 'Ft', 'name': 'Hungarian Forint'},
    'RON': {'symbol': 'lei', 'name': 'Romanian Leu'},
    'BGN': {'symbol': 'лв', 'name': 'Bulgarian Lev'},
    'HRK': {'symbol': 'kn', 'name': 'Croatian Kuna'},
    'RSD': {'symbol': 'дин', 'name': 'Serbian Dinar'},
    'UAH': {'symbol': '₴', 'name': 'Ukrainian Hryvnia'},
    'GEL': {'symbol': '₾', 'name': 'Georgian Lari'},
    'AMD': {'symbol': '֏', 'name': 'Armenian Dram'},
    'AZN': {'symbol': '₼', 'name': 'Azerbaijani Manat'},
    'BYN': {'symbol': 'Br', 'name': 'Belarusian Ruble'},
    'KZT': {'symbol': '₸', 'name': 'Kazakhstani Tenge'},
    'KGS': {'symbol': 'с', 'name': 'Kyrgyzstani Som'},
    'TJS': {'symbol': 'ЅМ', 'name': 'Tajikistani Somoni'},
    'TMT': {'symbol': 'T', 'name': 'Turkmenistani Manat'},
    'UZS': {'symbol': "so'm", 'name': 'Uzbekistani Som'},
    'MNT': {'symbol': '₮', 'name': 'Mongolian Tögrög'},
    'NPR': {'symbol': 'रू', 'name': 'Nepalese Rupee'},
    'BDT': {'symbol': '৳', 'name': 'Bangladeshi Taka'},
    'LKR': {'symbol': 'රු', 'name': 'Sri Lankan Rupee'},
    'PKR': {'symbol': '₨', 'name': 'Pakistani Rupee'},
    'AFN': {'symbol': '؋', 'name': 'Afghan Afghani'},
    'IRR': {'symbol': '﷼', 'name': 'Iranian Rial'},
    'IQD': {'symbol': 'ع.د', 'name': 'Iraqi Dinar'},
    'YER': {'symbol': '﷼', 'name': 'Yemeni Rial'},
    'SYP': {'symbol': 'ل.س', 'name': 'Syrian Pound'},
    'LYD': {'symbol': 'ل.د', 'name': 'Libyan Dinar'},
    'TND': {'symbol': 'د.ت', 'name': 'Tunisian Dinar'},
    'DZD': {'symbol': 'د.ج', 'name': 'Algerian Dinar'},
    'MAD': {'symbol': 'د.م.', 'name': 'Moroccan Dirham'},
    'XOF': {'symbol': 'CFA', 'name': 'West African CFA Franc'},
    'XAF': {'symbol': 'FCFA', 'name': 'Central African CFA Franc'},
    'XPF': {'symbol': 'CFP', 'name': 'CFP Franc'},
    'ZAR': {'symbol': 'R', 'name': 'South African Rand'},
    'NGN': {'symbol': '₦', 'name': 'Nigerian Naira'},
    'KES': {'symbol': 'KSh', 'name': 'Kenyan Shilling'},
    'UGX': {'symbol': 'USh', 'name': 'Ugandan Shilling'},
    'TZS': {'symbol': 'TSh', 'name': 'Tanzanian Shilling'},
    'ETB': {'symbol': 'Br', 'name': 'Ethiopian Birr'},
    'GHS': {'symbol': 'GH₵', 'name': 'Ghanaian Cedi'},
  };

  // Initialize default currencies
  Future<void> initializeDefaultCurrencies() async {
    final existingCurrencies = await _database.select(_database.currencySettings).get();
    
    if (existingCurrencies.isEmpty) {
      final companions = defaultCurrencies.entries.map((entry) {
        final code = entry.key;
        final data = entry.value;
        return CurrencySettingsCompanion.insert(
          currencyCode: code,
          currencySymbol: data['symbol']!,
          currencyName: data['name']!,
          isDefault: drift.Value(false),
          lastUpdated: DateTime.now(),
        );
      }).toList();
      
      await _database.batch((batch) {
        batch.insertAll(_database.currencySettings, companions);
      });
    }
  }

  // Get current default currency
  Future<CurrencySetting?> getDefaultCurrency() async {
    final result = await (_database.select(_database.currencySettings)
        ..where((tbl) => tbl.isDefault.equals(true)))
        .getSingleOrNull();
    return result;
  }

  // Set default currency
  Future<void> setDefaultCurrency(String currencyCode) async {
    await _database.transaction(() async {
      // Remove current default
      await (_database.update(_database.currencySettings)
            ..where((tbl) => tbl.isDefault.equals(true)))
          .write(CurrencySettingsCompanion(isDefault: drift.Value(false)));
      
      // Set new default
      await (_database.update(_database.currencySettings)
            ..where((tbl) => tbl.currencyCode.equals(currencyCode)))
          .write(CurrencySettingsCompanion(isDefault: drift.Value(true)));
    });
  }

  // Get all currencies
  Future<List<CurrencySetting>> getAllCurrencies() async {
    return await _database.select(_database.currencySettings).get();
  }

  // Format amount with current currency
  Future<String> formatAmount(double amount) async {
    final defaultCurrency = await getDefaultCurrency();
    if (defaultCurrency == null) return amount.toStringAsFixed(2);
    
    return '${defaultCurrency.currencySymbol}${amount.toStringAsFixed(2)}';
  }

  // Format amount with specific currency
  String formatAmountWithCurrency(double amount, String symbol) {
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  // Convert amount between currencies
  Future<double> convertAmount(double amount, String fromCurrency, String toCurrency) async {
    if (fromCurrency == toCurrency) return amount;
    
    // Get exchange rates (simplified - in real app, you'd fetch from API)
    final fromRate = await getExchangeRate(fromCurrency);
    final toRate = await getExchangeRate(toCurrency);
    
    if (fromRate == null || toRate == null) return amount;
    
    // Convert through USD as base (simplified conversion)
    final usdAmount = amount / fromRate;
    return usdAmount * toRate;
  }

  // Get exchange rate for currency (simplified - would be from API)
  Future<double?> getExchangeRate(String currencyCode) async {
    if (currencyCode == 'USD') return 1.0;
    
    // Simplified exchange rates (in real app, fetch from API)
    final rates = {
      'INR': 83.0,
      'EUR': 0.92,
      'GBP': 0.79,
      'JPY': 150.0,
      'CAD': 1.35,
      'AUD': 1.52,
      'CHF': 0.88,
      'CNY': 7.2,
      'SGD': 1.34,
      'AED': 3.67,
      'SAR': 3.75,
      'QAR': 3.64,
      'KWD': 0.31,
      'BHD': 0.38,
      'OMR': 0.38,
      'JOD': 0.71,
      'LBP': 15000.0,
      'EGP': 31.0,
      'TRY': 31.0,
      'RUB': 95.0,
      'KRW': 1350.0,
      'THB': 35.0,
      'MYR': 4.7,
      'IDR': 15700.0,
      'PHP': 56.0,
      'VND': 24500.0,
      'BRL': 4.95,
      'MXN': 17.0,
      'ARS': 350.0,
      'CLP': 950.0,
      'COP': 3900.0,
      'PEN': 3.7,
      'UYU': 39.0,
      'PYG': 7300.0,
      'BOB': 6.9,
      'NOK': 10.5,
      'SEK': 10.4,
      'DKK': 6.9,
      'PLN': 4.0,
      'CZK': 23.0,
      'HUF': 350.0,
      'RON': 4.6,
      'BGN': 1.8,
      'HRK': 7.0,
      'RSD': 108.0,
      'UAH': 38.0,
      'GEL': 2.7,
      'AMD': 400.0,
      'AZN': 1.7,
      'BYN': 3.2,
      'KZT': 450.0,
      'KGS': 89.0,
      'TJS': 11.0,
      'TMT': 3.5,
      'UZS': 12500.0,
      'MNT': 3400.0,
      'NPR': 133.0,
      'BDT': 110.0,
      'LKR': 320.0,
      'PKR': 280.0,
      'AFN': 70.0,
      'IRR': 42000.0,
      'IQD': 1310.0,
      'YER': 250.0,
      'SYP': 13000.0,
      'LYD': 4.8,
      'TND': 3.1,
      'DZD': 135.0,
      'MAD': 10.0,
      'XOF': 600.0,
      'XAF': 600.0,
      'XPF': 110.0,
      'ZAR': 18.5,
      'NGN': 1600.0,
      'KES': 160.0,
      'UGX': 3800.0,
      'TZS': 2500.0,
      'ETB': 56.0,
      'GHS': 12.0,
    };
    
    return rates[currencyCode];
  }

  // Update exchange rates (would be called periodically from API)
  Future<void> updateExchangeRates() async {
    // In real app, fetch from currency API
    // For now, just update timestamp
    await (_database.update(_database.currencySettings))
        .write(CurrencySettingsCompanion(lastUpdated: drift.Value(DateTime.now())));
  }
} 