import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:flutter/material.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notifications = FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    tz.initializeTimeZones();

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(initSettings);
  }

  // Schedule budget alert
  Future<void> scheduleBudgetAlert({
    required String budgetName,
    required double spentAmount,
    required double budgetAmount,
    required DateTime dueDate,
  }) async {
    final spentPercentage = (spentAmount / budgetAmount) * 100;
    
    if (spentPercentage >= 80) {
      String title;
      String body;
      int priority;

      if (spentPercentage >= 100) {
        title = 'Budget Exceeded!';
        body = 'You\'ve exceeded your $budgetName budget by ${(spentPercentage - 100).toStringAsFixed(1)}%';
        priority = 1; // High priority
      } else {
        title = 'Budget Alert';
        body = 'You\'ve spent ${spentPercentage.toStringAsFixed(1)}% of your $budgetName budget';
        priority = 2; // Medium priority
      }

      await _scheduleNotification(
        id: 'budget_${budgetName.hashCode}',
        title: title,
        body: body,
        scheduledDate: dueDate,
        priority: priority,
      );
    }
  }

  // Schedule EMI due alert
  Future<void> scheduleEmiAlert({
    required String emiName,
    required double amount,
    required DateTime dueDate,
  }) async {
    final now = DateTime.now();
    final daysUntilDue = dueDate.difference(now).inDays;

    if (daysUntilDue <= 7) {
      String title;
      String body;
      int priority;

      if (daysUntilDue <= 0) {
        title = 'EMI Overdue!';
        body = 'EMI payment of ₹${amount.toStringAsFixed(2)} for $emiName is overdue';
        priority = 1; // High priority
      } else if (daysUntilDue <= 3) {
        title = 'EMI Due Soon';
        body = 'EMI payment of ₹${amount.toStringAsFixed(2)} for $emiName is due in $daysUntilDue days';
        priority = 1; // High priority
      } else {
        title = 'EMI Due This Week';
        body = 'EMI payment of ₹${amount.toStringAsFixed(2)} for $emiName is due in $daysUntilDue days';
        priority = 2; // Medium priority
      }

      await _scheduleNotification(
        id: 'emi_${emiName.hashCode}',
        title: title,
        body: body,
        scheduledDate: dueDate,
        priority: priority,
      );
    }
  }

  // Schedule credit card due alert
  Future<void> scheduleCreditCardAlert({
    required String cardName,
    required double amount,
    required DateTime dueDate,
  }) async {
    final now = DateTime.now();
    final daysUntilDue = dueDate.difference(now).inDays;

    if (daysUntilDue <= 7) {
      String title;
      String body;
      int priority;

      if (daysUntilDue <= 0) {
        title = 'Credit Card Payment Overdue!';
        body = 'Payment for $cardName is overdue';
        priority = 1; // High priority
      } else if (daysUntilDue <= 3) {
        title = 'Credit Card Payment Due Soon';
        body = 'Payment for $cardName is due in $daysUntilDue days';
        priority = 1; // High priority
      } else {
        title = 'Credit Card Payment Due This Week';
        body = 'Payment for $cardName is due in $daysUntilDue days';
        priority = 2; // Medium priority
      }

      await _scheduleNotification(
        id: 'cc_${cardName.hashCode}',
        title: title,
        body: body,
        scheduledDate: dueDate,
        priority: priority,
      );
    }
  }

  // Schedule debt due alert
  Future<void> scheduleDebtAlert({
    required String debtName,
    required double amount,
    required DateTime dueDate,
  }) async {
    final now = DateTime.now();
    final daysUntilDue = dueDate.difference(now).inDays;

    if (daysUntilDue <= 7) {
      String title;
      String body;
      int priority;

      if (daysUntilDue <= 0) {
        title = 'Debt Payment Overdue!';
        body = 'Payment for $debtName is overdue';
        priority = 1; // High priority
      } else if (daysUntilDue <= 3) {
        title = 'Debt Payment Due Soon';
        body = 'Payment for $debtName is due in $daysUntilDue days';
        priority = 1; // High priority
      } else {
        title = 'Debt Payment Due This Week';
        body = 'Payment for $debtName is due in $daysUntilDue days';
        priority = 2; // Medium priority
      }

      await _scheduleNotification(
        id: 'debt_${debtName.hashCode}',
        title: title,
        body: body,
        scheduledDate: dueDate,
        priority: priority,
      );
    }
  }

  // Schedule daily expense reminder
  Future<void> scheduleDailyExpenseReminder() async {
    await _scheduleNotification(
      id: 'daily_expense_reminder',
      title: 'Daily Expense Reminder',
      body: 'Don\'t forget to log your expenses for today!',
      scheduledDate: DateTime.now().add(const Duration(days: 1)),
      priority: 3, // Low priority
      repeat: true,
    );
  }

  // Schedule weekly budget review
  Future<void> scheduleWeeklyBudgetReview() async {
    await _scheduleNotification(
      id: 'weekly_budget_review',
      title: 'Weekly Budget Review',
      body: 'Time to review your budget and spending for this week',
      scheduledDate: DateTime.now().add(const Duration(days: 7)),
      priority: 2, // Medium priority
      repeat: true,
    );
  }

  Future<void> _scheduleNotification({
    required String id,
    required String title,
    required String body,
    required DateTime scheduledDate,
    required int priority,
    bool repeat = false,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      'financial_alerts',
      'Financial Alerts',
      channelDescription: 'Notifications for budget alerts and payment reminders',
      importance: priority == 1 ? Importance.high : priority == 2 ? Importance.defaultImportance : Importance.low,
      priority: priority == 1 ? Priority.high : priority == 2 ? Priority.defaultPriority : Priority.low,
      color: priority == 1 ? const Color(0xFFE53E3E) : priority == 2 ? const Color(0xFFDD6B20) : const Color(0xFF3182CE),
      enableLights: true,
      enableVibration: true,
      playSound: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    if (repeat) {
      // Repeat daily for daily reminders
      await _notifications.zonedSchedule(
        int.parse(id.hashCode.toString().replaceAll('-', '')),
        title,
        body,
        tz.TZDateTime.now(tz.local).add(const Duration(days: 1)),
        details,
        androidScheduleMode: AndroidScheduleMode.exact,
      );
    } else {
      // One-time notification
      await _notifications.zonedSchedule(
        int.parse(id.hashCode.toString().replaceAll('-', '')),
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        details,
        androidScheduleMode: AndroidScheduleMode.exact,
      );
    }
  }

  // Cancel all notifications
  Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // Cancel specific notification
  Future<void> cancelNotification(String id) async {
    await _notifications.cancel(int.parse(id.hashCode.toString().replaceAll('-', '')));
  }

  // Show immediate notification (for testing)
  Future<void> showImmediateNotification({
    required String title,
    required String body,
    int priority = 2,
  }) async {
    final androidDetails = AndroidNotificationDetails(
      'financial_alerts',
      'Financial Alerts',
      channelDescription: 'Notifications for budget alerts and payment reminders',
      importance: priority == 1 ? Importance.high : priority == 2 ? Importance.defaultImportance : Importance.low,
      priority: priority == 1 ? Priority.high : priority == 2 ? Priority.defaultPriority : Priority.low,
      color: priority == 1 ? const Color(0xFFE53E3E) : priority == 2 ? const Color(0xFFDD6B20) : const Color(0xFF3182CE),
      enableLights: true,
      enableVibration: true,
      playSound: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    final details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      details,
    );
  }

  Future<void> init() async {}
  Future<void> showNotification(int id, String title, String message) async {}
} 