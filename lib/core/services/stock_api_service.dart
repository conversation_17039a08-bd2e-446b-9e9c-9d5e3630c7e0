import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_riverpod/flutter_riverpod.dart';

class StockSymbol {
  final String symbol;
  final String name;
  final String exchange;
  final String type;
  final String? sector;
  final String? industry;

  StockSymbol({
    required this.symbol,
    required this.name,
    required this.exchange,
    required this.type,
    this.sector,
    this.industry,
  });

  factory StockSymbol.fromJson(Map<String, dynamic> json) {
    return StockSymbol(
      symbol: json['symbol'] ?? '',
      name: json['name'] ?? '',
      exchange: json['exchange'] ?? '',
      type: json['type'] ?? '',
      sector: json['sector'],
      industry: json['industry'],
    );
  }
}

class StockPrice {
  final String symbol;
  final double price;
  final double change;
  final double changePercent;
  final double open;
  final double high;
  final double low;
  final double previousClose;
  final int volume;
  final DateTime timestamp;

  StockPrice({
    required this.symbol,
    required this.price,
    required this.change,
    required this.changePercent,
    required this.open,
    required this.high,
    required this.low,
    required this.previousClose,
    required this.volume,
    required this.timestamp,
  });

  factory StockPrice.fromJson(Map<String, dynamic> json) {
    return StockPrice(
      symbol: json['symbol'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      change: (json['change'] ?? 0.0).toDouble(),
      changePercent: (json['changePercent'] ?? 0.0).toDouble(),
      open: (json['open'] ?? 0.0).toDouble(),
      high: (json['high'] ?? 0.0).toDouble(),
      low: (json['low'] ?? 0.0).toDouble(),
      previousClose: (json['previousClose'] ?? 0.0).toDouble(),
      volume: json['volume'] ?? 0,
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class StockApiService {
  // Using Alpha Vantage API (free tier available)
  static const String _apiKey = 'demo'; // Replace with your API key
  static const String _baseUrl = 'https://www.alphavantage.co/query';
  
  // Alternative: Using Yahoo Finance API (no key required)
  static const String _yahooBaseUrl = 'https://query1.finance.yahoo.com/v8/finance';

  // Search for stock symbols
  Future<List<StockSymbol>> searchSymbols(String query) async {
    final symbols = await _getPopularSymbols();
    return symbols.where((symbol) =>
        symbol.symbol.toLowerCase().contains(query.toLowerCase())
      ).toList();
  }

  Future<List<StockSymbol>> searchSymbolsByCategory(String category) async {
    final symbols = await _getPopularSymbols();
    return symbols.where((symbol) =>
        symbol.symbol.toLowerCase().contains(category.toLowerCase())
      ).toList();
  }

  // Get current stock price
  Future<StockPrice?> getStockPrice(String symbol) async {
    try {
      // Using Alpha Vantage Quote API
      final response = await http.get(
        Uri.parse('$_baseUrl?function=GLOBAL_QUOTE&symbol=$symbol&apikey=$_apiKey'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final quote = data['Global Quote'];
        
        if (quote != null) {
          return StockPrice(
            symbol: quote['01. symbol'] ?? symbol,
            price: double.tryParse(quote['05. price'] ?? '0') ?? 0.0,
            change: double.tryParse(quote['09. change'] ?? '0') ?? 0.0,
            changePercent: double.tryParse(quote['10. change percent']?.replaceAll('%', '') ?? '0') ?? 0.0,
            open: double.tryParse(quote['02. open'] ?? '0') ?? 0.0,
            high: double.tryParse(quote['03. high'] ?? '0') ?? 0.0,
            low: double.tryParse(quote['04. low'] ?? '0') ?? 0.0,
            previousClose: double.tryParse(quote['08. previous close'] ?? '0') ?? 0.0,
            volume: int.tryParse(quote['06. volume'] ?? '0') ?? 0,
            timestamp: DateTime.now(),
          );
        }
      }

      // Fallback: Return mock data for demo
      return _getMockStockPrice(symbol);
    } catch (e) {
      print('Error getting stock price: $e');
      return _getMockStockPrice(symbol);
    }
  }

  // Get popular symbols for suggestions
  Future<List<StockSymbol>> _getPopularSymbols() async {
    // Mock popular symbols
    return [
      StockSymbol(symbol: 'AAPL', name: 'Apple Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'GOOGL', name: 'Alphabet Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'MSFT', name: 'Microsoft Corporation', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'AMZN', name: 'Amazon.com Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'TSLA', name: 'Tesla Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'META', name: 'Meta Platforms Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'NVDA', name: 'NVIDIA Corporation', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'NFLX', name: 'Netflix Inc.', exchange: 'NASDAQ', type: 'Stock'),
      StockSymbol(symbol: 'DIS', name: 'The Walt Disney Company', exchange: 'NYSE', type: 'Stock'),
      StockSymbol(symbol: 'JPM', name: 'JPMorgan Chase & Co.', exchange: 'NYSE', type: 'Stock'),
    ];
  }

  // Get popular symbols for suggestions
  Future<List<StockSymbol>> getPopularSymbols() async {
    return await _getPopularSymbols();
  }

  // Mock stock price for demo purposes
  StockPrice _getMockStockPrice(String symbol) {
    final basePrice = 100.0 + (symbol.hashCode % 500);
    final change = (symbol.hashCode % 20 - 10).toDouble();
    final changePercent = (change / basePrice) * 100;
    
    return StockPrice(
      symbol: symbol,
      price: basePrice + change,
      change: change,
      changePercent: changePercent,
      open: basePrice,
      high: basePrice + 5,
      low: basePrice - 5,
      previousClose: basePrice,
      volume: 1000000 + (symbol.hashCode % 5000000),
      timestamp: DateTime.now(),
    );
  }

  // Get multiple stock prices at once
  Future<List<StockPrice>> getMultipleStockPrices(List<String> symbols) async {
    final prices = <StockPrice>[];
    
    for (final symbol in symbols) {
      final price = await getStockPrice(symbol);
      if (price != null) {
        prices.add(price);
      }
    }
    
    return prices;
  }
}

// Providers
final stockApiServiceProvider = Provider<StockApiService>((ref) {
  return StockApiService();
});

final stockSymbolsProvider = FutureProvider.family<List<StockSymbol>, String>((ref, query) async {
  final service = ref.watch(stockApiServiceProvider);
  return await service.searchSymbols(query);
});

final stockPriceProvider = FutureProvider.family<StockPrice?, String>((ref, symbol) async {
  final service = ref.watch(stockApiServiceProvider);
  return await service.getStockPrice(symbol);
});

final multipleStockPricesProvider = FutureProvider.family<List<StockPrice>, List<String>>((ref, symbols) async {
  final service = ref.watch(stockApiServiceProvider);
  return await service.getMultipleStockPrices(symbols);
}); 