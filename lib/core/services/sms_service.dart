import 'package:permission_handler/permission_handler.dart';
import '../database/app_database.dart' as db;
import 'sms_parser_service.dart' as parser;
import 'package:flutter/services.dart';

class SmsService {
  final db.AppDatabase database;
  static const MethodChannel _channel = MethodChannel('sms_channel');

  SmsService(this.database);

  Future<List<String>> getSmsMessages() async {
    try {
      final List<dynamic> messages = await _channel.invokeMethod('getSmsMessages');
      return messages.cast<String>();
    } on PlatformException catch (e) {
      throw Exception('Failed to get SMS messages: \\${e.message}');
    }
  }

  Future<bool> requestSmsPermission() async {
    final status = await Permission.sms.request();
    return status.isGranted;
  }

  Future<bool> hasSmsPermission() async {
    return await Permission.sms.isGranted;
  }

  Future<List<parser.SmsTransactionData>> processUpiTransactions() async {
    if (!await hasSmsPermission()) {
      throw Exception('SMS permission not granted');
    }
    final messages = await getSmsMessages();
    final upiTransactions = <parser.SmsTransactionData>[];
    for (final message in messages) {
      if (parser.SmsParserService.isUpiTransaction(message)) {
        final parsed = parser.SmsParserService.parseUpiTransaction(message);
        if (parsed != null) {
          upiTransactions.add(parsed);
          // Save to database (implement as needed)
          await _saveSmsTransaction(parsed);
        }
      }
    }
    return upiTransactions;
  }

  Future<void> _saveSmsTransaction(parser.SmsTransactionData transaction) async {
    // TODO: Implement DB save logic for parsed SMS transaction
    // This is a stub for now
  }
} 