import 'package:drift/drift.dart' as drift;
import '../database/app_database.dart';

class TransactionService {
  final AppDatabase _database;

  TransactionService(this._database);

  /// Create a transaction and update account balance
  Future<void> createTransaction({
    required DateTime date,
    required double amount,
    required String type, // expense, income, transfer, etc.
    required String description,
    int? fromAccountId,
    int? toAccountId,
    int? categoryId,
    String? note,
    String? contactId,
  }) async {
    await _database.transaction(() async {
      // Create the transaction
      final transactionId = await _database.into(_database.transactions).insert(
        TransactionsCompanion.insert(
          date: date,
          amount: amount,
          type: drift.Value(type),
          description: drift.Value(description),
          fromAccountId: drift.Value(fromAccountId),
          toAccountId: drift.Value(toAccountId),
          categoryId: drift.Value(categoryId),
          note: drift.Value(note),
          contactId: drift.Value(contactId),
        ),
      );

      // Update account balances based on transaction type
      await _updateAccountBalances(
        transactionId: transactionId,
        amount: amount,
        type: type,
        fromAccountId: fromAccountId,
        toAccountId: toAccountId,
      );
    });
  }

  /// Update account balances based on transaction
  Future<void> _updateAccountBalances({
    required int transactionId,
    required double amount,
    required String type,
    int? fromAccountId,
    int? toAccountId,
  }) async {
    switch (type) {
      case 'expense':
        if (fromAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
        }
        break;
      case 'income':
        if (toAccountId != null) {
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
      case 'transfer':
        if (fromAccountId != null && toAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
      case 'credit_given':
        if (fromAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
        }
        break;
      case 'credit_received':
        if (toAccountId != null) {
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
    }
  }

  /// Increase account balance
  Future<void> _increaseAccountBalance(int accountId, double amount) async {
    await (_database.update(_database.bankAccounts)
          ..where((tbl) => tbl.id.equals(accountId)))
        .write(BankAccountsCompanion(
      currentBalance: drift.Value(
        await _getCalculatedBalance(accountId) + amount,
      ),
      lastUpdated: drift.Value(DateTime.now()),
    ));
  }

  /// Decrease account balance
  Future<void> _decreaseAccountBalance(int accountId, double amount) async {
    await (_database.update(_database.bankAccounts)
          ..where((tbl) => tbl.id.equals(accountId)))
        .write(BankAccountsCompanion(
      currentBalance: drift.Value(
        await _getCalculatedBalance(accountId) - amount,
      ),
      lastUpdated: drift.Value(DateTime.now()),
    ));
  }

  /// Calculate balance from transactions for an account
  Future<double> _getCalculatedBalance(int accountId) async {
    final account = await (_database.select(_database.bankAccounts)
          ..where((tbl) => tbl.id.equals(accountId)))
        .getSingleOrNull();

    if (account == null) return 0.0;

    // Get all transactions for this account
    final transactions = await (_database.select(_database.transactions)
          ..where((tbl) => 
              tbl.fromAccountId.equals(accountId) | 
              tbl.toAccountId.equals(accountId)))
        .get();

    double balance = account.openingBalance;

    for (final transaction in transactions) {
      if (transaction.type == 'expense' && transaction.fromAccountId == accountId) {
        balance -= transaction.amount;
      } else if (transaction.type == 'income' && transaction.toAccountId == accountId) {
        balance += transaction.amount;
      } else if (transaction.type == 'transfer') {
        if (transaction.fromAccountId == accountId) {
          balance -= transaction.amount;
        } else if (transaction.toAccountId == accountId) {
          balance += transaction.amount;
        }
      } else if (transaction.type == 'credit_given' && transaction.fromAccountId == accountId) {
        balance -= transaction.amount;
      } else if (transaction.type == 'credit_received' && transaction.toAccountId == accountId) {
        balance += transaction.amount;
      }
    }

    return balance;
  }

  /// Refresh account balances from transactions
  Future<void> refreshAccountBalances() async {
    final accounts = await _database.select(_database.bankAccounts).get();

    for (final account in accounts) {
      final calculatedBalance = await _getCalculatedBalance(account.id);
      
      await (_database.update(_database.bankAccounts)
            ..where((tbl) => tbl.id.equals(account.id)))
          .write(BankAccountsCompanion(
        currentBalance: drift.Value(calculatedBalance),
        lastUpdated: drift.Value(DateTime.now()),
      ));
    }
  }

  /// Get transactions for a specific account
  Future<List<Transaction>> getAccountTransactions(int accountId) async {
    return await (_database.select(_database.transactions)
          ..where((tbl) => 
              tbl.fromAccountId.equals(accountId) | 
              tbl.toAccountId.equals(accountId))
          ..orderBy([(tbl) => drift.OrderingTerm.desc(tbl.date)]))
        .get();
  }

  /// Get all transactions with category information
  Future<List<TransactionWithCategory>> getTransactionsWithCategories() async {
    final query = _database.select(_database.transactions).join([
      drift.leftOuterJoin(_database.categories, 
          _database.transactions.categoryId.equalsExp(_database.categories.id)),
    ]);

    final results = await query.get();
    
    return results.map((row) {
      final transaction = row.readTable(_database.transactions);
      final category = row.readTableOrNull(_database.categories);
      
      return TransactionWithCategory(
        transaction: transaction,
        category: category,
      );
    }).toList();
  }

  /// Create an expense transaction
  Future<void> createExpenseTransaction({
    required String description,
    required double amount,
    required int categoryId,
    required DateTime date,
    required int accountId,
    String? note,
    String? contactId,
    String? contactName,
    String? contactPhone,
  }) async {
    await createTransaction(
      date: date,
      amount: amount,
      type: 'expense',
      description: description,
      fromAccountId: accountId,
      categoryId: categoryId,
      note: note,
      contactId: contactId,
    );
  }

  /// Create an income transaction
  Future<void> createIncomeTransaction({
    required String description,
    required double amount,
    required int categoryId,
    required DateTime date,
    required int accountId,
    String? note,
  }) async {
    await createTransaction(
      date: date,
      amount: amount,
      type: 'income',
      description: description,
      toAccountId: accountId,
      categoryId: categoryId,
      note: note,
    );
  }

  /// Create a transfer transaction
  Future<void> createTransferTransaction({
    required double amount,
    required DateTime date,
    required int fromAccountId,
    required int toAccountId,
    String? note,
  }) async {
    await createTransaction(
      date: date,
      amount: amount,
      type: 'transfer',
      description: 'Transfer',
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      note: note,
    );
  }

  Future<List<TransactionWithCategory>> getAccountTransactionsWithCategories(int accountId) async {
    print('DEBUG: getAccountTransactionsWithCategories called for accountId: $accountId');
    final query = _database.select(_database.transactions)
      ..where((tbl) => tbl.fromAccountId.equals(accountId) | tbl.toAccountId.equals(accountId));
    final join = query.join([
      drift.leftOuterJoin(_database.categories, _database.transactions.categoryId.equalsExp(_database.categories.id)),
    ]);
    final results = await join.get();
    print('DEBUG: Found ${results.length} transactions for accountId: $accountId');
    for (final row in results) {
      final transaction = row.readTable(_database.transactions);
      print('DEBUG: Transaction id: ${transaction.id}, type: ${transaction.type}, from: ${transaction.fromAccountId}, to: ${transaction.toAccountId}, amount: ${transaction.amount}');
    }
    return results.map((row) {
      final transaction = row.readTable(_database.transactions);
      final category = row.readTableOrNull(_database.categories);
      return TransactionWithCategory(transaction: transaction, category: category);
    }).toList();
  }
}

/// Data class for transaction with category information
class TransactionWithCategory {
  final Transaction transaction;
  final Category? category;

  TransactionWithCategory({
    required this.transaction,
    this.category,
  });
} 