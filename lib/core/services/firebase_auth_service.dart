import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'dart:io' show Platform;

class AppUser {
  final String id;
  final String? email;
  final String? displayName;
  final String? photoURL;

  AppUser({
    required this.id,
    this.email,
    this.displayName,
    this.photoURL,
  });

  factory AppUser.fromFirebaseUser(firebase_auth.User? firebaseUser) {
    if (firebaseUser == null) return AppUser(id: '');
    return AppUser(
      id: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
    );
  }
}

class AuthState {
  final AppUser? user;
  final bool isLoading;

  AuthState({this.user, this.isLoading = false});

  bool get isAuthenticated => user != null && user!.id.isNotEmpty;
}

class FirebaseAuthService {
  final firebase_auth.FirebaseAuth _auth = firebase_auth.FirebaseAuth.instance;

  Stream<AuthState> get authStateChanges {
    return _auth.authStateChanges().map((firebaseUser) {
      return AuthState(
        user: AppUser.fromFirebaseUser(firebaseUser),
        isLoading: false,
      );
    });
  }

  Stream<AppUser?> get userChanges => _auth.userChanges().map((firebaseUser) {
    return AppUser.fromFirebaseUser(firebaseUser);
  });

  Stream<AppUser?> get userStream {
    return _auth.authStateChanges().map((user) => user != null ? AppUser(
      id: user.uid,
      email: user.email ?? '',
      displayName: user.displayName ?? '',
    ) : null);
  }

  Future<firebase_auth.UserCredential> signIn(String email, String password) async {
    try {
      print('Signing in with email: $email');
      final credential = await _auth.signInWithEmailAndPassword(email: email, password: password);
      print('Sign in successful: ${credential.user?.uid}');
      return credential;
    } catch (e) {
      print('Sign in error: $e');
      if (e.toString().contains('user-not-found')) {
        throw Exception('No account found with this email. Please sign up first.');
      } else if (e.toString().contains('wrong-password')) {
        throw Exception('Incorrect password. Please try again.');
      } else if (e.toString().contains('invalid-email')) {
        throw Exception('Please enter a valid email address.');
      } else if (e.toString().contains('user-disabled')) {
        throw Exception('This account has been disabled. Please contact support.');
      } else {
        throw Exception('Sign-in failed: ${e.toString()}');
      }
    }
  }

  Future<firebase_auth.UserCredential> signUp(String email, String password, {String? displayName}) async {
    try {
      print('Creating user with email: $email');
      final credential = await _auth.createUserWithEmailAndPassword(email: email, password: password);
      
      if (displayName != null && credential.user != null) {
        await credential.user!.updateDisplayName(displayName);
        print('Updated display name to: $displayName');
      }
      
      print('User created successfully: ${credential.user?.uid}');
      return credential;
    } catch (e) {
      print('Sign up error: $e');
      if (e.toString().contains('email-already-in-use')) {
        throw Exception('An account with this email already exists. Please sign in instead.');
      } else if (e.toString().contains('weak-password')) {
        throw Exception('Password is too weak. Please use a stronger password (at least 6 characters).');
      } else if (e.toString().contains('invalid-email')) {
        throw Exception('Please enter a valid email address.');
      } else if (e.toString().contains('operation-not-allowed')) {
        throw Exception('Email/password sign-up is not enabled. Please contact support.');
      } else {
        throw Exception('Sign-up failed: ${e.toString()}');
      }
    }
  }

  Future<firebase_auth.UserCredential> signInWithGoogle() async {
    try {
      // Use Firebase Auth's built-in Google sign-in
      final googleProvider = firebase_auth.GoogleAuthProvider();
      googleProvider.addScope('email');
      googleProvider.addScope('profile');
      
      return await _auth.signInWithPopup(googleProvider);
    } catch (e) {
      print('Google sign-in error: $e');
      if (e.toString().contains('popup-closed-by-user')) {
        throw Exception('Sign-in was cancelled.');
      } else if (e.toString().contains('account-exists-with-different-credential')) {
        throw Exception('An account already exists with this email using a different sign-in method.');
      } else {
        throw Exception('Google sign-in failed. Please try again.');
      }
    }
  }

  Future<firebase_auth.UserCredential> signInWithApple() async {
    // Apple Sign-In implementation for iOS
    if (Platform.isIOS) {
      try {
        final appleProvider = firebase_auth.OAuthProvider('apple.com');
        return await _auth.signInWithPopup(appleProvider);
      } catch (e) {
        throw Exception('Apple sign-in failed: ${e.toString()}');
      }
    } else {
      throw Exception('Apple sign-in is only available on iOS');
    }
  }

  Future<void> signOut() async {
    await _auth.signOut();
  }

  Future<void> deleteAccount() async {
    final user = _auth.currentUser;
    if (user != null) {
      await user.delete();
    }
  }

  AppUser? get currentUser {
    final firebaseUser = _auth.currentUser;
    return AppUser.fromFirebaseUser(firebaseUser);
  }

  bool get isAuthenticated => currentUser != null && currentUser!.id.isNotEmpty;

  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      return userCredential.user != null;
    } catch (e) {
      print('Error signing in: $e');
      return false;
    }
  }
} 