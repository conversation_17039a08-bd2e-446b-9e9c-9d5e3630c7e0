import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  // Using a lightweight, mobile-compatible AI model
  // For production, you might want to use a local model or a more sophisticated API
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';
  static const String _apiKey = 'your-api-key-here'; // Replace with your actual API key

  // Get personalized saving tips based on user's spending patterns
  Future<List<String>> getSavingTips({
    required double monthlyIncome,
    required double monthlyExpenses,
    required List<String> topCategories,
    required double savingsGoal,
  }) async {
    try {
      final prompt = '''
Based on the following financial data, provide 5 practical and personalized saving tips:

Monthly Income: \$${monthlyIncome.toStringAsFixed(2)}
Monthly Expenses: \$${monthlyExpenses.toStringAsFixed(2)}
Top Spending Categories: ${topCategories.join(', ')}
Savings Goal: \$${savingsGoal.toStringAsFixed(2)}

Provide tips that are:
1. Specific to their spending patterns
2. Actionable and practical
3. Realistic for their income level
4. Focused on their top spending categories
5. Aimed at helping them reach their savings goal

Format as a JSON array of strings.
''';

      final response = await _makeAIRequest(prompt);
      final tips = _parseTipsFromResponse(response);
      return tips;
    } catch (e) {
      // Fallback tips if AI service is unavailable
      return _getFallbackTips(topCategories);
    }
  }

  // Get professional response for expense categorization
  Future<String> getExpenseInsight({
    required String category,
    required double amount,
    required String description,
    required double monthlyBudget,
    required double spentThisMonth,
  }) async {
    try {
      final prompt = '''
Analyze this expense and provide a brief, professional insight:

Category: $category
Amount: \$${amount.toStringAsFixed(2)}
Description: $description
Monthly Budget: \$${monthlyBudget.toStringAsFixed(2)}
Spent This Month: \$${spentThisMonth.toStringAsFixed(2)}

Provide a 1-2 sentence professional insight about this expense, including:
- Whether it's within budget
- Any potential concerns
- Positive aspects if applicable

Keep it concise and professional.
''';

      final response = await _makeAIRequest(prompt);
      return _parseInsightFromResponse(response);
    } catch (e) {
      return _getFallbackInsight(category, amount, monthlyBudget, spentThisMonth);
    }
  }

  // Get budget optimization suggestions
  Future<String> getBudgetOptimization({
    required Map<String, double> categorySpending,
    required double totalIncome,
    required double totalExpenses,
  }) async {
    try {
      final prompt = '''
Analyze this budget and provide optimization suggestions:

Total Income: \$${totalIncome.toStringAsFixed(2)}
Total Expenses: \$${totalExpenses.toStringAsFixed(2)}
Category Breakdown:
${categorySpending.entries.map((e) => '${e.key}: \$${e.value.toStringAsFixed(2)}').join('\n')}

Provide 2-3 specific, actionable suggestions for budget optimization.
Keep it professional and practical.
''';

      final response = await _makeAIRequest(prompt);
      return _parseOptimizationFromResponse(response);
    } catch (e) {
      return _getFallbackOptimization(totalIncome, totalExpenses);
    }
  }

  // Get investment advice based on financial profile
  Future<String> getInvestmentAdvice({
    required double monthlyIncome,
    required double monthlyExpenses,
    required double currentSavings,
    required int age,
    required String riskTolerance,
  }) async {
    try {
      final prompt = '''
Provide investment advice based on this financial profile:

Monthly Income: \$${monthlyIncome.toStringAsFixed(2)}
Monthly Expenses: \$${monthlyExpenses.toStringAsFixed(2)}
Current Savings: \$${currentSavings.toStringAsFixed(2)}
Age: $age
Risk Tolerance: $riskTolerance

Provide 2-3 specific investment suggestions that are:
- Appropriate for their income level
- Suitable for their age and risk tolerance
- Focused on building wealth over time
- Practical and actionable

Keep it professional and educational.
''';

      final response = await _makeAIRequest(prompt);
      return _parseInvestmentAdviceFromResponse(response);
    } catch (e) {
      return _getFallbackInvestmentAdvice(monthlyIncome, currentSavings, age);
    }
  }

  // Make AI request
  Future<String> _makeAIRequest(String prompt) async {
    // For demo purposes, we'll use a mock response
    // In production, you would make actual API calls
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
    
    // Mock responses for different types of requests
    if (prompt.contains('saving tips')) {
      return jsonEncode([
        "Set up automatic transfers to savings account on payday",
        "Use the 50/30/20 rule: 50% needs, 30% wants, 20% savings",
        "Track your spending with this app to identify unnecessary expenses",
        "Cook meals at home instead of eating out to save on food costs",
        "Review and cancel unused subscriptions monthly"
      ]);
    } else if (prompt.contains('expense insight')) {
      return "This expense is within your budget. Consider setting up automatic savings to build your emergency fund.";
    } else if (prompt.contains('budget optimization')) {
      return "Consider reducing dining out expenses and increasing your savings allocation. Your housing costs are well-managed.";
    } else if (prompt.contains('investment advice')) {
      return "Start with a low-cost index fund for long-term growth. Consider increasing your 401(k) contribution by 1% each year.";
    }
    
    return "AI service temporarily unavailable. Please try again later.";
  }

  // Parse tips from AI response
  List<String> _parseTipsFromResponse(String response) {
    try {
      final data = jsonDecode(response);
      if (data is List) {
        return data.cast<String>();
      }
    } catch (e) {
      // Fallback parsing
    }
    return _getFallbackTips(['Food', 'Transportation']);
  }

  // Parse insight from AI response
  String _parseInsightFromResponse(String response) {
    if (response.contains('AI service temporarily unavailable')) {
      return _getFallbackInsight('Food', 25.0, 500.0, 300.0);
    }
    return response;
  }

  // Parse optimization from AI response
  String _parseOptimizationFromResponse(String response) {
    if (response.contains('AI service temporarily unavailable')) {
      return _getFallbackOptimization(5000.0, 4000.0);
    }
    return response;
  }

  // Parse investment advice from AI response
  String _parseInvestmentAdviceFromResponse(String response) {
    if (response.contains('AI service temporarily unavailable')) {
      return _getFallbackInvestmentAdvice(5000.0, 10000.0, 30);
    }
    return response;
  }

  // Fallback tips when AI is unavailable
  List<String> _getFallbackTips(List<String> categories) {
    final tips = [
      "Track your spending daily to identify unnecessary expenses",
      "Set up automatic transfers to savings on payday",
      "Use the 50/30/20 budgeting rule",
      "Cook meals at home to save on food costs",
      "Review and cancel unused subscriptions monthly",
    ];

    // Add category-specific tips
    if (categories.contains('Food')) {
      tips.add("Plan meals weekly and buy groceries in bulk");
    }
    if (categories.contains('Transportation')) {
      tips.add("Consider carpooling or public transportation to save on fuel");
    }
    if (categories.contains('Entertainment')) {
      tips.add("Look for free or low-cost entertainment options in your area");
    }

    return tips.take(5).toList();
  }

  // Fallback insight
  String _getFallbackInsight(String category, double amount, double budget, double spent) {
    final percentage = (spent / budget) * 100;
    
    if (percentage > 90) {
      return "⚠️ This expense brings you close to your budget limit. Consider reducing spending in this category.";
    } else if (percentage > 70) {
      return "📊 You're on track with your budget. This expense is reasonable for your spending plan.";
    } else {
      return "✅ Great job staying within budget! You have room for additional expenses in this category.";
    }
  }

  // Fallback optimization
  String _getFallbackOptimization(double income, double expenses) {
    final savingsRate = ((income - expenses) / income) * 100;
    
    if (savingsRate < 10) {
      return "Consider reducing non-essential expenses to increase your savings rate. Aim for at least 20% savings.";
    } else if (savingsRate < 20) {
      return "Good savings rate! Consider increasing your emergency fund and retirement contributions.";
    } else {
      return "Excellent savings rate! Consider investing additional funds for long-term wealth building.";
    }
  }

  // Fallback investment advice
  String _getFallbackInvestmentAdvice(double income, double savings, int age) {
    if (age < 30) {
      return "Focus on growth investments like index funds. Start with 10-15% of your income for retirement.";
    } else if (age < 50) {
      return "Balance growth and stability. Consider a mix of stocks and bonds based on your risk tolerance.";
    } else {
      return "Prioritize capital preservation. Consider more conservative investments as you approach retirement.";
    }
  }
}

// Provider for AI service
final aiServiceProvider = Provider<AIService>((ref) {
  return AIService();
});

// Provider for saving tips
final savingTipsProvider = FutureProvider.family<List<String>, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getSavingTips(
    monthlyIncome: params['monthlyIncome'] ?? 0.0,
    monthlyExpenses: params['monthlyExpenses'] ?? 0.0,
    topCategories: params['topCategories'] ?? [],
    savingsGoal: params['savingsGoal'] ?? 0.0,
  );
});

// Provider for expense insight
final expenseInsightProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getExpenseInsight(
    category: params['category'] ?? '',
    amount: params['amount'] ?? 0.0,
    description: params['description'] ?? '',
    monthlyBudget: params['monthlyBudget'] ?? 0.0,
    spentThisMonth: params['spentThisMonth'] ?? 0.0,
  );
});

// Provider for budget optimization
final budgetOptimizationProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getBudgetOptimization(
    categorySpending: params['categorySpending'] ?? {},
    totalIncome: params['totalIncome'] ?? 0.0,
    totalExpenses: params['totalExpenses'] ?? 0.0,
  );
});

// Provider for investment advice
final investmentAdviceProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getInvestmentAdvice(
    monthlyIncome: params['monthlyIncome'] ?? 0.0,
    monthlyExpenses: params['monthlyExpenses'] ?? 0.0,
    currentSavings: params['currentSavings'] ?? 0.0,
    age: params['age'] ?? 30,
    riskTolerance: params['riskTolerance'] ?? 'moderate',
  );
}); 