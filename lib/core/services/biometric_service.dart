import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

class BiometricService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  // Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      final isDeviceSupported = await _localAuth.isDeviceSupported();
      return isAvailable && isDeviceSupported;
    } catch (e) {
      print('Biometric availability check error: $e');
      return false;
    }
  }

  // Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      print('Get available biometrics error: $e');
      return [];
    }
  }

  // Check if PIN is set
  Future<bool> isPinSet() async {
    try {
      final pin = await _secureStorage.read(key: 'user_pin');
      return pin != null && pin.isNotEmpty;
    } catch (e) {
      print('Check PIN error: $e');
      return false;
    }
  }

  // Check if biometric is enabled
  Future<bool> isBiometricEnabled() async {
    try {
      final enabled = await _secureStorage.read(key: 'biometric_enabled');
      return enabled == 'true';
    } catch (e) {
      print('Check biometric enabled error: $e');
      return false;
    }
  }

  // Set PIN
  Future<bool> setPin(String pin) async {
    try {
      await _secureStorage.write(key: 'user_pin', value: pin);
      return true;
    } catch (e) {
      print('Set PIN error: $e');
      return false;
    }
  }

  // Enable biometric authentication
  Future<bool> enableBiometric() async {
    try {
      final isAvailable = await isBiometricAvailable();
      if (!isAvailable) {
        throw Exception('Biometric authentication is not available on this device.');
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to enable biometric login',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (authenticated) {
        await _secureStorage.write(key: 'biometric_enabled', value: 'true');
        return true;
      }
      return false;
    } catch (e) {
      print('Enable biometric error: $e');
      return false;
    }
  }

  // Disable biometric authentication
  Future<bool> disableBiometric() async {
    try {
      await _secureStorage.delete(key: 'biometric_enabled');
      return true;
    } catch (e) {
      print('Disable biometric error: $e');
      return false;
    }
  }

  // Authenticate with PIN
  Future<bool> authenticateWithPin(String pin) async {
    try {
      final storedPin = await _secureStorage.read(key: 'user_pin');
      return storedPin == pin;
    } catch (e) {
      print('PIN authentication error: $e');
      return false;
    }
  }

  // Authenticate with biometric
  Future<bool> authenticateWithBiometric() async {
    try {
      final isEnabled = await isBiometricEnabled();
      if (!isEnabled) {
        throw Exception('Biometric authentication is not enabled.');
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to access the app',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      return authenticated;
    } catch (e) {
      print('Biometric authentication error: $e');
      return false;
    }
  }

  // Change PIN
  Future<bool> changePin(String currentPin, String newPin) async {
    try {
      final isCurrentPinCorrect = await authenticateWithPin(currentPin);
      if (!isCurrentPinCorrect) {
        throw Exception('Current PIN is incorrect.');
      }

      await _secureStorage.write(key: 'user_pin', value: newPin);
      return true;
    } catch (e) {
      print('Change PIN error: $e');
      return false;
    }
  }

  // Remove PIN
  Future<bool> removePin(String currentPin) async {
    try {
      final isCurrentPinCorrect = await authenticateWithPin(currentPin);
      if (!isCurrentPinCorrect) {
        throw Exception('Current PIN is incorrect.');
      }

      await _secureStorage.delete(key: 'user_pin');
      await _secureStorage.delete(key: 'biometric_enabled');
      return true;
    } catch (e) {
      print('Remove PIN error: $e');
      return false;
    }
  }

  // Get authentication method (PIN or biometric)
  Future<String> getAuthenticationMethod() async {
    try {
      final isPinSet = await this.isPinSet();
      final isBiometricEnabled = await this.isBiometricEnabled();

      if (isBiometricEnabled) {
        return 'biometric';
      } else if (isPinSet) {
        return 'pin';
      } else {
        return 'none';
      }
    } catch (e) {
      print('Get authentication method error: $e');
      return 'none';
    }
  }
} 