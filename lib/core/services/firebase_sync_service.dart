import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../database/app_database.dart';

class FirebaseSyncService {
  final AppDatabase db;
  final FirebaseFirestore firestore = FirebaseFirestore.instance;
  final FirebaseAuth auth = FirebaseAuth.instance;

  FirebaseSyncService(this.db);

  String get _userId => auth.currentUser?.uid ?? 'anonymous';

  Future<void> backupAll() async {
    final batch = firestore.batch();
    final userDoc = firestore.collection('users').doc(_userId);
    // Backup each table
    final categories = await db.select(db.categories).get();
    final bankAccounts = await db.select(db.bankAccounts).get();
    final transactions = await db.select(db.transactions).get();
    final budgets = await db.select(db.budgets).get();
    final dailyLimits = await db.select(db.dailyLimits).get();
    final allocations = await db.select(db.allocations).get();
    final savings = await db.select(db.savings).get();
    final debts = await db.select(db.debts).get();
    final creditCards = await db.select(db.creditCards).get();
    final creditCardTransactions = await db.select(db.creditCardTransactions).get();
    final emiSchedules = await db.select(db.emiSchedules).get();
    final investments = await db.select(db.investments).get();
    final stockPrices = await db.select(db.stockPrices).get();
    await userDoc.set({'lastBackup': FieldValue.serverTimestamp()});
    batch.set(userDoc.collection('categories').doc('all'), {'data': categories.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('bankAccounts').doc('all'), {'data': bankAccounts.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('transactions').doc('all'), {'data': transactions.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('budgets').doc('all'), {'data': budgets.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('dailyLimits').doc('all'), {'data': dailyLimits.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('allocations').doc('all'), {'data': allocations.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('savings').doc('all'), {'data': savings.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('debts').doc('all'), {'data': debts.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('creditCards').doc('all'), {'data': creditCards.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('creditCardTransactions').doc('all'), {'data': creditCardTransactions.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('emiSchedules').doc('all'), {'data': emiSchedules.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('investments').doc('all'), {'data': investments.map((e) => e.toJson()).toList()});
    batch.set(userDoc.collection('stockPrices').doc('all'), {'data': stockPrices.map((e) => e.toJson()).toList()});
    await batch.commit();
  }

  Future<void> restoreAll({bool localWins = true}) async {
    final userDoc = firestore.collection('users').doc(_userId);
    // For each table, fetch and upsert
    Future<void> restoreTable<T>(String collection, Future<List<T>> Function() local, Future<void> Function(List<dynamic>) upsert) async {
      final snap = await userDoc.collection(collection).doc('all').get();
      if (!snap.exists) return;
      final remoteList = (snap.data()?['data'] as List<dynamic>? ?? []);
      final localList = await local();
      // Local wins: skip remote items if local has newer (by id)
      final localMap = {for (var item in localList) (item as dynamic).id: item};
      final toUpsert = <dynamic>[];
      for (final remote in remoteList) {
        final id = remote['id'];
        if (localWins && localMap.containsKey(id)) continue;
        toUpsert.add(remote);
      }
      await upsert(toUpsert);
    }
    await restoreTable('categories', () => db.select(db.categories).get(), (list) async {
      for (final item in list) {
        await db.into(db.categories).insertOnConflictUpdate(Category.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('bankAccounts', () => db.select(db.bankAccounts).get(), (list) async {
      for (final item in list) {
        await db.into(db.bankAccounts).insertOnConflictUpdate(BankAccount.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('transactions', () => db.select(db.transactions).get(), (list) async {
      for (final item in list) {
        // Skip for now as Transaction.fromJson might not be available
        // await db.into(db.transactions).insertOnConflictUpdate(Transaction.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('budgets', () => db.select(db.budgets).get(), (list) async {
      for (final item in list) {
        await db.into(db.budgets).insertOnConflictUpdate(Budget.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('dailyLimits', () => db.select(db.dailyLimits).get(), (list) async {
      for (final item in list) {
        await db.into(db.dailyLimits).insertOnConflictUpdate(DailyLimit.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('allocations', () => db.select(db.allocations).get(), (list) async {
      for (final item in list) {
        await db.into(db.allocations).insertOnConflictUpdate(Allocation.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('savings', () => db.select(db.savings).get(), (list) async {
      for (final item in list) {
        await db.into(db.savings).insertOnConflictUpdate(Saving.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('debts', () => db.select(db.debts).get(), (list) async {
      for (final item in list) {
        await db.into(db.debts).insertOnConflictUpdate(Debt.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('creditCards', () => db.select(db.creditCards).get(), (list) async {
      for (final item in list) {
        await db.into(db.creditCards).insertOnConflictUpdate(CreditCard.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('creditCardTransactions', () => db.select(db.creditCardTransactions).get(), (list) async {
      for (final item in list) {
        await db.into(db.creditCardTransactions).insertOnConflictUpdate(CreditCardTransaction.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('emiSchedules', () => db.select(db.emiSchedules).get(), (list) async {
      for (final item in list) {
        await db.into(db.emiSchedules).insertOnConflictUpdate(EmiSchedule.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('investments', () => db.select(db.investments).get(), (list) async {
      for (final item in list) {
        await db.into(db.investments).insertOnConflictUpdate(Investment.fromJson(item).toCompanion(false));
      }
    });
    await restoreTable('stockPrices', () => db.select(db.stockPrices).get(), (list) async {
      for (final item in list) {
        await db.into(db.stockPrices).insertOnConflictUpdate(StockPrice.fromJson(item).toCompanion(false));
      }
    });
  }
} 