
class SmsTransactionData {
  final double amount;
  final String? vpa;
  final String bankName;
  final String? accountNumber;
  final DateTime transactionDate;
  final String transactionType;
  final String? refNumber;
  final String originalMessage;

  SmsTransactionData({
    required this.amount,
    this.vpa,
    required this.bankName,
    this.accountNumber,
    required this.transactionDate,
    required this.transactionType,
    this.refNumber,
    required this.originalMessage,
  });
}

class SmsParserService {
  static SmsTransactionData? parseUpiTransaction(String message) {
    try {
      // Common UPI transaction patterns
      final patterns = [
        // Pattern 1: Rs 152.25 debited via UPI on 06-07-2025 11:06:29 <NAME_EMAIL> No ************.Small txns?Use UPI Lite!-Federal Bank
        RegExp(r'Rs\s+(\d+(?:\.\d{2})?)\s+(debited|credited)\s+via\s+UPI\s+on\s+(\d{2}-\d{2}-\d{4})\s+(\d{2}:\d{2}:\d{2})\s+to\s+VPA\s+([^\.]+)\.Ref\s+No\s+(\d+)\.(.+?)-(.+?)$', caseSensitive: false),
        
        // Pattern 2: Rs. 500 debited from A/c XX1234 on 15-12-2024 14:30:15 for UPI/**********@icici to merchant@upi. Ref No: ********** - HDFC Bank
        RegExp(r'Rs\.?\s+(\d+(?:\.\d{2})?)\s+(debited|credited)\s+from\s+A/c\s+([A-Z0-9]+)\s+on\s+(\d{2}-\d{2}-\d{4})\s+(\d{2}:\d{2}:\d{2})\s+for\s+UPI/([^@]+@[^\.]+)\.?\s*Ref\s+No:?\s*(\d+)\s*-\s*(.+?)$', caseSensitive: false),
        
        // Pattern 3: UPI Transaction: Rs. 1000 debited on 20-12-2024 16:45:30 to **********@okicici. Ref: ********** - ICICI Bank
        RegExp(r'UPI\s+Transaction:\s+Rs\.?\s+(\d+(?:\.\d{2})?)\s+(debited|credited)\s+on\s+(\d{2}-\d{2}-\d{4})\s+(\d{2}:\d{2}:\d{2})\s+to\s+([^\.]+)\.\s*Ref:?\s*(\d+)\s*-\s*(.+?)$', caseSensitive: false),
        
        // Pattern 4: Rs 250.00 debited from A/c XX5678 on 10-12-2024 12:15:45 for UPI/**********@paytm to merchant@paytm. Ref No: ********** - SBI
        RegExp(r'Rs\s+(\d+(?:\.\d{2})?)\s+(debited|credited)\s+from\s+A/c\s+([A-Z0-9]+)\s+on\s+(\d{2}-\d{2}-\d{4})\s+(\d{2}:\d{2}:\d{2})\s+for\s+UPI/([^@]+@[^\.]+)\.?\s*Ref\s+No:?\s*(\d+)\s*-\s*(.+?)$', caseSensitive: false),
      ];

      for (final pattern in patterns) {
        final match = pattern.firstMatch(message);
        if (match != null) {
          final amount = double.tryParse(match.group(1)?.replaceAll(',', '') ?? '') ?? 0.0;
          final transactionType = match.group(2)?.toLowerCase() ?? 'debit';
          final dateStr = match.group(3) ?? '';
          final timeStr = match.group(4) ?? '';
          final vpa = match.group(5)?.trim() ?? '';
          final refNumber = match.group(6) ?? '';
          final bankName = match.group(7)?.trim() ?? '';
          final accountNumber = match.group(3) ?? ''; // Some patterns have account number

          // Parse date and time
          final dateTime = _parseDateTime(dateStr, timeStr);
          if (dateTime == null) continue;

          return SmsTransactionData(
            amount: amount,
            vpa: vpa.isNotEmpty ? vpa : null,
            bankName: bankName,
            accountNumber: accountNumber.isNotEmpty ? accountNumber : null,
            transactionDate: dateTime,
            transactionType: transactionType,
            refNumber: refNumber.isNotEmpty ? refNumber : null,
            originalMessage: message,
          );
        }
      }

      return null;
    } catch (e) {
      print('Error parsing SMS: $e');
      return null;
    }
  }

  static DateTime? _parseDateTime(String dateStr, String timeStr) {
    try {
      // Parse date in DD-MM-YYYY format
      final dateParts = dateStr.split('-');
      if (dateParts.length != 3) return null;

      final day = int.tryParse(dateParts[0]) ?? 1;
      final month = int.tryParse(dateParts[1]) ?? 1;
      final year = int.tryParse(dateParts[2]) ?? DateTime.now().year;

      // Parse time in HH:MM:SS format
      final timeParts = timeStr.split(':');
      if (timeParts.length != 3) return null;

      final hour = int.tryParse(timeParts[0]) ?? 0;
      final minute = int.tryParse(timeParts[1]) ?? 0;
      final second = int.tryParse(timeParts[2]) ?? 0;

      return DateTime(year, month, day, hour, minute, second);
    } catch (e) {
      print('Error parsing date/time: $e');
      return null;
    }
  }

  static bool isUpiTransaction(String message) {
    final upiKeywords = [
      'UPI',
      'upi',
      'VPA',
      'vpa',
      'debited via UPI',
      'credited via UPI',
      'UPI Transaction',
    ];

    return upiKeywords.any((keyword) => message.contains(keyword));
  }

  static String extractBankName(String message) {
    // Common bank names and their variations
    final bankPatterns = {
      'HDFC': ['HDFC', 'HDFC Bank'],
      'ICICI': ['ICICI', 'ICICI Bank'],
      'SBI': ['SBI', 'State Bank of India'],
      'Axis': ['Axis', 'Axis Bank'],
      'Kotak': ['Kotak', 'Kotak Bank'],
      'Federal': ['Federal', 'Federal Bank'],
      'Yes Bank': ['Yes Bank'],
      'IDFC': ['IDFC', 'IDFC Bank'],
      'RBL': ['RBL', 'RBL Bank'],
      'Paytm': ['Paytm', 'Paytm Bank'],
      'PhonePe': ['PhonePe'],
      'Google Pay': ['Google Pay', 'GPay'],
    };

    for (final entry in bankPatterns.entries) {
      for (final pattern in entry.value) {
        if (message.contains(pattern)) {
          return entry.key;
        }
      }
    }

    return 'Unknown Bank';
  }
} 