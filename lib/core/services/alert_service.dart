import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../database/app_database.dart';
import '../database/database_provider.dart';

class AlertService {
  final AppDatabase _database;

  AlertService(this._database);

  // Check budget alerts
  Future<List<BudgetAlert>> checkBudgetAlerts() async {
    final budgets = await _database.select(_database.budgets).get();
    final alerts = <BudgetAlert>[];

    for (final budget in budgets) {
      final spentPercentage = (budget.actualSpent / budget.budgetedAmount) * 100;
      
      if (spentPercentage >= 80 && spentPercentage < 100) {
        alerts.add(BudgetAlert(
          id: 'budget_${budget.id}',
          title: 'Budget Alert',
          message: 'You\'ve spent ${spentPercentage.toStringAsFixed(1)}% of your budget for this month',
          severity: AlertSeverity.warning,
          dueDate: DateTime.now(),
          isRead: false,
        ));
      } else if (spentPercentage >= 100) {
        alerts.add(BudgetAlert(
          id: 'budget_${budget.id}_exceeded',
          title: 'Budget Exceeded',
          message: 'You\'ve exceeded your budget by ${(spentPercentage - 100).toStringAsFixed(1)}%',
          severity: AlertSeverity.critical,
          dueDate: DateTime.now(),
          isRead: false,
        ));
      }
    }

    return alerts;
  }

  // Check EMI due alerts
  Future<List<EmiAlert>> checkEmiDueAlerts() async {
    final emiSchedules = await _database.select(_database.emiSchedules).get();
    final alerts = <EmiAlert>[];

    final now = DateTime.now();
    
    for (final emi in emiSchedules) {
      if (emi.isPaid) continue;
      
      final daysUntilDue = emi.dueDate.difference(now).inDays;
      
      if (daysUntilDue <= 0) {
        // Overdue
        alerts.add(EmiAlert(
          id: 'emi_${emi.id}_overdue',
          title: 'EMI Overdue',
          message: 'EMI payment of ₹${emi.monthlyPayment.toStringAsFixed(2)} is overdue',
          severity: AlertSeverity.critical,
          dueDate: emi.dueDate,
          emiId: emi.id,
          amount: emi.monthlyPayment,
          isRead: false,
        ));
      } else if (daysUntilDue <= 3) {
        // Due in 3 days
        alerts.add(EmiAlert(
          id: 'emi_${emi.id}_due_soon',
          title: 'EMI Due Soon',
          message: 'EMI payment of ₹${emi.monthlyPayment.toStringAsFixed(2)} is due in $daysUntilDue days',
          severity: AlertSeverity.warning,
          dueDate: emi.dueDate,
          emiId: emi.id,
          amount: emi.monthlyPayment,
          isRead: false,
        ));
      } else if (daysUntilDue <= 7) {
        // Due in a week
        alerts.add(EmiAlert(
          id: 'emi_${emi.id}_due_week',
          title: 'EMI Due This Week',
          message: 'EMI payment of ₹${emi.monthlyPayment.toStringAsFixed(2)} is due in $daysUntilDue days',
          severity: AlertSeverity.info,
          dueDate: emi.dueDate,
          emiId: emi.id,
          amount: emi.monthlyPayment,
          isRead: false,
        ));
      }
    }

    return alerts;
  }

  // Check credit card due alerts
  Future<List<CreditCardAlert>> checkCreditCardDueAlerts() async {
    final creditCards = await _database.select(_database.creditCards).get();
    final alerts = <CreditCardAlert>[];

    final now = DateTime.now();
    
    for (final card in creditCards) {
      final daysUntilDue = card.dueDate.difference(now).inDays;
      
      if (daysUntilDue <= 0) {
        // Overdue
        alerts.add(CreditCardAlert(
          id: 'cc_${card.id}_overdue',
          title: 'Credit Card Payment Overdue',
          message: 'Payment for ${card.bankName} card is overdue',
          severity: AlertSeverity.critical,
          dueDate: card.dueDate,
          cardId: card.id,
          amount: card.outstandingBalance,
          isRead: false,
        ));
      } else if (daysUntilDue <= 3) {
        // Due in 3 days
        alerts.add(CreditCardAlert(
          id: 'cc_${card.id}_due_soon',
          title: 'Credit Card Payment Due Soon',
          message: 'Payment for ${card.bankName} card is due in $daysUntilDue days',
          severity: AlertSeverity.warning,
          dueDate: card.dueDate,
          cardId: card.id,
          amount: card.outstandingBalance,
          isRead: false,
        ));
      } else if (daysUntilDue <= 7) {
        // Due in a week
        alerts.add(CreditCardAlert(
          id: 'cc_${card.id}_due_week',
          title: 'Credit Card Payment Due This Week',
          message: 'Payment for ${card.bankName} card is due in $daysUntilDue days',
          severity: AlertSeverity.info,
          dueDate: card.dueDate,
          cardId: card.id,
          amount: card.outstandingBalance,
          isRead: false,
        ));
      }
    }

    return alerts;
  }

  // Check debt due alerts
  Future<List<DebtAlert>> checkDebtDueAlerts() async {
    final debts = await _database.select(_database.debts).get();
    final alerts = <DebtAlert>[];

    final now = DateTime.now();
    
    for (final debt in debts) {
      if (!debt.isActive) continue;
      
      final daysUntilDue = debt.dueDate.difference(now).inDays;
      
      if (daysUntilDue <= 0) {
        // Overdue
        alerts.add(DebtAlert(
          id: 'debt_${debt.id}_overdue',
          title: 'Debt Payment Overdue',
          message: 'Payment for ${debt.loanName} is overdue',
          severity: AlertSeverity.critical,
          dueDate: debt.dueDate,
          debtId: debt.id,
          amount: debt.currentBalance,
          isRead: false,
        ));
      } else if (daysUntilDue <= 3) {
        // Due in 3 days
        alerts.add(DebtAlert(
          id: 'debt_${debt.id}_due_soon',
          title: 'Debt Payment Due Soon',
          message: 'Payment for ${debt.loanName} is due in $daysUntilDue days',
          severity: AlertSeverity.warning,
          dueDate: debt.dueDate,
          debtId: debt.id,
          amount: debt.currentBalance,
          isRead: false,
        ));
      } else if (daysUntilDue <= 7) {
        // Due in a week
        alerts.add(DebtAlert(
          id: 'debt_${debt.id}_due_week',
          title: 'Debt Payment Due This Week',
          message: 'Payment for ${debt.loanName} is due in $daysUntilDue days',
          severity: AlertSeverity.info,
          dueDate: debt.dueDate,
          debtId: debt.id,
          amount: debt.currentBalance,
          isRead: false,
        ));
      }
    }

    return alerts;
  }

  // Get all alerts
  Future<List<BaseAlert>> getAllAlerts() async {
    final budgetAlerts = await checkBudgetAlerts();
    final emiAlerts = await checkEmiDueAlerts();
    final creditCardAlerts = await checkCreditCardDueAlerts();
    final debtAlerts = await checkDebtDueAlerts();

    final allAlerts = <BaseAlert>[];
    allAlerts.addAll(budgetAlerts);
    allAlerts.addAll(emiAlerts);
    allAlerts.addAll(creditCardAlerts);
    allAlerts.addAll(debtAlerts);

    // Sort by severity and due date
    allAlerts.sort((a, b) {
      if (a.severity.index != b.severity.index) {
        return b.severity.index.compareTo(a.severity.index);
      }
      return a.dueDate.compareTo(b.dueDate);
    });

    return allAlerts;
  }

  // Mark alert as read
  Future<void> markAlertAsRead(String alertId) async {
    // In a real app, you would store this in a database
    // For now, we'll just return
  }

  // Mark all alerts as read
  Future<void> markAllAlertsAsRead() async {
    // In a real app, you would update the database
    // For now, we'll just return
  }
}

// Alert base class
abstract class BaseAlert {
  final String id;
  final String title;
  final String message;
  final AlertSeverity severity;
  final DateTime dueDate;
  final bool isRead;

  BaseAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.severity,
    required this.dueDate,
    this.isRead = false,
  });
}

// Budget alert
class BudgetAlert extends BaseAlert {
  BudgetAlert({
    required super.id,
    required super.title,
    required super.message,
    required super.severity,
    required super.dueDate,
    super.isRead,
  });
}

// EMI alert
class EmiAlert extends BaseAlert {
  final int emiId;
  final double amount;

  EmiAlert({
    required super.id,
    required super.title,
    required super.message,
    required super.severity,
    required super.dueDate,
    required this.emiId,
    required this.amount,
    super.isRead,
  });
}

// Credit card alert
class CreditCardAlert extends BaseAlert {
  final int cardId;
  final double amount;

  CreditCardAlert({
    required super.id,
    required super.title,
    required super.message,
    required super.severity,
    required super.dueDate,
    required this.cardId,
    required this.amount,
    super.isRead,
  });
}

// Debt alert
class DebtAlert extends BaseAlert {
  final int debtId;
  final double amount;

  DebtAlert({
    required super.id,
    required super.title,
    required super.message,
    required super.severity,
    required super.dueDate,
    required this.debtId,
    required this.amount,
    super.isRead,
  });
}

// Alert severity levels
enum AlertSeverity {
  info,      // Blue - informational
  warning,   // Orange - warning
  critical,  // Red - critical/urgent
}

// Provider for alert service
final alertServiceProvider = Provider<AlertService>((ref) {
  final database = ref.watch(databaseProvider);
  return AlertService(database);
});

// Provider for all alerts
final allAlertsProvider = FutureProvider<List<BaseAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.getAllAlerts();
});

// Provider for unread alerts count
final unreadAlertsCountProvider = FutureProvider<int>((ref) async {
  final alerts = await ref.watch(allAlertsProvider.future);
  return alerts.where((alert) => !alert.isRead).length;
}); 