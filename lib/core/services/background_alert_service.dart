import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'alert_service.dart';
import 'notification_service.dart';

class BackgroundAlertService {
  static final BackgroundAlertService _instance = BackgroundAlertService._internal();
  factory BackgroundAlertService() => _instance;
  BackgroundAlertService._internal();

  Timer? _timer;
  final NotificationService _notificationService = NotificationService();

  // Start background alert checking
  void startAlertChecking() {
    // Check every hour
    _timer = Timer.periodic(const Duration(hours: 1), (timer) {
      _checkAlerts();
    });
    
    // Also check immediately when started
    _checkAlerts();
  }

  // Stop background alert checking
  void stopAlertChecking() {
    _timer?.cancel();
    _timer = null;
  }

  // Check for alerts and schedule notifications
  Future<void> _checkAlerts() async {
    try {
      // Create a temporary provider container to access the alert service
      final container = ProviderContainer();
      final alertService = container.read(alertServiceProvider);
      
      // Check budget alerts
      final budgetAlerts = await alertService.checkBudgetAlerts();
      for (final alert in budgetAlerts) {
        await _notificationService.showImmediateNotification(
          title: alert.title,
          body: alert.message,
          priority: alert.severity == AlertSeverity.critical ? 1 : 2,
        );
      }

      // Check EMI alerts
      final emiAlerts = await alertService.checkEmiDueAlerts();
      for (final alert in emiAlerts) {
        await _notificationService.showImmediateNotification(
          title: alert.title,
          body: alert.message,
          priority: alert.severity == AlertSeverity.critical ? 1 : 2,
        );
      }

      // Check credit card alerts
      final creditCardAlerts = await alertService.checkCreditCardDueAlerts();
      for (final alert in creditCardAlerts) {
        await _notificationService.showImmediateNotification(
          title: alert.title,
          body: alert.message,
          priority: alert.severity == AlertSeverity.critical ? 1 : 2,
        );
      }

      // Check debt alerts
      final debtAlerts = await alertService.checkDebtDueAlerts();
      for (final alert in debtAlerts) {
        await _notificationService.showImmediateNotification(
          title: alert.title,
          body: alert.message,
          priority: alert.severity == AlertSeverity.critical ? 1 : 2,
        );
      }

      container.dispose();
    } catch (e) {
      print('Error checking alerts: $e');
    }
  }

  // Schedule alerts for specific items
  Future<void> scheduleBudgetAlert({
    required String budgetName,
    required double spentAmount,
    required double budgetAmount,
    required DateTime dueDate,
  }) async {
    await _notificationService.scheduleBudgetAlert(
      budgetName: budgetName,
      spentAmount: spentAmount,
      budgetAmount: budgetAmount,
      dueDate: dueDate,
    );
  }

  Future<void> scheduleEmiAlert({
    required String emiName,
    required double amount,
    required DateTime dueDate,
  }) async {
    await _notificationService.scheduleEmiAlert(
      emiName: emiName,
      amount: amount,
      dueDate: dueDate,
    );
  }

  Future<void> scheduleCreditCardAlert({
    required String cardName,
    required double amount,
    required DateTime dueDate,
  }) async {
    await _notificationService.scheduleCreditCardAlert(
      cardName: cardName,
      amount: amount,
      dueDate: dueDate,
    );
  }

  Future<void> scheduleDebtAlert({
    required String debtName,
    required double amount,
    required DateTime dueDate,
  }) async {
    await _notificationService.scheduleDebtAlert(
      debtName: debtName,
      amount: amount,
      dueDate: dueDate,
    );
  }

  // Schedule recurring reminders
  Future<void> scheduleDailyExpenseReminder() async {
    await _notificationService.scheduleDailyExpenseReminder();
  }

  Future<void> scheduleWeeklyBudgetReview() async {
    await _notificationService.scheduleWeeklyBudgetReview();
  }

  // Cancel all scheduled notifications
  Future<void> cancelAllNotifications() async {
    await _notificationService.cancelAllNotifications();
  }
}

// Provider for background alert service
final backgroundAlertServiceProvider = Provider<BackgroundAlertService>((ref) {
  return BackgroundAlertService();
}); 