import '../../core/database/app_database.dart';

abstract class AllocationRepository {
  Future<List<Allocation>> getAllocations();
  Future<void> addAllocation(AllocationsCompanion allocation);
  Future<void> updateAllocation(int id, AllocationsCompanion allocation);
  Future<void> deleteAllocation(int id);
}

class AllocationWithCategory {
  final Allocation allocation;
  final Category category;
  AllocationWithCategory(this.allocation, this.category);
} 