import '../../core/database/app_database.dart';

abstract class BankAccountRepository {
  Future<List<BankAccount>> getBankAccounts();
  Future<BankAccount?> getBankAccountById(int id);
  Future<int> addBankAccount(BankAccountsCompanion account);
  Future<void> updateBankAccount(int id, BankAccountsCompanion account);
  Future<void> deleteBankAccount(int id);
}

extension BankAccountX on BankAccount {
  String get name => bankName;
  String get type => accountType;
  String get currency => 'INR'; // Default currency
} 