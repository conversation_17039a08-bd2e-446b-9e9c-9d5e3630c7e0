import '../../core/database/app_database.dart';

abstract class BudgetRepository {
  Future<List<BudgetWithCategory>> getBudgets();
  Future<BudgetWithCategory?> getBudgetById(int id);
  Future<int> addBudget(BudgetsCompanion budget);
  Future<void> updateBudget(int id, BudgetsCompanion budget);
  Future<void> deleteBudget(int id);
}

class BudgetWithCategory {
  final Budget budget;
  final Category category;
  BudgetWithCategory(this.budget, this.category);
}

extension BudgetWithCategoryX on BudgetWithCategory {
  double get budgetedAmount => budget.budgetedAmount ?? 0;
  double get actualSpent => budget.actualSpent ?? 0;
  int get month => budget.month ?? 0;
  String get categoryName => category.name;
} 