import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';

abstract class CreditCardRepository {
  Future<List<CreditCard>> getAllCreditCards();
  Future<CreditCard?> getCreditCardById(int id);
  Future<int> addCreditCard(CreditCardsCompanion card);
  Future<bool> updateCreditCard(CreditCardsCompanion card);
  Future<int> deleteCreditCard(int id);
  
  // Transaction management
  Future<List<CreditCardTransaction>> getTransactionsByCardId(int cardId);
  Future<List<CreditCardTransaction>> getEmiTransactionsByCardId(int cardId);
  Future<int> addTransaction(CreditCardTransactionsCompanion transaction);
  Future<bool> updateTransaction(CreditCardTransactionsCompanion transaction);
  Future<int> deleteTransaction(int id);
  
  // Payment management
  // Future<List<CreditCardPayment>> getPaymentsByCardId(int cardId);
  // Future<int> addPayment(CreditCardPaymentsCompanion payment);
  // Future<bool> updatePayment(CreditCardPaymentsCompanion payment);
  // Future<int> deletePayment(int id);
  
  // EMI management
  Future<List<EmiSchedule>> getEmiSchedulesByTransactionId(int transactionId);
  Future<List<EmiSchedule>> getActiveEmiSchedules();
  Future<bool> updateEmiSchedule(EmiSchedulesCompanion schedule);
  
  // Summary and analytics
  Future<Map<String, dynamic>> getCreditCardSummary(int cardId);
  Future<Map<String, dynamic>> getCreditCardAnalytics(int cardId);
  
  // Loan calculations
  Future<Map<String, dynamic>> calculateEmiForTransaction({
    required double amount,
    required double interestRate,
    required int months,
    required double processingFee,
    required double gstRate,
  });
  
  Future<List<Map<String, dynamic>>> generateEmiSchedule({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  });
  
  // Credit card overview
  Future<List<CreditCard>> getActiveCreditCards();
  Future<double> getTotalCreditLimit();
  Future<double> getTotalOutstandingBalance();

  Future<List<CreditCard>> getCreditCards();
}

class CreditCardWithTransactions {
  final CreditCard card;
  final List<CreditCardTransaction> transactions;
  CreditCardWithTransactions(this.card, this.transactions);
}

extension CreditCardX on CreditCard {
  double get outstandingBalance => this.outstandingBalance;
  double get utilizationRate => this.outstandingBalance / (creditLimit > 0 ? creditLimit : 1);
}

// No extension needed if balance/currentBalance does not exist on CreditCard. 