import '../../core/database/app_database.dart';

abstract class IncomeRepository {
  Future<List<Transaction>> getIncomes();
  Future<Transaction?> getIncomeById(int id);
  Future<int> addIncome(TransactionsCompanion income);
  Future<void> updateIncome(int id, TransactionsCompanion income);
  Future<void> deleteIncome(int id);
}

extension IncomeX on Transaction {
  double get amount => this.amount ?? 0;
  DateTime get date => this.date ?? DateTime.now();
} 