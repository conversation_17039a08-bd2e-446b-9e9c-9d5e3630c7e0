import '../../core/database/app_database.dart';

abstract class DebtRepository {
  Future<List<Debt>> getAllDebts();
  Future<Debt?> getDebtById(int id);
  Future<int> addDebt(DebtsCompanion debt);
  Future<bool> updateDebt(DebtsCompanion debt);
  Future<int> deleteDebt(int id);
  
  // Debt filtering
  Future<List<Debt>> getDebtsByType(String type);
  Future<List<Debt>> getActiveDebts();
  Future<List<Debt>> getLendingDebts();
  Future<List<Debt>> getBorrowingDebts();
  Future<List<Debt>> getDebtsByPartyName(String partyName);
  Future<List<Debt>> getOverdueDebts();
  Future<List<Debt>> getUpcomingDueDebts({int days = 7});
  Future<List<Debt>> getCreditCardLoans();
  
  // Summary and analytics
  Future<Map<String, dynamic>> getDebtSummary(int debtId);
  Future<Map<String, dynamic>> getLoanAnalytics();
  
  // Loan calculations
  Future<Map<String, dynamic>> calculateLoanEmi({
    required String loanType,
    required double principal,
    required double interestRate,
    required int months,
    required String interestType,
    required String interestFrequency,
  });
  
  Future<Map<String, dynamic>> calculateNoCostEmi({
    required double principal,
    required int months,
    required double processingFee,
    required double gstRate,
  });
  
  Future<List<Map<String, dynamic>>> generateEmiSchedule({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  });
  
  Future<Map<String, dynamic>> calculatePrepaymentSavings({
    required double remainingPrincipal,
    required double prepaymentAmount,
    required double interestRate,
    required int remainingMonths,
  });
  
  Future<double> calculateForeclosureCharges({
    required double remainingPrincipal,
    required double foreclosureRate,
  });

  Future<List<Debt>> getDebts();
}

extension DebtX on Debt {
  double get totalAmount => principalAmount + (interestRate * principalAmount / 100);
}

// TODO: Migrate all payment logic to use TransactionsCompanion and Transactions table. Remove all references to DebtPaymentsCompanion and payment methods. 