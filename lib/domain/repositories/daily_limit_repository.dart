import '../../core/database/app_database.dart';

abstract class DailyLimitRepository {
  Future<List<DailyLimit>> getDailyLimits();
  Future<DailyLimit?> getDailyLimitById(int id);
  Future<int> addDailyLimit(DailyLimitsCompanion limit);
  Future<void> updateDailyLimit(int id, DailyLimitsCompanion limit);
  Future<void> deleteDailyLimit(int id);
}

class DailyLimitWithCategory {
  final DailyLimit dailyLimit;
  final Category category;
  DailyLimitWithCategory(this.dailyLimit, this.category);
} 