import '../../core/database/app_database.dart';
import 'transaction_repository.dart';

abstract class ExpenseRepository {
  Future<List<TransactionWithCategory>> getExpenses();
  Future<Transaction?> getExpenseById(int id);
  Future<int> addExpense(TransactionsCompanion expense);
  Future<void> updateExpense(int id, TransactionsCompanion expense);
  Future<void> deleteExpense(int id);
  Future<List<BorrowedContactSummary>> getBorrowedContactsSummary();
  Future<List<ContactSummary>> getAllContactsSummary();
  Future<List<TransactionWithCategory>> getExpensesByAccountId(int accountId);
}

// TransactionWithCategory is now defined in transaction_repository.dart

extension TransactionWithCategoryX on TransactionWithCategory {
  double get amount => transaction.amount ?? 0;
  DateTime get date => transaction.date ?? DateTime.now();
  String get categoryName => category?.name ?? 'Unknown';
} 