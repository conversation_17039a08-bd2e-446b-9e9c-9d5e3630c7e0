import '../../core/database/app_database.dart';

abstract class CreditCardTransactionRepository {
  Future<List<CreditCardTransaction>> getTransactionsForCard(int cardId);
  Future<CreditCardTransaction?> getTransactionById(int id);
  Future<int> addTransaction(CreditCardTransactionsCompanion tx);
  Future<void> updateTransaction(int id, CreditCardTransactionsCompanion tx);
  Future<void> deleteTransaction(int id);
} 