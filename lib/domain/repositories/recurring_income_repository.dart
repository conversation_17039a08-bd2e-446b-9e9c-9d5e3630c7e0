import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';

abstract class RecurringIncomeRepository {
  Future<int> addRecurringIncome(RecurringIncomesCompanion recurringIncome);
  Future<List<RecurringIncome>> getAllRecurringIncomes();
  Future<List<RecurringIncome>> getActiveRecurringIncomes();
  Future<List<RecurringIncome>> getStoppedRecurringIncomes();
  Future<List<RecurringIncome>> getDueRecurringIncomes(DateTime date);
  Future<void> updateRecurringIncome(int id, RecurringIncomesCompanion recurringIncome);
  Future<void> deleteRecurringIncome(int id);
  Future<void> updateLastProcessedDate(int id, DateTime date);
  Future<void> updateNextDueDate(int id, DateTime date);
  Future<RecurringIncome?> getRecurringIncomeById(int id);
} 