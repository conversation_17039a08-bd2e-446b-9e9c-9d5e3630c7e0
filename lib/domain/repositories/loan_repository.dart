import '../../core/database/app_database.dart';

abstract class LoanRepository {
  Future<List<Debt>> getAllLoans();
  Future<Debt?> getLoanById(int id);
  Future<int> addLoan(DebtsCompanion loan);
  Future<bool> updateLoan(int id, DebtsCompanion loan);
  Future<bool> deleteLoan(int id);
  Future<double> getTotalOutstandingBalance();
  Future<Map<String, dynamic>> getLoanStatistics();
}

extension LoanX on Debt {
  double get currentBalance => this.currentBalance;
}

// No extension needed if currentBalance does not exist on Debt.
// TODO: Migrate all payment logic to use TransactionsCompanion and Transactions table. Remove all references to DebtPaymentsCompanion and payment methods. 