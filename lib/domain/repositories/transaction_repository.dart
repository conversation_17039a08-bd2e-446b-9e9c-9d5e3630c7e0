import '../../core/database/app_database.dart';

abstract class TransactionRepository {
  Future<List<TransactionWithCategory>> getTransactions();
  Future<List<TransactionWithCategory>> getTransactionsByType(String type);
  Future<List<TransactionWithCategory>> getTransactionsByAccountId(int accountId);
  Future<Transaction?> getTransactionById(int id);
  Future<int> addTransaction(TransactionsCompanion transaction);
  Future<void> updateTransaction(int id, TransactionsCompanion transaction);
  Future<void> deleteTransaction(int id);
  Future<List<BorrowedContactSummary>> getBorrowedContactsSummary();
  Future<List<ContactSummary>> getAllContactsSummary();
  Future<double> getTotalBalance();
  Future<Map<String, double>> getCategoryTotals();
  Future<List<TransactionWithCategory>> getTransactionsPaginated({int limit = 20, int offset = 0});
}

class TransactionWithCategory {
  final Transaction transaction;
  final Category? category;
  final BankAccount? fromAccount;
  final BankAccount? toAccount;

  TransactionWithCategory({
    required this.transaction,
    this.category,
    this.fromAccount,
    this.toAccount,
  });
}

class BorrowedContactSummary {
  final String? contactId;
  final String? contactName;
  final String? contactPhone;
  final double totalAmount;
  BorrowedContactSummary({this.contactId, this.contactName, this.contactPhone, required this.totalAmount});
}

class ContactSummary {
  final String? contactId;
  final String? contactName;
  final String? contactPhone;
  final double netBalance;
  ContactSummary({this.contactId, this.contactName, this.contactPhone, required this.netBalance});
}

extension TransactionWithCategoryX on TransactionWithCategory {
  double get amount => transaction.amount;
  DateTime get date => transaction.date;
  String get categoryName => category?.name ?? 'Unknown';
  String get type => transaction.type;
} 