class EMI {
  final int id;
  final int debtId;
  final int transactionId;
  final double monthlyPayment;
  final int emiNumber;
  final int totalEmis;
  final int remainingEmis;
  final DateTime dueDate;
  final double principalComponent;
  final double interestComponent;
  final double? processingFee;
  final double? gstAmount;
  final bool isPaid;
  final DateTime? paidDate;
  final String? paymentMethod;
  final int fromAccountId;

  EMI({
    required this.id,
    required this.debtId,
    required this.transactionId,
    required this.monthlyPayment,
    required this.emiNumber,
    required this.totalEmis,
    required this.remainingEmis,
    required this.dueDate,
    required this.principalComponent,
    required this.interestComponent,
    this.processingFee,
    this.gstAmount,
    required this.isPaid,
    this.paidDate,
    this.paymentMethod,
    required this.fromAccountId,
  });

  bool get isOverdue => !isPaid && DateTime.now().isAfter(dueDate);

  // Add fromMap/toMap or fromDb/toDb methods as needed for repository integration
} 