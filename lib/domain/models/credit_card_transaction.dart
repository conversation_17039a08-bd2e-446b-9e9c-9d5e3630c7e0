class CreditCardTransaction {
  final int id;
  final int cardId;
  final double amount;
  final String description;
  final String category;
  final DateTime transactionDate;
  final bool isEmi;
  final int? emiMonths;
  final double? emiInterestRate;
  final double? processingFee;
  final double? gstAmount;
  final String transactionType; // Purchase, Payment, Cash Advance, etc.
  final bool isPaid;
  final int? linkedExpenseId;
  final String? merchantName;
  final String? location;

  CreditCardTransaction({
    required this.id,
    required this.cardId,
    required this.amount,
    required this.description,
    required this.category,
    required this.transactionDate,
    required this.isEmi,
    this.emiMonths,
    this.emiInterestRate,
    this.processingFee,
    this.gstAmount,
    required this.transactionType,
    required this.isPaid,
    this.linkedExpenseId,
    this.merchantName,
    this.location,
  });

  bool get isEmiTransaction => isEmi && emiMonths != null && emiMonths! > 0;

  // Add fromMap/toMap or fromDb/toDb methods as needed for repository integration
} 