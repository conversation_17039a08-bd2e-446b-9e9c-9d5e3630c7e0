enum PaymentSourceType { bankAccount, creditCard, personalCreditor }

class PaymentSource {
  final PaymentSourceType type;
  final int sourceId; // id of the bank account, credit card, or personal creditor
  final String name;
  final String? details; // e.g., last four digits, card type, etc.

  PaymentSource({
    required this.type,
    required this.sourceId,
    required this.name,
    this.details,
  });
} 