class CreditCard {
  final int id;
  final String bankName;
  final String cardNumber;
  final String? lastFourDigits;
  final String cardHolderName;
  final double creditLimit;
  final double availableCredit;
  final double outstandingBalance;
  final double interestRate;
  final double annualFee;
  final DateTime dueDate;
  final DateTime statementDate;
  final bool isActive;
  final String cardType; // Visa, MasterCard, Amex, etc.
  final String cardColor;
  final DateTime createdAt;
  final DateTime lastUpdated;

  CreditCard({
    required this.id,
    required this.bankName,
    required this.cardNumber,
    this.lastFourDigits,
    required this.cardHolderName,
    required this.creditLimit,
    required this.availableCredit,
    required this.outstandingBalance,
    required this.interestRate,
    required this.annualFee,
    required this.dueDate,
    required this.statementDate,
    required this.isActive,
    required this.cardType,
    required this.cardColor,
    required this.createdAt,
    required this.lastUpdated,
  });

  // Add business logic methods as needed, e.g.:
  double get utilizationRate =>
      creditLimit > 0 ? (outstandingBalance / creditLimit) : 0.0;

  bool get isOverdue => DateTime.now().isAfter(dueDate);

  // Add fromMap/toMap or fromDb/toDb methods as needed for repository integration
} 