class Loan {
  final int id;
  final String type; // Personal, Gold, Car, Home, Business, Credit Card
  final String loanName;
  final bool isLending;
  final String partyName;
  final String? partyPhone;
  final String? partyEmail;
  final double principalAmount;
  final double currentBalance;
  final double interestRate;
  final String interestType; // Simple, Compound, Flat
  final String interestFrequency; // Monthly, Quarterly, Yearly
  final DateTime startDate;
  final DateTime dueDate;
  final double paidAmount;
  final double? foreclosureCharges;
  final double? processingFee;
  final double? gstAmount;
  final bool isEmi;
  final int? emiMonths;
  final double? monthlyEmi;
  final String? loanPurpose;
  final String? collateral;
  final bool isActive;
  final int? linkedCreditCardId;
  final DateTime createdAt;
  final DateTime lastUpdated;

  Loan({
    required this.id,
    required this.type,
    required this.loanName,
    required this.isLending,
    required this.partyName,
    this.partyPhone,
    this.partyEmail,
    required this.principalAmount,
    required this.currentBalance,
    required this.interestRate,
    required this.interestType,
    required this.interestFrequency,
    required this.startDate,
    required this.dueDate,
    required this.paidAmount,
    this.foreclosureCharges,
    this.processingFee,
    this.gstAmount,
    required this.isEmi,
    this.emiMonths,
    this.monthlyEmi,
    this.loanPurpose,
    this.collateral,
    required this.isActive,
    this.linkedCreditCardId,
    required this.createdAt,
    required this.lastUpdated,
  });

  bool get isOverdue => DateTime.now().isAfter(dueDate) && isActive;

  // Add fromMap/toMap or fromDb/toDb methods as needed for repository integration
} 