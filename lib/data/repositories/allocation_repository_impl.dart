import '../../core/database/app_database.dart';
import '../../domain/repositories/allocation_repository.dart';

class AllocationRepositoryImpl implements AllocationRepository {
  final AppDatabase db;

  AllocationRepositoryImpl(this.db);

  @override
  Future<List<Allocation>> getAllocations() async {
    return await db.select(db.allocations).get();
  }

  @override
  Future<void> addAllocation(AllocationsCompanion allocation) async {
    await db.into(db.allocations).insert(allocation);
  }

  @override
  Future<void> updateAllocation(int id, AllocationsCompanion allocation) async {
    await (db.update(db.allocations)..where((tbl) => tbl.id.equals(id))).write(allocation);
  }

  @override
  Future<void> deleteAllocation(int id) async {
    await (db.delete(db.allocations)..where((tbl) => tbl.id.equals(id))).go();
  }
} 