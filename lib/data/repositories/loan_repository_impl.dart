import '../../core/database/app_database.dart';
import '../../domain/repositories/loan_repository.dart';

class LoanRepositoryImpl implements LoanRepository {
  final AppDatabase _database;

  LoanRepositoryImpl(this._database);

  @override
  Future<List<Debt>> getAllLoans() async {
    return await _database.select(_database.debts).get();
  }

  @override
  Future<Debt?> getLoanById(int id) async {
    return await (_database.select(_database.debts)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  @override
  Future<int> addLoan(DebtsCompanion loan) async {
    return await _database.into(_database.debts).insert(loan);
  }

  @override
  Future<bool> updateLoan(int id, DebtsCompanion loan) async {
    return await (_database.update(_database.debts)..where((tbl) => tbl.id.equals(id))).write(loan) > 0;
  }

  @override
  Future<bool> deleteLoan(int id) async {
    return await (_database.delete(_database.debts)..where((tbl) => tbl.id.equals(id))).go() > 0;
  }

  @override
  Future<double> getTotalOutstandingBalance() async {
    final loans = await getAllLoans();
    return loans.fold<double>(0.0, (sum, loan) => sum + loan.currentBalance);
  }

  @override
  Future<Map<String, dynamic>> getLoanStatistics() async {
    final loans = await getAllLoans();
    final totalOutstanding = loans.fold(0.0, (sum, loan) => sum + loan.currentBalance);
    final totalPrincipal = loans.fold(0.0, (sum, loan) => sum + loan.principalAmount);
    final totalInterest = loans.fold(0.0, (sum, loan) => sum + loan.interestRate);
    
    return {
      'totalOutstanding': totalOutstanding,
      'totalPrincipal': totalPrincipal,
      'totalInterest': totalInterest,
      'loanCount': loans.length,
    };
  }
} 