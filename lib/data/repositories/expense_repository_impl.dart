import '../../domain/repositories/expense_repository.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class ExpenseRepositoryImpl implements ExpenseRepository {
  final AppDatabase db;
  ExpenseRepositoryImpl(this.db);

  @override
  Future<List<TransactionWithCategory>> getExpenses() async {
    final query = await (db.select(db.transactions).join([
      drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
    ])..where(db.transactions.type.equals('expense'))).get();
    return query.map((row) {
              final transaction = row.readTable(db.transactions);
        final category = row.readTable(db.categories);
        return TransactionWithCategory(transaction: transaction, category: category);
    }).toList();
  }

  @override
  Future<Transaction?> getExpenseById(int id) async {
    final query = db.select(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('expense'));
    return await query.getSingleOrNull();
  }

  @override
  Future<int> addExpense(TransactionsCompanion expense) async {
    // Insert the expense with type='expense'
    final expenseWithType = expense.copyWith(type: drift.Value('expense'));
    final id = await db.into(db.transactions).insert(expenseWithType);
    
    // Subtract amount from account balance
    if (expense.amount.value != null && expense.fromAccountId.value != null) {
      final account = await (db.select(db.bankAccounts)..where((a) => a.id.equals(expense.fromAccountId.value!))).getSingleOrNull();
      if (account != null) {
        final newBalance = account.currentBalance - expense.amount.value!;
        await (db.update(db.bankAccounts)..where((a) => a.id.equals(account.id))).write(
          BankAccountsCompanion(currentBalance: drift.Value(newBalance), lastUpdated: drift.Value(DateTime.now())),
        );
      }
    }
    return id;
  }

  @override
  Future<void> updateExpense(int id, TransactionsCompanion expense) async {
    final expenseWithType = expense.copyWith(type: drift.Value('expense'));
    await (db.update(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('expense'))).write(expenseWithType);
  }

  @override
  Future<void> deleteExpense(int id) async {
    await (db.delete(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('expense'))).go();
  }

  @override
  Future<List<BorrowedContactSummary>> getBorrowedContactsSummary() async {
    final query = await (db.select(db.transactions)..where((tbl) => tbl.type.equals('expense') & tbl.contactId.isNotNull())).get();
    
    final contactTotals = <String, double>{};
    final contactNames = <String, String>{};
    final contactPhones = <String, String>{};
    
    for (final transaction in query) {
      final contactId = transaction.contactId;
      if (contactId != null) {
        contactTotals[contactId] = (contactTotals[contactId] ?? 0) + transaction.amount;
        contactNames[contactId] = transaction.contactName ?? '';
        contactPhones[contactId] = transaction.contactPhone ?? '';
      }
    }
    
    return contactTotals.entries.map((entry) => BorrowedContactSummary(
      contactId: entry.key,
      contactName: contactNames[entry.key],
      contactPhone: contactPhones[entry.key],
      totalAmount: entry.value,
    )).toList();
  }

  @override
  Future<List<ContactSummary>> getAllContactsSummary() async {
    final query = await (db.select(db.transactions)..where((tbl) => tbl.contactId.isNotNull())).get();
    
    final contactBalances = <String, double>{};
    final contactNames = <String, String>{};
    final contactPhones = <String, String>{};
    
    for (final transaction in query) {
      final contactId = transaction.contactId;
      if (contactId != null) {
        final amount = transaction.type == 'expense' ? -transaction.amount : transaction.amount;
        contactBalances[contactId] = (contactBalances[contactId] ?? 0) + amount;
        contactNames[contactId] = transaction.contactName ?? '';
        contactPhones[contactId] = transaction.contactPhone ?? '';
      }
    }
    
    return contactBalances.entries.map((entry) => ContactSummary(
      contactId: entry.key,
      contactName: contactNames[entry.key],
      contactPhone: contactPhones[entry.key],
      netBalance: entry.value,
    )).toList();
  }

  @override
  Future<List<TransactionWithCategory>> getExpensesByAccountId(int accountId) async {
    final query = await (db.select(db.transactions).join([
      drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
    ])..where(db.transactions.type.equals('expense') & db.transactions.fromAccountId.equals(accountId))).get();
    
    return query.map((row) {
              final transaction = row.readTable(db.transactions);
        final category = row.readTable(db.categories);
        return TransactionWithCategory(transaction: transaction, category: category);
    }).toList();
  }
} 