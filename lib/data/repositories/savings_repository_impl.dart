import '../../core/database/app_database.dart';
import '../../domain/repositories/savings_repository.dart';

class SavingsRepositoryImpl implements SavingsRepository {
  final AppDatabase db;
  SavingsRepositoryImpl(this.db);

  @override
  Future<List<Saving>> getSavings() async {
    return await db.select(db.savings).get();
  }

  @override
  Future<void> addSaving(SavingsCompanion saving) async {
    await db.into(db.savings).insert(saving);
  }

  @override
  Future<void> updateSaving(int id, SavingsCompanion saving) async {
    await (db.update(db.savings)..where((tbl) => tbl.id.equals(id))).write(saving);
  }

  @override
  Future<void> deleteSaving(int id) async {
    await (db.delete(db.savings)..where((tbl) => tbl.id.equals(id))).go();
  }
} 