import '../../domain/repositories/income_repository.dart';
import '../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class IncomeRepositoryImpl implements IncomeRepository {
  final AppDatabase db;
  IncomeRepositoryImpl(this.db);

  @override
  Future<List<Transaction>> getIncomes() async {
    return await (db.select(db.transactions)..where((tbl) => tbl.type.equals('income'))).get();
  }

  @override
  Future<Transaction?> getIncomeById(int id) async {
    return await (db.select(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('income'))).getSingleOrNull();
  }

  @override
  Future<int> addIncome(TransactionsCompanion income) async {
    final incomeWithType = income.copyWith(type: drift.Value('income'));
    return await db.into(db.transactions).insert(incomeWithType);
  }

  @override
  Future<void> updateIncome(int id, TransactionsCompanion income) async {
    final incomeWithType = income.copyWith(type: drift.Value('income'));
    await (db.update(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('income'))).write(incomeWithType);
  }

  @override
  Future<void> deleteIncome(int id) async {
    await (db.delete(db.transactions)..where((tbl) => tbl.id.equals(id) & tbl.type.equals('income'))).go();
  }
} 