import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../core/services/loan_calculation_service.dart';
import '../../domain/repositories/credit_card_repository.dart';

class CreditCardRepositoryImpl implements CreditCardRepository {
  final AppDatabase _database;

  CreditCardRepositoryImpl(this._database);

  @override
  Future<List<CreditCard>> getAllCreditCards() async {
    return await _database.select(_database.creditCards).get();
  }

  @override
  Future<CreditCard?> getCreditCardById(int id) async {
    final result = await (_database.select(_database.creditCards)
          ..where((card) => card.id.equals(id)))
        .getSingleOrNull();
    return result;
  }

  @override
  Future<int> addCreditCard(CreditCardsCompanion card) async {
    return await _database.into(_database.creditCards).insert(card);
  }

  @override
  Future<bool> updateCreditCard(CreditCardsCompanion card) async {
    return await _database.update(_database.creditCards).replace(card);
  }

  @override
  Future<int> deleteCreditCard(int id) async {
    return await (_database.delete(_database.creditCards)
          ..where((card) => card.id.equals(id)))
        .go();
  }

  @override
  Future<List<CreditCardTransaction>> getTransactionsByCardId(int cardId) async {
    return await (_database.select(_database.creditCardTransactions)
          ..where((transaction) => transaction.cardId.equals(cardId))
          ..orderBy([(transaction) => OrderingTerm.desc(transaction.transactionDate)]))
        .get();
  }

  @override
  Future<List<CreditCardTransaction>> getEmiTransactionsByCardId(int cardId) async {
    return await (_database.select(_database.creditCardTransactions)
          ..where((transaction) => transaction.cardId.equals(cardId) & transaction.isEmi.equals(true))
          ..orderBy([(transaction) => OrderingTerm.desc(transaction.transactionDate)]))
        .get();
  }

  @override
  Future<List<CreditCardPayment>> getPaymentsByCardId(int cardId) async {
    return await (_database.select(_database.creditCardPayments)
          ..where((payment) => payment.cardId.equals(cardId))
          ..orderBy([(payment) => OrderingTerm.desc(payment.paymentDate)]))
        .get();
  }

  @override
  Future<Map<String, dynamic>> getCreditCardSummary(int cardId) async {
    final card = await getCreditCardById(cardId);
    if (card == null) return {};

    final transactions = await getTransactionsByCardId(cardId);
    final payments = await getPaymentsByCardId(cardId);
    final emiTransactions = await getEmiTransactionsByCardId(cardId);

    // Calculate pending amount
    final totalCharges = transactions
        .where((t) => !t.isPaid && t.transactionType == 'Purchase')
        .fold(0.0, (sum, t) => sum + t.amount);

    final totalPayments = payments
        .where((p) => p.isProcessed == true)
        .fold(0.0, (sum, p) => sum + p.amount);

    final pendingAmount = totalCharges - totalPayments;

    // Calculate active EMIs
    final activeEmis = emiTransactions
        .where((t) => t.isEmi && !t.isPaid)
        .length;

    // Calculate total outstanding EMIs
    final totalEmiOutstanding = emiTransactions
        .where((t) => t.isEmi && !t.isPaid)
        .fold(0.0, (sum, t) {
          if (t.emiMonths != null && t.emiInterestRate != null) {
            final emiAmount = LoanCalculationService.calculateEmi(
              principal: t.amount,
              interestRate: t.emiInterestRate!,
              months: t.emiMonths!,
              interestType: 'compound',
            );
            return sum + (emiAmount * t.emiMonths!);
          }
          return sum;
        });

    return {
      'card': card,
      'pendingAmount': pendingAmount,
      'totalCharges': totalCharges,
      'totalPayments': totalPayments,
      'activeEmis': activeEmis,
      'totalEmiOutstanding': totalEmiOutstanding,
      'availableCredit': card.creditLimit - pendingAmount,
      'creditUtilization': (pendingAmount / card.creditLimit) * 100,
    };
  }

  @override
  Future<int> addTransaction(CreditCardTransactionsCompanion transaction) async {
    return await _database.into(_database.creditCardTransactions).insert(transaction);
  }

  @override
  Future<bool> updateTransaction(CreditCardTransactionsCompanion transaction) async {
    return await _database.update(_database.creditCardTransactions).replace(transaction);
  }

  @override
  Future<int> deleteTransaction(int id) async {
    return await (_database.delete(_database.creditCardTransactions)
          ..where((transaction) => transaction.id.equals(id)))
        .go();
  }

  @override
  Future<int> addPayment(CreditCardPaymentsCompanion payment) async {
    // Comment out or remove methods using missing types
    // return await _database.into(_database.creditCardPayments).insert(payment);
    throw UnimplementedError();
  }

  @override
  Future<bool> updatePayment(CreditCardPaymentsCompanion payment) async {
    // Comment out or remove methods using missing types
    // return await _database.update(_database.creditCardPayments).replace(payment);
    throw UnimplementedError();
  }

  @override
  Future<int> deletePayment(int id) async {
    // Comment out or remove methods using missing types
    // return await (_database.delete(_database.creditCardPayments)
    //       ..where((payment) => payment.id.equals(id)))
    //     .go();
    throw UnimplementedError();
  }

  @override
  Future<List<EmiSchedule>> getEmiSchedulesByTransactionId(int transactionId) async {
    return await (_database.select(_database.emiSchedules)
          ..where((schedule) => schedule.transactionId.equals(transactionId))
          ..orderBy([(schedule) => OrderingTerm.asc(schedule.emiNumber)]))
        .get();
  }

  @override
  Future<List<EmiSchedule>> getActiveEmiSchedules() async {
    return await (_database.select(_database.emiSchedules)
          ..where((schedule) => schedule.isPaid.equals(false))
          ..orderBy([(schedule) => OrderingTerm.asc(schedule.dueDate)]))
        .get();
  }

  @override
  Future<bool> updateEmiSchedule(EmiSchedulesCompanion schedule) async {
    return await _database.update(_database.emiSchedules).replace(schedule);
  }

  @override
  Future<Map<String, dynamic>> calculateEmiForTransaction({
    required double amount,
    required double interestRate,
    required int months,
    required double processingFee,
    required double gstRate,
  }) async {
    return LoanCalculationService.calculateCreditCardEmi(
      principal: amount,
      interestRate: interestRate,
      months: months,
      processingFee: processingFee,
      gstRate: gstRate,
    );
  }

  @override
  Future<List<Map<String, dynamic>>> generateEmiSchedule({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  }) async {
    return LoanCalculationService.generateEmiSchedule(
      principal: principal,
      emiAmount: emiAmount,
      interestRate: interestRate,
      months: months,
      startDate: startDate,
    );
  }

  @override
  Future<Map<String, dynamic>> getCreditCardAnalytics(int cardId) async {
    final transactions = await getTransactionsByCardId(cardId);
    final payments = await getPaymentsByCardId(cardId);

    // Monthly spending analysis
    final monthlySpending = <String, double>{};
    for (final transaction in transactions) {
      final monthKey = '${transaction.transactionDate.year}-${transaction.transactionDate.month.toString().padLeft(2, '0')}';
      monthlySpending[monthKey] = (monthlySpending[monthKey] ?? 0) + transaction.amount;
    }

    // Category-wise spending
    final categorySpending = <String, double>{};
    for (final transaction in transactions) {
      categorySpending[transaction.category] = (categorySpending[transaction.category] ?? 0) + transaction.amount;
    }

    // Payment history
    final paymentHistory = payments
        .map((p) => {
              'date': p.paymentDate ?? DateTime.now(),
              'amount': p.amount,
              'method': p.paymentMethod ?? 'Unknown',
            })
        .toList();

    return {
      'monthlySpending': monthlySpending,
      'categorySpending': categorySpending,
      'paymentHistory': paymentHistory,
      'totalTransactions': transactions.length,
      'totalPayments': payments.length,
    };
  }

  @override
  Future<List<CreditCard>> getActiveCreditCards() async {
    return await (_database.select(_database.creditCards)
          ..where((card) => card.isActive.equals(true)))
        .get();
  }

  @override
  Future<double> getTotalCreditLimit() async {
    final cards = await getActiveCreditCards();
    return cards.fold<double>(0.0, (sum, card) => sum + card.creditLimit);
  }

  @override
  Future<double> getTotalOutstandingBalance() async {
    final cards = await getActiveCreditCards();
    return cards.fold<double>(0.0, (sum, card) => sum + card.outstandingBalance);
  }

  @override
  Future<List<CreditCard>> getCreditCards() async {
    return await _database.select(_database.creditCards).get();
  }
} 