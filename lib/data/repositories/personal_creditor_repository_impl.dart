import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../domain/models/personal_creditor.dart';
import '../../domain/repositories/personal_creditor_repository.dart';

class PersonalCreditorRepositoryImpl implements PersonalCreditorRepository {
  final AppDatabase _database;
  PersonalCreditorRepositoryImpl(this._database);

  @override
  Future<List<PersonalCreditor>> getAllCreditors() async {
    final debts = await (_database.select(_database.debts)
        ..where((tbl) => tbl.type.equals('PersonalCreditor')))
        .get();
    
    return debts.map((debt) => PersonalCreditor(
      id: debt.id,
      name: debt.partyName,
      phone: debt.partyPhone,
      email: debt.partyEmail,
    )).toList();
  }

  @override
  Future<PersonalCreditor?> getCreditorById(int id) async {
    final debt = await (_database.select(_database.debts)
        ..where((tbl) => tbl.id.equals(id) & tbl.type.equals('PersonalCreditor')))
        .getSingleOrNull();
    
    if (debt == null) return null;
    
    return PersonalCreditor(
      id: debt.id,
      name: debt.partyName,
      phone: debt.partyPhone,
      email: debt.partyEmail,
    );
  }

  @override
  Future<int> addCreditor(DebtsCompanion creditor) async {
    return await _database.into(_database.debts).insert(creditor);
  }

  @override
  Future<bool> updateCreditor(DebtsCompanion creditor) async {
    return await _database.update(_database.debts).replace(creditor);
  }

  @override
  Future<int> deleteCreditor(int id) async {
    return await (_database.delete(_database.debts)
        ..where((tbl) => tbl.id.equals(id) & tbl.type.equals('PersonalCreditor')))
        .go();
  }
} 