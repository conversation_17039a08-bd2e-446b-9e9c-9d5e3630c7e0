import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../core/services/loan_calculation_service.dart';
import '../../domain/repositories/debt_repository.dart';

class DebtRepositoryImpl implements DebtRepository {
  final AppDatabase _database;

  DebtRepositoryImpl(this._database);

  @override
  Future<List<Debt>> getAllDebts() async {
    return await _database.select(_database.debts).get();
  }

  @override
  Future<List<Debt>> getDebts() async {
    return await _database.select(_database.debts).get();
  }

  @override
  Future<Debt?> getDebtById(int id) async {
    final result = await (_database.select(_database.debts)
          ..where((debt) => debt.id.equals(id)))
        .getSingleOrNull();
    return result;
  }

  @override
  Future<int> addDebt(DebtsCompanion debt) async {
    return await _database.into(_database.debts).insert(debt);
  }

  @override
  Future<bool> updateDebt(DebtsCompanion debt) async {
    return await _database.update(_database.debts).replace(debt);
  }

  @override
  Future<int> deleteDebt(int id) async {
    return await (_database.delete(_database.debts)
          ..where((debt) => debt.id.equals(id)))
        .go();
  }

  @override
  Future<List<Debt>> getDebtsByType(String type) async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.type.equals(type))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<List<Debt>> getActiveDebts() async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<List<Debt>> getLendingDebts() async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.isLending.equals(true) & debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<List<Debt>> getBorrowingDebts() async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.isLending.equals(false) & debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<Map<String, dynamic>> getDebtSummary(int debtId) async {
    final debt = await getDebtById(debtId);
    if (debt == null) return {};

    // Calculate next EMI if applicable
    double? nextEmiAmount;
    DateTime? nextEmiDate;
    if (debt.isEmi && debt.monthlyEmi != null) {
      nextEmiAmount = debt.monthlyEmi;
      final lastPayment = debt.startDate;
      nextEmiDate = DateTime(lastPayment.year, lastPayment.month + 1, debt.startDate.day);
    }

    return {
      'debt': debt,
      'nextEmiAmount': nextEmiAmount,
      'nextEmiDate': nextEmiDate,
    };
  }

  @override
  Future<Map<String, dynamic>> calculateLoanEmi({
    required String loanType,
    required double principal,
    required double interestRate,
    required int months,
    required String interestType,
    required String interestFrequency,
  }) async {
    double emiAmount;
    
    switch (loanType.toLowerCase()) {
      case 'gold':
        emiAmount = LoanCalculationService.calculateGoldLoanEmi(
          principal: principal,
          yearlyInterestRate: interestRate,
          months: months,
        );
        break;
      case 'home':
        emiAmount = LoanCalculationService.calculateHomeLoanEmi(
          principal: principal,
          interestRate: interestRate,
          years: months ~/ 12,
        );
        break;
      case 'car':
        emiAmount = LoanCalculationService.calculateCarLoanEmi(
          principal: principal,
          interestRate: interestRate,
          years: months ~/ 12,
        );
        break;
      case 'personal':
        emiAmount = LoanCalculationService.calculatePersonalLoanEmi(
          principal: principal,
          interestRate: interestRate,
          months: months,
        );
        break;
      case 'business':
        emiAmount = LoanCalculationService.calculateBusinessLoanEmi(
          principal: principal,
          interestRate: interestRate,
          years: months ~/ 12,
        );
        break;
      default:
        emiAmount = LoanCalculationService.calculateEmi(
          principal: principal,
          interestRate: interestRate,
          months: months,
          interestType: interestType,
        );
    }

    final totalInterest = LoanCalculationService.calculateTotalInterest(
      principal: principal,
      emiAmount: emiAmount,
      months: months,
    );

    return {
      'monthlyEmi': emiAmount,
      'totalInterest': totalInterest,
      'totalAmount': emiAmount * months,
      'interestRate': interestRate,
      'loanType': loanType,
    };
  }

  @override
  Future<Map<String, dynamic>> calculateNoCostEmi({
    required double principal,
    required int months,
    required double processingFee,
    required double gstRate,
  }) async {
    return LoanCalculationService.calculateNoCostEmi(
      principal: principal,
      months: months,
      processingFee: processingFee,
      gstRate: gstRate,
    );
  }

  @override
  Future<List<Map<String, dynamic>>> generateEmiSchedule({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  }) async {
    return LoanCalculationService.generateEmiSchedule(
      principal: principal,
      emiAmount: emiAmount,
      interestRate: interestRate,
      months: months,
      startDate: startDate,
    );
  }

  @override
  Future<Map<String, dynamic>> calculatePrepaymentSavings({
    required double remainingPrincipal,
    required double prepaymentAmount,
    required double interestRate,
    required int remainingMonths,
  }) async {
    return LoanCalculationService.calculatePrepaymentSavings(
      remainingPrincipal: remainingPrincipal,
      prepaymentAmount: prepaymentAmount,
      interestRate: interestRate,
      remainingMonths: remainingMonths,
    );
  }

  @override
  Future<double> calculateForeclosureCharges({
    required double remainingPrincipal,
    required double foreclosureRate,
  }) async {
    return LoanCalculationService.calculateForeclosureCharges(
      remainingPrincipal: remainingPrincipal,
      foreclosureRate: foreclosureRate,
    );
  }

  @override
  Future<Map<String, dynamic>> getLoanAnalytics() async {
    final allDebts = await getAllDebts();
    final activeDebts = await getActiveDebts();
    final lendingDebts = await getLendingDebts();
    final borrowingDebts = await getBorrowingDebts();

    // Calculate totals
    final totalPrincipal = allDebts.fold(0.0, (sum, debt) => sum + debt.principalAmount);
    final totalOutstanding = activeDebts.fold(0.0, (sum, debt) => sum + debt.currentBalance);
    final totalLending = lendingDebts.fold(0.0, (sum, debt) => sum + debt.currentBalance);
    final totalBorrowing = borrowingDebts.fold(0.0, (sum, debt) => sum + debt.currentBalance);

    // Loan type distribution
    final loanTypeDistribution = <String, double>{};
    for (final debt in activeDebts) {
      loanTypeDistribution[debt.type] = (loanTypeDistribution[debt.type] ?? 0) + debt.currentBalance;
    }

    // Interest rate analysis
    final avgInterestRate = activeDebts.isNotEmpty
        ? activeDebts.fold(0.0, (sum, debt) => sum + debt.interestRate) / activeDebts.length
        : 0.0;

    return {
      'totalPrincipal': totalPrincipal,
      'totalOutstanding': totalOutstanding,
      'totalLending': totalLending,
      'totalBorrowing': totalBorrowing,
      'loanTypeDistribution': loanTypeDistribution,
      'avgInterestRate': avgInterestRate,
      'totalActiveLoans': activeDebts.length,
      'totalLendingLoans': lendingDebts.length,
      'totalBorrowingLoans': borrowingDebts.length,
    };
  }

  @override
  Future<List<Debt>> getDebtsByPartyName(String partyName) async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.partyName.equals(partyName))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<List<Debt>> getOverdueDebts() async {
    final today = DateTime.now();
    return await (_database.select(_database.debts)
          ..where((debt) => debt.dueDate.isSmallerThanValue(today) & debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.asc(debt.dueDate)]))
        .get();
  }

  @override
  Future<List<Debt>> getUpcomingDueDebts({int days = 7}) async {
    final today = DateTime.now();
    final futureDate = today.add(Duration(days: days));
    
    return await (_database.select(_database.debts)
          ..where((debt) => 
              debt.dueDate.isBiggerOrEqualValue(today) & 
              debt.dueDate.isSmallerOrEqualValue(futureDate) & 
              debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.asc(debt.dueDate)]))
        .get();
  }

  @override
  Future<List<Debt>> getCreditCardLoans() async {
    return await (_database.select(_database.debts)
          ..where((debt) => debt.type.equals('Credit Card') & debt.isActive.equals(true))
          ..orderBy([(debt) => OrderingTerm.desc(debt.createdAt)]))
        .get();
  }

  @override
  Future<Map<String, dynamic>> getDebtAnalytics() async {
    final debts = await (_database.select(_database.debts)
        ..where((tbl) => tbl.isActive.equals(true)))
        .get();
    return {
      'totalDebts': debts.length,
      'totalAmount': debts.fold(0.0, (sum, debt) => sum + debt.currentBalance),
      'debts': debts,
    };
  }
} 