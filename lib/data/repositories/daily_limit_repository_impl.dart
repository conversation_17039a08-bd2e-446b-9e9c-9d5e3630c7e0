import '../../domain/repositories/daily_limit_repository.dart';
import '../../core/database/app_database.dart';

class DailyLimitRepositoryImpl implements DailyLimitRepository {
  final AppDatabase db;
  DailyLimitRepositoryImpl(this.db);

  @override
  Future<List<DailyLimit>> getDailyLimits() async {
    return await db.select(db.dailyLimits).get();
  }

  @override
  Future<DailyLimit?> getDailyLimitById(int id) async {
    final query = db.select(db.dailyLimits)..where((tbl) => tbl.id.equals(id));
    return await query.getSingleOrNull();
  }

  @override
  Future<int> addDailyLimit(DailyLimitsCompanion limit) async {
    return await db.into(db.dailyLimits).insert(limit);
  }

  @override
  Future<void> updateDailyLimit(int id, DailyLimitsCompanion limit) async {
    await (db.update(db.dailyLimits)..where((tbl) => tbl.id.equals(id))).write(limit);
  }

  @override
  Future<void> deleteDailyLimit(int id) async {
    await (db.delete(db.dailyLimits)..where((tbl) => tbl.id.equals(id))).go();
  }
} 