import '../../core/database/app_database.dart';
import '../../domain/repositories/investment_repository.dart';

class InvestmentRepositoryImpl implements InvestmentRepository {
  final AppDatabase db;
  InvestmentRepositoryImpl(this.db);

  @override
  Future<List<Investment>> getInvestments() => db.select(db.investments).get();

  @override
  Future<Investment?> getInvestmentById(int id) => (db.select(db.investments)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();

  @override
  Future<int> addInvestment(InvestmentsCompanion investment) => db.into(db.investments).insert(investment);

  @override
  Future<void> updateInvestment(int id, InvestmentsCompanion investment) => (db.update(db.investments)..where((tbl) => tbl.id.equals(id))).write(investment);

  @override
  Future<void> deleteInvestment(int id) => (db.delete(db.investments)..where((tbl) => tbl.id.equals(id))).go();
} 