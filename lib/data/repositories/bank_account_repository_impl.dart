import '../../core/database/app_database.dart';
import '../../domain/repositories/bank_account_repository.dart';

class BankAccountRepositoryImpl implements BankAccountRepository {
  final AppDatabase db;
  BankAccountRepositoryImpl(this.db);

  @override
  Future<List<BankAccount>> getBankAccounts() async {
    return await db.select(db.bankAccounts).get();
  }

  @override
  Future<BankAccount?> getBankAccountById(int id) async {
    return await (db.select(db.bankAccounts)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  @override
  Future<int> addBankAccount(BankAccountsCompanion account) async {
    return await db.into(db.bankAccounts).insert(account);
  }

  @override
  Future<void> updateBankAccount(int id, BankAccountsCompanion account) async {
    await (db.update(db.bankAccounts)..where((tbl) => tbl.id.equals(id))).write(account);
  }

  @override
  Future<void> deleteBankAccount(int id) async {
    await (db.delete(db.bankAccounts)..where((tbl) => tbl.id.equals(id))).go();
  }
} 