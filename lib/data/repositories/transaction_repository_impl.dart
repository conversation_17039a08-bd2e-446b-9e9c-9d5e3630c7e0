import '../../domain/repositories/transaction_repository.dart';
import '../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class TransactionRepositoryImpl implements TransactionRepository {
  final AppDatabase db;
  TransactionRepositoryImpl(this.db);

  @override
  Future<List<TransactionWithCategory>> getTransactions() async {
    final fromAcc = db.bankAccounts.createAlias('fromAcc');
    final toAcc = db.bankAccounts.createAlias('toAcc');
    try {
      print('DEBUG: Getting all transactions');
      final query = await (db.select(db.transactions).join([
        drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
        drift.leftOuterJoin(fromAcc, db.transactions.fromAccountId.equalsExp(fromAcc.id)),
        drift.leftOuterJoin(toAcc, db.transactions.toAccountId.equalsExp(toAcc.id)),
      ])).get();
      
      print('DEBUG: Found ${query.length} transactions in database');
      
      final result = query.map((row) {
        final transaction = row.readTable(db.transactions);
        final category = row.readTableOrNull(db.categories);
        final fromAccount = row.readTableOrNull(fromAcc);
        final toAccount = row.readTableOrNull(toAcc);
        return TransactionWithCategory(
          transaction: transaction,
          category: category,
          fromAccount: fromAccount,
          toAccount: toAccount,
        );
      }).toList();
      
      print('DEBUG: Returning ${result.length} transactions');
      return result;
    } catch (e) {
      print('DEBUG: Error getting transactions: $e');
      rethrow;
    }
  }

  @override
  Future<List<TransactionWithCategory>> getTransactionsByType(String type) async {
    final fromAcc = db.bankAccounts.createAlias('fromAcc');
    final toAcc = db.bankAccounts.createAlias('toAcc');
    final query = await (db.select(db.transactions).join([
      drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
      drift.leftOuterJoin(fromAcc, db.transactions.fromAccountId.equalsExp(fromAcc.id)),
      drift.leftOuterJoin(toAcc, db.transactions.toAccountId.equalsExp(toAcc.id)),
    ])..where(db.transactions.type.equals(type))).get();
    
    return query.map((row) {
      final transaction = row.readTable(db.transactions);
      final category = row.readTableOrNull(db.categories);
      final fromAccount = row.readTableOrNull(fromAcc);
      final toAccount = row.readTableOrNull(toAcc);
      return TransactionWithCategory(
        transaction: transaction,
        category: category,
        fromAccount: fromAccount,
        toAccount: toAccount,
      );
    }).toList();
  }

  @override
  Future<List<TransactionWithCategory>> getTransactionsByAccountId(int accountId) async {
    final fromAcc = db.bankAccounts.createAlias('fromAcc');
    final toAcc = db.bankAccounts.createAlias('toAcc');
    try {
      print('DEBUG: Getting transactions for account ID: $accountId');
      final query = await (db.select(db.transactions).join([
        drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
        drift.leftOuterJoin(fromAcc, db.transactions.fromAccountId.equalsExp(fromAcc.id)),
        drift.leftOuterJoin(toAcc, db.transactions.toAccountId.equalsExp(toAcc.id)),
      ])..where(db.transactions.fromAccountId.equals(accountId) | db.transactions.toAccountId.equals(accountId))).get();
      
      print('DEBUG: Found ${query.length} transactions for account $accountId');
      
      final result = query.map((row) {
        final transaction = row.readTable(db.transactions);
        final category = row.readTableOrNull(db.categories);
        final fromAccount = row.readTableOrNull(fromAcc);
        final toAccount = row.readTableOrNull(toAcc);
        return TransactionWithCategory(
          transaction: transaction,
          category: category,
          fromAccount: fromAccount,
          toAccount: toAccount,
        );
      }).toList();
      
      return result;
    } catch (e) {
      print('DEBUG: Error getting transactions for account $accountId: $e');
      rethrow;
    }
  }

  @override
  Future<Transaction?> getTransactionById(int id) async {
    final query = db.select(db.transactions)..where((tbl) => tbl.id.equals(id));
    return await query.getSingleOrNull();
  }

  @override
  Future<int> addTransaction(TransactionsCompanion transaction) async {
    print('DEBUG: Adding transaction - amount: ${transaction.amount.value}, type: ${transaction.type.value}');
    
    try {
      final id = await db.into(db.transactions).insert(transaction);
      print('DEBUG: Transaction inserted with ID: $id');
      
      // Only update account balances for non-pending transactions
      final status = transaction.status.value;
      if (status != 'pending') {
        await _updateAccountBalances(transaction);
      } else {
        print('DEBUG: Skipping account balance update for pending transaction');
      }
      
      // Verify the transaction was actually saved
      final savedTransaction = await getTransactionById(id);
      print('DEBUG: Retrieved saved transaction: ${savedTransaction?.toString()}');
      
      return id;
    } catch (e) {
      print('DEBUG: Error adding transaction: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateTransaction(int id, TransactionsCompanion transaction) async {
    // Get the current transaction to check if status is changing
    final currentTransaction = await getTransactionById(id);
    
    await (db.update(db.transactions)..where((tbl) => tbl.id.equals(id))).write(transaction);
    
    // If status is changing from pending to active, update account balances
    if (currentTransaction != null && 
        currentTransaction.status == 'pending' && 
        transaction.status.value == 'active') {
      print('DEBUG: Updating account balances for approved transaction');
      await _updateAccountBalances(TransactionsCompanion(
        amount: drift.Value(currentTransaction.amount),
        type: drift.Value(currentTransaction.type),
        fromAccountId: drift.Value(currentTransaction.fromAccountId),
        toAccountId: drift.Value(currentTransaction.toAccountId),
      ));
    }
  }

  @override
  Future<void> deleteTransaction(int id) async {
    await (db.delete(db.transactions)..where((tbl) => tbl.id.equals(id))).go();
  }

  @override
  Future<List<BorrowedContactSummary>> getBorrowedContactsSummary() async {
    final query = db.customSelect(
      '''
      SELECT contact_id, contact_name, contact_phone, SUM(amount) as total_amount
      FROM transactions
      WHERE type = 'credit_given' AND contact_id IS NOT NULL
      GROUP BY contact_id, contact_name, contact_phone
      ''',
    );
    final rows = await query.get();
    return rows.map((row) => BorrowedContactSummary(
      contactId: row.data['contact_id'] as String?,
      contactName: row.data['contact_name'] as String?,
      contactPhone: row.data['contact_phone'] as String?,
      totalAmount: (row.data['total_amount'] as num?)?.toDouble() ?? 0.0,
    )).toList();
  }

  @override
  Future<List<ContactSummary>> getAllContactsSummary() async {
    final query = db.customSelect(
      '''
      SELECT contact_id, contact_name, contact_phone,
        SUM(CASE
          WHEN type = 'credit_given' THEN amount
          WHEN type = 'credit_received' THEN -amount
          ELSE 0
        END) as net_balance
      FROM transactions
      WHERE contact_id IS NOT NULL
      GROUP BY contact_id, contact_name, contact_phone
      ''',
    );
    final rows = await query.get();
    return rows.map((row) => ContactSummary(
      contactId: row.data['contact_id'] as String?,
      contactName: row.data['contact_name'] as String?,
      contactPhone: row.data['contact_phone'] as String?,
      netBalance: (row.data['net_balance'] as num?)?.toDouble() ?? 0.0,
    )).toList();
  }

  @override
  Future<double> getTotalBalance() async {
    final transactions = await getTransactions();
    return transactions.fold<double>(0.0, (sum, t) {
      switch (t.transaction.type) {
        case 'income':
        case 'credit_received':
          return sum + t.transaction.amount;
        case 'expense':
        case 'credit_given':
          return sum - t.transaction.amount;
        default:
          return sum;
      }
    });
  }

  @override
  Future<Map<String, double>> getCategoryTotals() async {
    final transactions = await getTransactions();
    final categoryTotals = <String, double>{};
    
    for (final transaction in transactions) {
      final categoryName = transaction.category?.name ?? 'Unknown';
      final amount = transaction.transaction.amount;
      
      if (transaction.transaction.type == 'expense') {
        categoryTotals[categoryName] = (categoryTotals[categoryName] ?? 0) + amount;
      }
    }
    
    return categoryTotals;
  }

  @override
  Future<List<TransactionWithCategory>> getTransactionsPaginated({int limit = 20, int offset = 0}) async {
    final fromAcc = db.bankAccounts.createAlias('fromAcc');
    final toAcc = db.bankAccounts.createAlias('toAcc');
    try {
      final query = await (db.select(db.transactions).join([
        drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
        drift.leftOuterJoin(fromAcc, db.transactions.fromAccountId.equalsExp(fromAcc.id)),
        drift.leftOuterJoin(toAcc, db.transactions.toAccountId.equalsExp(toAcc.id)),
      ])
        ..orderBy([
          drift.OrderingTerm.desc(db.transactions.date),
        ])
        ..limit(limit, offset: offset)).get();

      return query.map((row) {
        final transaction = row.readTable(db.transactions);
        final category = row.readTableOrNull(db.categories);
        final fromAccount = row.readTableOrNull(fromAcc);
        final toAccount = row.readTableOrNull(toAcc);
        return TransactionWithCategory(
          transaction: transaction,
          category: category,
          fromAccount: fromAccount,
          toAccount: toAccount,
        );
      }).toList();
    } catch (e) {
      print('DEBUG: Error getting paginated transactions: $e');
      rethrow;
    }
  }

  Future<void> _updateAccountBalances(TransactionsCompanion transaction) async {
    // Safely extract values from drift.Value objects
    final amount = transaction.amount.value;
    final type = transaction.type.value;
    final fromAccountId = transaction.fromAccountId.value;
    final toAccountId = transaction.toAccountId.value;

    // Check if required values are present
    if (amount == null || type == null) {
      print('DEBUG: Missing required values - amount: $amount, type: $type');
      return;
    }

    print('DEBUG: Updating account balances - type: $type, amount: $amount, fromAccount: $fromAccountId, toAccount: $toAccountId');

    switch (type) {
      case 'expense':
        if (fromAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
        }
        break;
      case 'income':
        if (toAccountId != null) {
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
      case 'transfer':
        if (fromAccountId != null && toAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
      case 'credit_given':
        if (fromAccountId != null) {
          await _decreaseAccountBalance(fromAccountId, amount);
        }
        break;
      case 'credit_received':
        if (toAccountId != null) {
          await _increaseAccountBalance(toAccountId, amount);
        }
        break;
      default:
        print('DEBUG: Unknown transaction type: $type');
        break;
    }
  }

  Future<void> _increaseAccountBalance(int accountId, double amount) async {
    final account = await (db.select(db.bankAccounts)..where((a) => a.id.equals(accountId))).getSingleOrNull();
    if (account != null) {
      final newBalance = account.currentBalance + amount;
      await (db.update(db.bankAccounts)..where((a) => a.id.equals(accountId))).write(
        BankAccountsCompanion(
          currentBalance: drift.Value(newBalance),
          lastUpdated: drift.Value(DateTime.now()),
        ),
      );
    }
  }

  Future<void> _decreaseAccountBalance(int accountId, double amount) async {
    final account = await (db.select(db.bankAccounts)..where((a) => a.id.equals(accountId))).getSingleOrNull();
    if (account != null) {
      final newBalance = account.currentBalance - amount;
      await (db.update(db.bankAccounts)..where((a) => a.id.equals(accountId))).write(
        BankAccountsCompanion(
          currentBalance: drift.Value(newBalance),
          lastUpdated: drift.Value(DateTime.now()),
        ),
      );
    }
  }
} 