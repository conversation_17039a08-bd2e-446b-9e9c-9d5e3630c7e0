import '../../domain/repositories/budget_repository.dart';
import '../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class BudgetRepositoryImpl implements BudgetRepository {
  final AppDatabase db;
  BudgetRepositoryImpl(this.db);

  @override
  Future<List<BudgetWithCategory>> getBudgets() async {
    final query = db.select(db.budgets).join([
      drift.innerJoin(db.categories, db.categories.id.equalsExp(db.budgets.categoryId)),
    ]);
    final rows = await query.get();
    return rows.map((row) => BudgetWithCategory(
      row.readTable(db.budgets),
      row.readTable(db.categories),
    )).toList();
  }

  @override
  Future<BudgetWithCategory?> getBudgetById(int id) async {
    final query = db.select(db.budgets).join([
      drift.innerJoin(db.categories, db.categories.id.equalsExp(db.budgets.categoryId)),
    ])..where(db.budgets.id.equals(id));
    final row = await query.getSingleOrNull();
    if (row == null) return null;
    return BudgetWithCategory(
      row.readTable(db.budgets),
      row.readTable(db.categories),
    );
  }

  @override
  Future<int> addBudget(BudgetsCompanion budget) async {
    return await db.into(db.budgets).insert(budget);
  }

  @override
  Future<void> updateBudget(int id, BudgetsCompanion budget) async {
    await (db.update(db.budgets)..where((tbl) => tbl.id.equals(id))).write(budget);
  }

  @override
  Future<void> deleteBudget(int id) async {
    await (db.delete(db.budgets)..where((tbl) => tbl.id.equals(id))).go();
  }
} 