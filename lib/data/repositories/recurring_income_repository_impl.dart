import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../domain/repositories/recurring_income_repository.dart';

class RecurringIncomeRepositoryImpl implements RecurringIncomeRepository {
  final AppDatabase _database;

  RecurringIncomeRepositoryImpl(this._database);

  @override
  Future<int> addRecurringIncome(RecurringIncomesCompanion recurringIncome) async {
    return await _database.into(_database.recurringIncomes).insert(recurringIncome);
  }

  @override
  Future<List<RecurringIncome>> getAllRecurringIncomes() async {
    return await _database.select(_database.recurringIncomes).get();
  }

  @override
  Future<List<RecurringIncome>> getActiveRecurringIncomes() async {
    return await (_database.select(_database.recurringIncomes)
          ..where((tbl) => tbl.isRecurringActive.equals(true)))
        .get();
  }

  @override
  Future<List<RecurringIncome>> getStoppedRecurringIncomes() async {
    return await (_database.select(_database.recurringIncomes)
          ..where((tbl) => tbl.isRecurringActive.equals(false)))
        .get();
  }

  @override
  Future<List<RecurringIncome>> getDueRecurringIncomes(DateTime date) async {
    final today = DateTime(date.year, date.month, date.day);
    print('RecurringIncomeRepository: Checking for due recurring incomes on $today');
    
    final allRecurringIncomes = await _database.select(_database.recurringIncomes).get();
    print('RecurringIncomeRepository: Total recurring incomes: ${allRecurringIncomes.length}');
    
    for (final income in allRecurringIncomes) {
      print('RecurringIncomeRepository: Income: ${income.name}, nextDueDate: ${income.nextDueDate}, isActive: ${income.isRecurringActive}');
    }
    
    final dueIncomes = await (_database.select(_database.recurringIncomes)
          ..where((tbl) => tbl.isRecurringActive.equals(true) &
              tbl.nextDueDate.isSmallerOrEqualValue(today)))
        .get();
    
    print('RecurringIncomeRepository: Found ${dueIncomes.length} due recurring incomes');
    return dueIncomes;
  }

  @override
  Future<void> updateRecurringIncome(int id, RecurringIncomesCompanion recurringIncome) async {
    await (_database.update(_database.recurringIncomes)
          ..where((tbl) => tbl.id.equals(id)))
        .write(recurringIncome);
  }

  @override
  Future<void> deleteRecurringIncome(int id) async {
    await (_database.delete(_database.recurringIncomes)
          ..where((tbl) => tbl.id.equals(id)))
        .go();
  }

  @override
  Future<void> updateLastProcessedDate(int id, DateTime date) async {
    await (_database.update(_database.recurringIncomes)
          ..where((tbl) => tbl.id.equals(id)))
        .write(RecurringIncomesCompanion(
      lastProcessedDate: Value(date),
      recurringUpdatedAt: Value(DateTime.now()),
    ));
  }

  @override
  Future<void> updateNextDueDate(int id, DateTime date) async {
    await (_database.update(_database.recurringIncomes)
          ..where((tbl) => tbl.id.equals(id)))
        .write(RecurringIncomesCompanion(
      nextDueDate: Value(date),
      recurringUpdatedAt: Value(DateTime.now()),
    ));
  }

  @override
  Future<RecurringIncome?> getRecurringIncomeById(int id) async {
    return await (_database.select(_database.recurringIncomes)
          ..where((tbl) => tbl.id.equals(id)))
        .getSingleOrNull();
  }
} 