import '../../domain/repositories/category_repository.dart';
import '../../core/database/app_database.dart';

class CategoryRepositoryImpl implements CategoryRepository {
  final AppDatabase db;
  CategoryRepositoryImpl(this.db);

  @override
  Future<List<Category>> getCategories() async {
    return await db.select(db.categories).get();
  }

  @override
  Future<Category?> getCategoryById(int id) async {
    final query = db.select(db.categories)..where((tbl) => tbl.id.equals(id));
    return await query.getSingleOrNull();
  }

  @override
  Future<int> addCategory(CategoriesCompanion category) async {
    return await db.into(db.categories).insert(category);
  }

  @override
  Future<void> updateCategory(int id, CategoriesCompanion category) async {
    await (db.update(db.categories)..where((tbl) => tbl.id.equals(id))).write(category);
  }

  @override
  Future<void> deleteCategory(int id) async {
    await (db.delete(db.categories)..where((tbl) => tbl.id.equals(id))).go();
  }
} 