import '../../domain/repositories/credit_card_transaction_repository.dart';
import '../../core/database/app_database.dart';

class CreditCardTransactionRepositoryImpl implements CreditCardTransactionRepository {
  final AppDatabase db;
  CreditCardTransactionRepositoryImpl(this.db);

  @override
  Future<List<CreditCardTransaction>> getTransactionsForCard(int cardId) async {
    return await (db.select(db.creditCardTransactions)..where((t) => t.cardId.equals(cardId))).get();
  }

  @override
  Future<CreditCardTransaction?> getTransactionById(int id) async {
    return await (db.select(db.creditCardTransactions)..where((t) => t.id.equals(id))).getSingleOrNull();
  }

  @override
  Future<int> addTransaction(CreditCardTransactionsCompanion tx) async {
    return await db.into(db.creditCardTransactions).insert(tx);
  }

  @override
  Future<void> updateTransaction(int id, CreditCardTransactionsCompanion tx) async {
    await (db.update(db.creditCardTransactions)..where((tbl) => tbl.id.equals(id))).write(tx);
  }

  @override
  Future<void> deleteTransaction(int id) async {
    await (db.delete(db.creditCardTransactions)..where((tbl) => tbl.id.equals(id))).go();
  }
} 