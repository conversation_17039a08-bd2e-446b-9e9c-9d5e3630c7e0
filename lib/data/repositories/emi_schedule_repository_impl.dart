import '../../domain/repositories/emi_schedule_repository.dart';
import '../../core/database/app_database.dart';

class EmiScheduleRepositoryImpl implements EmiScheduleRepository {
  final AppDatabase db;
  EmiScheduleRepositoryImpl(this.db);

  @override
  Future<List<EmiSchedule>> getSchedulesForTransaction(int transactionId) async {
    return await (db.select(db.emiSchedules)..where((s) => s.transactionId.equals(transactionId))).get();
  }

  @override
  Future<EmiSchedule?> getScheduleById(int id) async {
    return await (db.select(db.emiSchedules)..where((s) => s.id.equals(id))).getSingleOrNull();
  }

  @override
  Future<int> addSchedule(EmiSchedulesCompanion schedule) async {
    return await db.into(db.emiSchedules).insert(schedule);
  }

  @override
  Future<void> updateSchedule(int id, EmiSchedulesCompanion schedule) async {
    await (db.update(db.emiSchedules)..where((tbl) => tbl.id.equals(id))).write(schedule);
  }

  @override
  Future<void> deleteSchedule(int id) async {
    await (db.delete(db.emiSchedules)..where((tbl) => tbl.id.equals(id))).go();
  }
} 