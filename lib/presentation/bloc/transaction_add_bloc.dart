import 'package:flutter_bloc/flutter_bloc.dart';
import 'transaction_add_event.dart';
import 'transaction_add_state.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../application/usecases/add_transaction_usecase.dart';
import '../../application/usecases/add_expense_usecase.dart';
import '../../application/usecases/add_income_usecase.dart';
import '../../application/usecases/add_credit_card_transaction_usecase.dart';
import '../../application/usecases/add_debt_usecase.dart';
import '../../application/usecases/add_saving_usecase.dart';
import '../../application/usecases/add_investment_usecase.dart';
import '../../application/usecases/add_budget_usecase.dart';
import '../../application/usecases/add_allocation_usecase.dart';
import '../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../providers/expense_providers.dart';
import '../providers/income_providers.dart';
import '../providers/transaction_providers.dart';

class TransactionAddBloc extends Bloc<TransactionAddEvent, TransactionAddState> {
  final WidgetRef ref;
  TransactionAddBloc(this.ref) : super(const TransactionAddState()) {
    on<TransactionTypeChanged>((event, emit) => emit(state.copyWith(type: event.type)));
    on<TransactionCategoryChanged>((event, emit) => emit(state.copyWith(category: event.category)));
    on<TransactionFromAccountChanged>((event, emit) => emit(state.copyWith(fromAccount: event.fromAccount)));
    on<TransactionToAccountChanged>((event, emit) => emit(state.copyWith(toAccount: event.toAccount)));
    on<TransactionContactChanged>((event, emit) => emit(state.copyWith(contact: event.contact)));
    on<TransactionAmountChanged>((event, emit) => emit(state.copyWith(amount: event.amount)));
    on<TransactionDateChanged>((event, emit) => emit(state.copyWith(date: event.date)));
    on<TransactionDescriptionChanged>((event, emit) => emit(state.copyWith(description: event.description)));
    on<TransactionSubmitted>((event, emit) async {
      emit(state.copyWith(isSubmitting: true, error: null));
      try {
        // Build the TransactionsCompanion based on state
        final tx = TransactionsCompanion(
          amount: drift.Value(state.amount ?? 0),
          date: drift.Value(state.date ?? DateTime.now()),
          type: drift.Value(state.type),
          categoryId: drift.Value(state.category),
          fromAccountId: drift.Value(state.fromAccount),
          toAccountId: drift.Value(state.toAccount),
          contactId: drift.Value(state.contact),
          description: drift.Value(state.description),
          status: const drift.Value('active'),
        );
        // Use the correct use case based on type
        if (state.type == 'expense') {
          await ref.read(addExpenseUseCaseProvider).call(tx);
        } else if (state.type == 'income') {
          await ref.read(addIncomeUseCaseProvider).call(tx);
        } else {
          await ref.read(addTransactionUseCaseProvider).call(tx);
        }
        emit(state.copyWith(isSubmitting: false, isSuccess: true));
      } catch (e) {
        emit(state.copyWith(isSubmitting: false, isSuccess: false, error: e.toString()));
      }
    });
  }
} 