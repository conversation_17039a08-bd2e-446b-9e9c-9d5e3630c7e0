import 'package:equatable/equatable.dart';

abstract class TransactionAddEvent extends Equatable {
  const TransactionAddEvent();
  @override
  List<Object?> get props => [];
}

class TransactionTypeChanged extends TransactionAddEvent {
  final String type;
  const TransactionTypeChanged(this.type);
  @override
  List<Object?> get props => [type];
}

class TransactionCategoryChanged extends TransactionAddEvent {
  final int? category;
  const TransactionCategoryChanged(this.category);
  @override
  List<Object?> get props => [category];
}

class TransactionFromAccountChanged extends TransactionAddEvent {
  final int? fromAccount;
  const TransactionFromAccountChanged(this.fromAccount);
  @override
  List<Object?> get props => [fromAccount];
}

class TransactionToAccountChanged extends TransactionAddEvent {
  final int? toAccount;
  const TransactionToAccountChanged(this.toAccount);
  @override
  List<Object?> get props => [toAccount];
}

class TransactionContactChanged extends TransactionAddEvent {
  final String? contact;
  const TransactionContactChanged(this.contact);
  @override
  List<Object?> get props => [contact];
}

class TransactionAmountChanged extends TransactionAddEvent {
  final double amount;
  const TransactionAmountChanged(this.amount);
  @override
  List<Object?> get props => [amount];
}

class TransactionDateChanged extends TransactionAddEvent {
  final DateTime date;
  const TransactionDateChanged(this.date);
  @override
  List<Object?> get props => [date];
}

class TransactionDescriptionChanged extends TransactionAddEvent {
  final String? description;
  const TransactionDescriptionChanged(this.description);
  @override
  List<Object?> get props => [description];
}

class TransactionSubmitted extends TransactionAddEvent {} 