import 'package:equatable/equatable.dart';

class TransactionAddState extends Equatable {
  final String type;
  final int? category;
  final int? fromAccount;
  final int? toAccount;
  final String? contact;
  final double? amount;
  final DateTime? date;
  final String? description;
  final bool isSubmitting;
  final bool isSuccess;
  final String? error;

  const TransactionAddState({
    this.type = 'expense',
    this.category,
    this.fromAccount,
    this.toAccount,
    this.contact,
    this.amount,
    this.date,
    this.description,
    this.isSubmitting = false,
    this.isSuccess = false,
    this.error,
  });

  TransactionAddState copyWith({
    String? type,
    int? category,
    int? fromAccount,
    int? toAccount,
    String? contact,
    double? amount,
    DateTime? date,
    String? description,
    bool? isSubmitting,
    bool? isSuccess,
    String? error,
  }) {
    return TransactionAddState(
      type: type ?? this.type,
      category: category ?? this.category,
      fromAccount: fromAccount ?? this.fromAccount,
      toAccount: toAccount ?? this.toAccount,
      contact: contact ?? this.contact,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      description: description ?? this.description,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isSuccess: isSuccess ?? this.isSuccess,
      error: error,
    );
  }

  @override
  List<Object?> get props => [type, category, fromAccount, toAccount, contact, amount, date, description, isSubmitting, isSuccess, error];
} 