import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/category_repository_impl.dart';
import '../../application/usecases/get_categories_usecase.dart';
import '../../application/usecases/add_category_usecase.dart';
import '../../application/usecases/update_category_usecase.dart';
import '../../application/usecases/delete_category_usecase.dart';
import '../../core/database/database_provider.dart' as db;
import '../../domain/models/category.dart';

final categoryRepositoryProvider = Provider<CategoryRepositoryImpl>((ref) {
  final database = ref.watch(db.databaseProvider);
  return CategoryRepositoryImpl(database);
});

final getCategoriesUseCaseProvider = Provider<GetCategoriesUseCase>((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return GetCategoriesUseCase(repo);
});

final addCategoryUseCaseProvider = Provider<AddCategoryUseCase>((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return AddCategoryUseCase(repo);
});

final updateCategoryUseCaseProvider = Provider<UpdateCategoryUseCase>((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return UpdateCategoryUseCase(repo);
});

final deleteCategoryUseCaseProvider = Provider<DeleteCategoryUseCase>((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return DeleteCategoryUseCase(repo);
});

final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  final useCase = ref.watch(getCategoriesUseCaseProvider);
  return await useCase();
}); 