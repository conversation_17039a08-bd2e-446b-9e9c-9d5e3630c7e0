import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/notification_service.dart';

final notificationServiceProvider = Provider<NotificationService>((ref) => NotificationService());

final notificationInitProvider = FutureProvider<void>((ref) async {
  final service = ref.watch(notificationServiceProvider);
  await service.init();
});

final showBudgetAlertProvider = FutureProvider.family<void, String>((ref, message) async {
  final service = ref.watch(notificationServiceProvider);
  await service.showNotification(1, 'Budget Alert', message);
});

final showEmiDueProvider = FutureProvider.family<void, String>((ref, message) async {
  final service = ref.watch(notificationServiceProvider);
  await service.showNotification(2, 'EMI Due', message);
});

final showSavingsTipProvider = FutureProvider.family<void, String>((ref, message) async {
  final service = ref.watch(notificationServiceProvider);
  await service.showNotification(3, 'Savings Tip', message);
}); 