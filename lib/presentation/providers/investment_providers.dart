import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart';
import '../../data/repositories/investment_repository_impl.dart';
import '../../application/usecases/add_investment_usecase.dart';
import '../../application/usecases/delete_investment_usecase.dart';
import '../../application/usecases/update_investment_usecase.dart';
import '../../application/usecases/get_investments_usecase.dart';

final investmentRepositoryProvider = Provider<InvestmentRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return InvestmentRepositoryImpl(db);
});

final getInvestmentsUseCaseProvider = Provider<GetInvestmentsUseCase>((ref) {
  final repo = ref.watch(investmentRepositoryProvider);
  return GetInvestmentsUseCase(repo);
});

final addInvestmentUseCaseProvider = Provider<AddInvestmentUseCase>((ref) {
  final repo = ref.watch(investmentRepositoryProvider);
  return AddInvestmentUseCase(repo);
});

final updateInvestmentUseCaseProvider = Provider<UpdateInvestmentUseCase>((ref) {
  final repo = ref.watch(investmentRepositoryProvider);
  return UpdateInvestmentUseCase(repo);
});

final deleteInvestmentUseCaseProvider = Provider<DeleteInvestmentUseCase>((ref) {
  final repo = ref.watch(investmentRepositoryProvider);
  return DeleteInvestmentUseCase(repo);
});

final investmentsProvider = FutureProvider<List<Investment>>((ref) async {
  final useCase = ref.watch(getInvestmentsUseCaseProvider);
  return await useCase.call();
}); 