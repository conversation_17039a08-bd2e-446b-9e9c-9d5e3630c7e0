import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:financial_app/core/database/database_provider.dart';
import '../../data/repositories/daily_limit_repository_impl.dart';
import '../../application/usecases/get_daily_limits_usecase.dart';
import '../../core/database/app_database.dart';

final dailyLimitRepositoryProvider = Provider<DailyLimitRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return DailyLimitRepositoryImpl(db);
});

final getDailyLimitsUseCaseProvider = Provider<GetDailyLimitsUseCase>((ref) {
  final repo = ref.watch(dailyLimitRepositoryProvider);
  return GetDailyLimitsUseCase(repo);
});

final addDailyLimitUseCaseProvider = Provider((ref) {
  final repo = ref.watch(dailyLimitRepositoryProvider);
  return (DailyLimitsCompanion limit) => repo.addDailyLimit(limit);
});

final updateDailyLimitUseCaseProvider = Provider((ref) {
  final repo = ref.watch(dailyLimitRepositoryProvider);
  return (int id, DailyLimitsCompanion limit) => repo.updateDailyLimit(id, limit);
});

final deleteDailyLimitUseCaseProvider = Provider((ref) {
  final repo = ref.watch(dailyLimitRepositoryProvider);
  return (int id) => repo.deleteDailyLimit(id);
});

final dailyLimitsProvider = FutureProvider<List<DailyLimit>>((ref) async {
  final repo = ref.watch(dailyLimitRepositoryProvider);
  return repo.getDailyLimits();
}); 