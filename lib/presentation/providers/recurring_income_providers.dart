import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/database_provider.dart';
import '../../core/database/app_database.dart';
import '../../data/repositories/recurring_income_repository_impl.dart';
import '../../application/usecases/add_recurring_income_usecase.dart';
import '../../application/usecases/get_due_recurring_incomes_usecase.dart';
import '../../application/usecases/process_recurring_income_usecase.dart';
import '../../core/services/recurring_income_service.dart';
import '../../domain/repositories/recurring_income_repository.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../providers/transaction_providers.dart';

// Repository providers
final recurringIncomeRepositoryProvider = Provider<RecurringIncomeRepository>((ref) {
  final database = ref.watch(databaseProvider);
  return RecurringIncomeRepositoryImpl(database);
});

// Use case providers
final addRecurringIncomeUseCaseProvider = Provider<AddRecurringIncomeUseCase>((ref) {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return AddRecurringIncomeUseCase(repository);
});

final getDueRecurringIncomesUseCaseProvider = Provider<GetDueRecurringIncomesUseCase>((ref) {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return GetDueRecurringIncomesUseCase(repository);
});

final updateRecurringIncomeUseCaseProvider = Provider((ref) {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return (int id, RecurringIncomesCompanion recurringIncome) => repository.updateRecurringIncome(id, recurringIncome);
});

final deleteRecurringIncomeUseCaseProvider = Provider((ref) {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return (int id) => repository.deleteRecurringIncome(id);
});

final processRecurringIncomeUseCaseProvider = Provider<ProcessRecurringIncomeUseCase>((ref) {
  final recurringIncomeRepository = ref.watch(recurringIncomeRepositoryProvider);
  final transactionRepository = ref.watch(transactionRepositoryProvider);
  return ProcessRecurringIncomeUseCase(recurringIncomeRepository, transactionRepository);
});

// Service provider
final recurringIncomeServiceProvider = Provider<RecurringIncomeService>((ref) {
  final getDueRecurringIncomesUseCase = ref.watch(getDueRecurringIncomesUseCaseProvider);
  final processRecurringIncomeUseCase = ref.watch(processRecurringIncomeUseCaseProvider);
  return RecurringIncomeService(getDueRecurringIncomesUseCase, processRecurringIncomeUseCase);
});

// Data providers
final recurringIncomesProvider = FutureProvider((ref) async {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return await repository.getAllRecurringIncomes();
});

final activeRecurringIncomesProvider = FutureProvider((ref) async {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return await repository.getActiveRecurringIncomes();
});

final stoppedRecurringIncomesProvider = FutureProvider((ref) async {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  return await repository.getStoppedRecurringIncomes();
});

final dueRecurringIncomesProvider = FutureProvider((ref) async {
  final repository = ref.watch(recurringIncomeRepositoryProvider);
  final today = DateTime.now();
  return await repository.getDueRecurringIncomes(today);
}); 