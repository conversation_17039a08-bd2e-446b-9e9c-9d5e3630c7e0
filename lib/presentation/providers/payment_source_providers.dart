import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/bank_account_repository_impl.dart';
import '../../data/repositories/credit_card_repository_impl.dart';
import '../../data/repositories/personal_creditor_repository_impl.dart';
import '../../application/usecases/get_payment_sources_usecase.dart';
import '../../core/database/database_provider.dart' as db;

final bankAccountRepositoryProvider = Provider<BankAccountRepositoryImpl>((ref) {
  // Assume databaseProvider is already defined elsewhere
  final database = ref.watch(db.databaseProvider);
  return BankAccountRepositoryImpl(database);
});

final creditCardRepositoryProvider = Provider<CreditCardRepositoryImpl>((ref) {
  final database = ref.watch(db.databaseProvider);
  return CreditCardRepositoryImpl(database);
});

final personalCreditorRepositoryProvider = Provider<PersonalCreditorRepositoryImpl>((ref) {
  final database = ref.watch(db.databaseProvider);
  return PersonalCreditorRepositoryImpl(database);
});

final getPaymentSourcesUseCaseProvider = Provider<GetPaymentSourcesUseCase>((ref) {
  final bankRepo = ref.watch(bankAccountRepositoryProvider);
  final cardRepo = ref.watch(creditCardRepositoryProvider);
  final creditorRepo = ref.watch(personalCreditorRepositoryProvider);
  return GetPaymentSourcesUseCase(
    bankAccountRepository: bankRepo,
    creditCardRepository: cardRepo,
    personalCreditorRepository: creditorRepo,
  );
});

final paymentSourcesProvider = FutureProvider((ref) async {
  final useCase = ref.watch(getPaymentSourcesUseCaseProvider);
  return await useCase();
}); 