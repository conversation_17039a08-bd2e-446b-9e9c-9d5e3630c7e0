import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/transaction_repository_impl.dart';
import '../../application/usecases/add_transaction_usecase.dart';
import '../../application/usecases/get_transactions_usecase.dart';
import '../../application/usecases/update_transaction_usecase.dart';
import '../../application/usecases/delete_transaction_usecase.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../providers/database_provider.dart';

// Repository Provider
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  final databaseAsync = ref.watch(databaseProvider);
  return databaseAsync.when(
    data: (database) => TransactionRepositoryImpl(database),
    loading: () => throw Exception('Database not ready'),
    error: (error, stack) => throw Exception('Database error: $error'),
  );
});

// Use Case Providers
final addTransactionUseCaseProvider = Provider<AddTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return AddTransactionUseCase(repository);
});

final getTransactionsUseCaseProvider = Provider<GetTransactionsUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return GetTransactionsUseCase(repository);
});

final updateTransactionUseCaseProvider = Provider<UpdateTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return UpdateTransactionUseCase(repository);
});

final deleteTransactionUseCaseProvider = Provider<DeleteTransactionUseCase>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return DeleteTransactionUseCase(repository);
});

// Data Providers
final transactionsProvider = FutureProvider<List<TransactionWithCategory>>((ref) async {
  final useCase = ref.watch(getTransactionsUseCaseProvider);
  return await useCase.call();
});

final transactionsByTypeProvider = FutureProvider.family<List<TransactionWithCategory>, String>((ref, type) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getTransactionsByType(type);
});

final transactionsByAccountProvider = FutureProvider.family<List<TransactionWithCategory>, int>((ref, accountId) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getTransactionsByAccountId(accountId);
});

final borrowedContactsSummaryProvider = FutureProvider<List<BorrowedContactSummary>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getBorrowedContactsSummary();
});

final allContactsSummaryProvider = FutureProvider<List<ContactSummary>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getAllContactsSummary();
});

final totalBalanceProvider = FutureProvider<double>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getTotalBalance();
});

final categoryTotalsProvider = FutureProvider<Map<String, double>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return await repository.getCategoryTotals();
});

final paginatedTransactionsProvider = FutureProvider.family<List<TransactionWithCategory>, Map<String, int>>((ref, params) async {
  final repository = ref.watch(transactionRepositoryProvider);
  final limit = params['limit'] ?? 20;
  final offset = params['offset'] ?? 0;
  return await repository.getTransactionsPaginated(limit: limit, offset: offset);
}); 