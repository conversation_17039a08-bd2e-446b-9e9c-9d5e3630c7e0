import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/database_provider.dart';
import '../../data/repositories/loan_repository_impl.dart';
import '../../application/usecases/get_unified_loans_usecase.dart';

final loanRepositoryProvider = Provider<LoanRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return LoanRepositoryImpl(db);
});

final getUnifiedLoansUseCaseProvider = Provider<GetUnifiedLoansUseCase>((ref) {
  final repo = ref.watch(loanRepositoryProvider);
  return GetUnifiedLoansUseCase(repo);
});

final unifiedLoansProvider = FutureProvider((ref) async {
  final useCase = ref.watch(getUnifiedLoansUseCaseProvider);
  return await useCase();
}); 