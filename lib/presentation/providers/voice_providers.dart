import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/voice_service.dart';

final voiceServiceProvider = Provider<VoiceService>((ref) {
  return VoiceService();
});

final voiceInitializedProvider = FutureProvider<bool>((ref) async {
  final voiceService = ref.read(voiceServiceProvider);
  await voiceService.initialize();
  return voiceService.isAvailable;
});

final voiceListeningProvider = StateProvider<bool>((ref) => false);

final voiceTranscriptProvider = StateProvider<String>((ref) => '');

final parsedExpenseProvider = StateProvider<Map<String, dynamic>?>((ref) => null); 