import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../data/repositories/category_repository_impl.dart';
import '../../application/usecases/get_categories_usecase.dart';
import 'package:financial_app/core/database/database_provider.dart';

final categoryRepositoryProvider = Provider<CategoryRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return CategoryRepositoryImpl(db);
});

final getCategoriesUseCaseProvider = Provider<GetCategoriesUseCase>((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return GetCategoriesUseCase(repo);
});

final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  final repo = ref.watch(categoryRepositoryProvider);
  return repo.getCategories();
});

final addCategoryUseCaseProvider = Provider((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return (CategoriesCompanion category) => repo.addCategory(category);
});

final updateCategoryUseCaseProvider = Provider((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return (int id, CategoriesCompanion category) => repo.updateCategory(id, category);
});

final deleteCategoryUseCaseProvider = Provider((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return (int id) => repo.deleteCategory(id);
});

final getCategoryByIdUseCaseProvider = Provider((ref) {
  final repo = ref.watch(categoryRepositoryProvider);
  return (int id) => repo.getCategoryById(id);
}); 