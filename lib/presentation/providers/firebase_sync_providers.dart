import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/firebase_sync_service.dart';
import 'package:financial_app/core/database/database_provider.dart';

final firebaseSyncServiceProvider = Provider<FirebaseSyncService>((ref) {
  final db = ref.watch(databaseProvider);
  return FirebaseSyncService(db);
});

final firebaseBackupProvider = FutureProvider<void>((ref) async {
  final sync = ref.watch(firebaseSyncServiceProvider);
  await sync.backupAll();
});

final firebaseRestoreProvider = FutureProvider<void>((ref) async {
  final sync = ref.watch(firebaseSyncServiceProvider);
  await sync.restoreAll();
}); 