import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart';
import '../../data/repositories/income_repository_impl.dart';
import '../../application/usecases/add_income_usecase.dart';
import '../../application/usecases/delete_income_usecase.dart';
import '../../application/usecases/update_income_usecase.dart';
import '../../application/usecases/get_incomes_usecase.dart';

final incomeRepositoryProvider = Provider<IncomeRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return IncomeRepositoryImpl(db);
});

final getIncomesUseCaseProvider = Provider<GetIncomesUseCase>((ref) {
  final repo = ref.watch(incomeRepositoryProvider);
  return GetIncomesUseCase(repo);
});

final addIncomeUseCaseProvider = Provider<AddIncomeUseCase>((ref) {
  final repo = ref.watch(incomeRepositoryProvider);
  return AddIncomeUseCase(repo);
});

final updateIncomeUseCaseProvider = Provider<UpdateIncomeUseCase>((ref) {
  final repo = ref.watch(incomeRepositoryProvider);
  return UpdateIncomeUseCase(repo);
});

final deleteIncomeUseCaseProvider = Provider<DeleteIncomeUseCase>((ref) {
  final repo = ref.watch(incomeRepositoryProvider);
  return DeleteIncomeUseCase(repo);
});

final incomesProvider = FutureProvider<List<Transaction>>((ref) async {
  final useCase = ref.watch(getIncomesUseCaseProvider);
  return await useCase.call();
}); 