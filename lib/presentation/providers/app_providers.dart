import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:financial_app/core/database/database_provider.dart';

final seedCategoriesProvider = FutureProvider<void>((ref) async {
  final db = ref.watch(databaseProvider);
  await db.seedCategories();
});

final ensureCashAccountProvider = FutureProvider<void>((ref) async {
  final db = ref.watch(databaseProvider);
  await db.ensureCashAccountExists();
});

final initializeDatabaseProvider = FutureProvider<void>((ref) async {
  final db = ref.watch(databaseProvider);
  
  // Initialize database with cash account and categories
  await db.initializeDatabase();
});

final resetDatabaseProvider = FutureProvider<void>((ref) async {
  final db = ref.watch(databaseProvider);
  
  // Reset and reinitialize database
  await db.resetAndInitializeDatabase();
});

final authProvider = Provider<String>((ref) {
  // TODO: Replace with actual auth logic
  return 'mock_user_id';
});

final mockNetWorthProvider = Provider<double>((ref) => 100000.0);
final mockBudgetsProvider = Provider<List<Map<String, dynamic>>>((ref) => [
  {'category': 'Groceries', 'budgeted': 6000, 'spent': 5000},
  {'category': 'Transport', 'budgeted': 1500, 'spent': 1000},
]); 