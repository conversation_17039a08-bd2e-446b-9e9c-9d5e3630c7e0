import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart' as db;
import '../../data/repositories/savings_repository_impl.dart';
import '../../application/usecases/add_saving_usecase.dart';
import '../../application/usecases/delete_saving_usecase.dart';
import '../../application/usecases/update_saving_usecase.dart';
import '../../application/usecases/get_savings_usecase.dart';

final savingsRepositoryProvider = Provider<SavingsRepositoryImpl>((ref) {
  final database = ref.watch(db.databaseProvider);
  return SavingsRepositoryImpl(database);
});

final getSavingsUseCaseProvider = Provider<GetSavingsUseCase>((ref) {
  final repo = ref.watch(savingsRepositoryProvider);
  return GetSavingsUseCase(repo);
});

final addSavingsUseCaseProvider = Provider<AddSavingUseCase>((ref) {
  final repo = ref.watch(savingsRepositoryProvider);
  return AddSavingUseCase(repo);
});

final updateSavingsUseCaseProvider = Provider<UpdateSavingUseCase>((ref) {
  final repo = ref.watch(savingsRepositoryProvider);
  return UpdateSavingUseCase(repo);
});

final deleteSavingsUseCaseProvider = Provider<DeleteSavingUseCase>((ref) {
  final repo = ref.watch(savingsRepositoryProvider);
  return DeleteSavingUseCase(repo);
});

final savingsProvider = FutureProvider<List<Saving>>((ref) async {
  final repo = ref.watch(savingsRepositoryProvider);
  return await repo.getSavings();
}); 