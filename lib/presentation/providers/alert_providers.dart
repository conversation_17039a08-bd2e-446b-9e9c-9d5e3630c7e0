import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/alert_service.dart';
import '../../core/database/database_provider.dart';

// Provider for alert service
final alertServiceProvider = Provider<AlertService>((ref) {
  final database = ref.watch(databaseProvider);
  return AlertService(database);
});

// Provider for all alerts
final allAlertsProvider = FutureProvider<List<BaseAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.getAllAlerts();
});

// Provider for unread alerts count
final unreadAlertsCountProvider = FutureProvider<int>((ref) async {
  final alerts = await ref.watch(allAlertsProvider.future);
  return alerts.where((alert) => !alert.isRead).length;
});

// Provider for critical alerts only
final criticalAlertsProvider = FutureProvider<List<BaseAlert>>((ref) async {
  final alerts = await ref.watch(allAlertsProvider.future);
  return alerts.where((alert) => alert.severity == AlertSeverity.critical).toList();
});

// Provider for budget alerts only
final budgetAlertsProvider = FutureProvider<List<BudgetAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.checkBudgetAlerts();
});

// Provider for EMI alerts only
final emiAlertsProvider = FutureProvider<List<EmiAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.checkEmiDueAlerts();
});

// Provider for credit card alerts only
final creditCardAlertsProvider = FutureProvider<List<CreditCardAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.checkCreditCardDueAlerts();
});

// Provider for debt alerts only
final debtAlertsProvider = FutureProvider<List<DebtAlert>>((ref) async {
  final alertService = ref.watch(alertServiceProvider);
  return await alertService.checkDebtDueAlerts();
}); 