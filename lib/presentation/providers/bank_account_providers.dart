import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:financial_app/core/database/database_provider.dart';
import '../../data/repositories/bank_account_repository_impl.dart';
import '../../application/usecases/get_bank_accounts_usecase.dart';
import '../../application/usecases/add_bank_account_usecase.dart';
import '../../application/usecases/update_bank_account_usecase.dart';
import '../../application/usecases/delete_bank_account_usecase.dart';

final bankAccountRepositoryProvider = Provider<BankAccountRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return BankAccountRepositoryImpl(db);
});

final getBankAccountsUseCaseProvider = Provider<GetBankAccountsUseCase>((ref) {
  final repo = ref.watch(bankAccountRepositoryProvider);
  return GetBankAccountsUseCase(repo);
});

final addBankAccountUseCaseProvider = Provider<AddBankAccountUseCase>((ref) {
  final repo = ref.watch(bankAccountRepositoryProvider);
  return AddBankAccountUseCase(repo);
});

final updateBankAccountUseCaseProvider = Provider<UpdateBankAccountUseCase>((ref) {
  final repo = ref.watch(bankAccountRepositoryProvider);
  return UpdateBankAccountUseCase(repo);
});

final deleteBankAccountUseCaseProvider = Provider<DeleteBankAccountUseCase>((ref) {
  final repo = ref.watch(bankAccountRepositoryProvider);
  return DeleteBankAccountUseCase(repo);
});

final bankAccountsProvider = FutureProvider((ref) async {
  final useCase = ref.watch(getBankAccountsUseCaseProvider);
  return await useCase();
}); 