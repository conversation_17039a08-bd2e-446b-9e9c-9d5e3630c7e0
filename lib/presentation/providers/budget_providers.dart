import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../data/repositories/budget_repository_impl.dart';
import '../../application/usecases/get_budgets_usecase.dart';
import '../../application/usecases/add_budget_usecase.dart';
import '../../application/usecases/update_budget_usecase.dart';
import '../../application/usecases/delete_budget_usecase.dart';
import '../../domain/repositories/budget_repository.dart';
import 'package:financial_app/core/database/database_provider.dart';

final budgetRepositoryProvider = Provider<BudgetRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return BudgetRepositoryImpl(db);
});

final getBudgetsUseCaseProvider = Provider<GetBudgetsUseCase>((ref) {
  final repo = ref.watch(budgetRepositoryProvider);
  return GetBudgetsUseCase(repo);
});

final addBudgetUseCaseProvider = Provider<AddBudgetUseCase>((ref) {
  final repo = ref.watch(budgetRepositoryProvider);
  return AddBudgetUseCase(repo);
});

final updateBudgetUseCaseProvider = Provider<UpdateBudgetUseCase>((ref) {
  final repo = ref.watch(budgetRepositoryProvider);
  return UpdateBudgetUseCase(repo);
});

final deleteBudgetUseCaseProvider = Provider<DeleteBudgetUseCase>((ref) {
  final repo = ref.watch(budgetRepositoryProvider);
  return DeleteBudgetUseCase(repo);
});

final budgetsProvider = FutureProvider<List<BudgetWithCategory>>((ref) async {
  final useCase = ref.watch(getBudgetsUseCaseProvider);
  return await useCase();
}); 