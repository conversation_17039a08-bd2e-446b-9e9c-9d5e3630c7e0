import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/stock_api_service.dart';

// Stock API service provider
final stockApiServiceProvider = Provider<StockApiService>((ref) {
  return StockApiService();
});

// Stock symbols search provider
final searchSymbolsProvider = FutureProvider.family<List<StockSymbol>, String>((ref, query) async {
  final stockApiService = ref.watch(stockApiServiceProvider);
  return await stockApiService.searchSymbols(query);
});

// Stock price provider
final stockPriceProvider = FutureProvider.family<StockPrice?, String>((ref, symbol) async {
  final stockApiService = ref.watch(stockApiServiceProvider);
  return await stockApiService.getStockPrice(symbol);
});

// Popular symbols provider
final getPopularSymbolsProvider = FutureProvider<List<StockSymbol>>((ref) async {
  final stockApiService = ref.watch(stockApiServiceProvider);
  return await stockApiService.getPopularSymbols();
}); 