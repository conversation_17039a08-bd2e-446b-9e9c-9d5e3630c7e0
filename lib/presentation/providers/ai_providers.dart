import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/ai_service.dart';

// Provider for AI service
final aiServiceProvider = Provider<AIService>((ref) {
  return AIService();
});

// Provider for saving tips
final savingTipsProvider = FutureProvider.family<List<String>, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getSavingTips(
    monthlyIncome: params['monthlyIncome'] ?? 0.0,
    monthlyExpenses: params['monthlyExpenses'] ?? 0.0,
    topCategories: params['topCategories'] ?? [],
    savingsGoal: params['savingsGoal'] ?? 0.0,
  );
});

// Provider for expense insight
final expenseInsightProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getExpenseInsight(
    category: params['category'] ?? '',
    amount: params['amount'] ?? 0.0,
    description: params['description'] ?? '',
    monthlyBudget: params['monthlyBudget'] ?? 0.0,
    spentThisMonth: params['spentThisMonth'] ?? 0.0,
  );
});

// Provider for budget optimization
final budgetOptimizationProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getBudgetOptimization(
    categorySpending: params['categorySpending'] ?? {},
    totalIncome: params['totalIncome'] ?? 0.0,
    totalExpenses: params['totalExpenses'] ?? 0.0,
  );
});

// Provider for investment advice
final investmentAdviceProvider = FutureProvider.family<String, Map<String, dynamic>>((ref, params) async {
  final aiService = ref.watch(aiServiceProvider);
  return await aiService.getInvestmentAdvice(
    monthlyIncome: params['monthlyIncome'] ?? 0.0,
    monthlyExpenses: params['monthlyExpenses'] ?? 0.0,
    currentSavings: params['currentSavings'] ?? 0.0,
    age: params['age'] ?? 30,
    riskTolerance: params['riskTolerance'] ?? 'moderate',
  );
}); 