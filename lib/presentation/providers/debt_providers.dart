import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart';
import '../../data/repositories/debt_repository_impl.dart';
import '../../application/usecases/add_debt_usecase.dart';
import '../../application/usecases/delete_debt_usecase.dart';
import '../../application/usecases/update_debt_usecase.dart';
import '../../application/usecases/get_debts_usecase.dart';

final debtRepositoryProvider = Provider<DebtRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return DebtRepositoryImpl(db);
});

final getDebtsUseCaseProvider = Provider<GetDebtsUseCase>((ref) {
  final repo = ref.watch(debtRepositoryProvider);
  return GetDebtsUseCase(repo);
});

final addDebtUseCaseProvider = Provider<AddDebtUseCase>((ref) {
  final repo = ref.watch(debtRepositoryProvider);
  return AddDebtUseCase(repo);
});

final updateDebtUseCaseProvider = Provider<UpdateDebtUseCase>((ref) {
  final repo = ref.watch(debtRepositoryProvider);
  return UpdateDebtUseCase(repo);
});

final deleteDebtUseCaseProvider = Provider<DeleteDebtUseCase>((ref) {
  final repo = ref.watch(debtRepositoryProvider);
  return DeleteDebtUseCase(repo);
});

final debtsProvider = FutureProvider<List<Debt>>((ref) async {
  final useCase = ref.watch(getDebtsUseCaseProvider);
  return await useCase.call();
}); 