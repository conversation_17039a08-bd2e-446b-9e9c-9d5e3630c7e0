import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart';
import '../../data/repositories/allocation_repository_impl.dart';
import '../../application/usecases/add_allocation_usecase.dart';
import '../../application/usecases/delete_allocation_usecase.dart';
import '../../application/usecases/update_allocation_usecase.dart';
import '../../application/usecases/get_allocations_usecase.dart';

final allocationRepositoryProvider = Provider<AllocationRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return AllocationRepositoryImpl(db);
});

final getAllocationsUseCaseProvider = Provider<GetAllocationsUseCase>((ref) {
  final repo = ref.watch(allocationRepositoryProvider);
  return GetAllocationsUseCase(repo);
});

final addAllocationUseCaseProvider = Provider<AddAllocationUseCase>((ref) {
  final repo = ref.watch(allocationRepositoryProvider);
  return AddAllocationUseCase(repo);
});

final updateAllocationUseCaseProvider = Provider<UpdateAllocationUseCase>((ref) {
  final repo = ref.watch(allocationRepositoryProvider);
  return UpdateAllocationUseCase(repo);
});

final deleteAllocationUseCaseProvider = Provider<DeleteAllocationUseCase>((ref) {
  final repo = ref.watch(allocationRepositoryProvider);
  return DeleteAllocationUseCase(repo);
});

final allocationsProvider = FutureProvider<List<Allocation>>((ref) async {
  final useCase = ref.watch(getAllocationsUseCaseProvider);
  return await useCase.call();
}); 