import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/app_database.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:path/path.dart' as path;

// SQLite database provider with persistent storage
final databaseProvider = FutureProvider<AppDatabase>((ref) async {
  try {
    final documentsDir = await getApplicationDocumentsDirectory();
    final dbPath = File(path.join(documentsDir.path, 'financial_app.db'));
    print('DEBUG: Creating SQLite database at: ${dbPath.path}');
    return AppDatabase(NativeDatabase(dbPath));
  } catch (e) {
    print('Error creating SQLite database: $e');
    // Fallback to memory database
    return AppDatabase(NativeDatabase.memory());
  }
});

// Memory database provider (for testing)
final memoryDatabaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase(NativeDatabase.memory());
}); 