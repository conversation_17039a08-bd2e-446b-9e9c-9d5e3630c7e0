import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/sms_service.dart';
import '../../core/database/database_provider.dart' as db;

final smsServiceProvider = Provider<SmsService>((ref) {
  final database = ref.watch(db.databaseProvider);
  return SmsService(database);
});

final smsPermissionProvider = FutureProvider<bool>((ref) async {
  final smsService = ref.watch(smsServiceProvider);
  return await smsService.hasSmsPermission();
}); 