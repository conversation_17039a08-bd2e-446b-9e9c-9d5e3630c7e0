import '../../domain/repositories/expense_repository.dart';
import '../../domain/repositories/transaction_repository.dart';
import '../../data/repositories/expense_repository_impl.dart';
import '../../application/usecases/get_expenses_usecase.dart';
import '../../application/usecases/add_expense_usecase.dart';
import '../../application/usecases/update_expense_usecase.dart';
import '../../application/usecases/delete_expense_usecase.dart';
import '../../core/database/app_database.dart';
import '../../core/database/database_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final expenseRepositoryProvider = Provider<ExpenseRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return ExpenseRepositoryImpl(db);
});

final getExpensesUseCaseProvider = Provider<GetExpensesUseCase>((ref) {
  final repo = ref.watch(expenseRepositoryProvider);
  return GetExpensesUseCase(repo);
});

final addExpenseUseCaseProvider = Provider<AddExpenseUseCase>((ref) {
  final repo = ref.watch(expenseRepositoryProvider);
  return AddExpenseUseCase(repo);
});

final updateExpenseUseCaseProvider = Provider<UpdateExpenseUseCase>((ref) {
  final repo = ref.watch(expenseRepositoryProvider);
  return UpdateExpenseUseCase(repo);
});

final deleteExpenseUseCaseProvider = Provider<DeleteExpenseUseCase>((ref) {
  final repo = ref.watch(expenseRepositoryProvider);
  return DeleteExpenseUseCase(repo);
});

final expensesProvider = FutureProvider<List<TransactionWithCategory>>((ref) async {
  final useCase = ref.watch(getExpensesUseCaseProvider);
  return await useCase();
});

final borrowedContactsSummaryProvider = FutureProvider<List<BorrowedContactSummary>>((ref) async {
  final repo = ref.watch(expenseRepositoryProvider);
  return await repo.getBorrowedContactsSummary();
});

final allContactsSummaryProvider = FutureProvider<List<ContactSummary>>((ref) async {
  final repo = ref.watch(expenseRepositoryProvider);
  return await repo.getAllContactsSummary();
}); 