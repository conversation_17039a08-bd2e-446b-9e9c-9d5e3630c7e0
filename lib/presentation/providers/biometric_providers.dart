import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:local_auth/local_auth.dart';
import '../../core/services/biometric_service.dart';

final biometricServiceProvider = Provider<BiometricService>((ref) {
  return BiometricService();
});

// Provider to check if biometric is available
final biometricAvailableProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  return service.isBiometricAvailable();
});

// Provider to check if PIN is set
final pinSetProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  return service.isPinSet();
});

// Provider to check if biometric is enabled
final biometricEnabledProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  return service.isBiometricEnabled();
});

// Provider to get authentication method
final authenticationMethodProvider = FutureProvider<String>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  return service.getAuthenticationMethod();
});

// Provider to check if app should be locked
final biometricLockProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  final authMethod = await service.getAuthenticationMethod();
  return authMethod != 'none';
});

// Provider for available biometric types
final availableBiometricsProvider = FutureProvider<List<BiometricType>>((ref) async {
  final service = ref.watch(biometricServiceProvider);
  return service.getAvailableBiometrics();
}); 