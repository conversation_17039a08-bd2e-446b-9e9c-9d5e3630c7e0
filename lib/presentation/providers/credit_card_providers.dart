import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:financial_app/core/database/database_provider.dart';
import '../../data/repositories/credit_card_repository_impl.dart';
import '../../data/repositories/credit_card_transaction_repository_impl.dart';
import '../../data/repositories/emi_schedule_repository_impl.dart';
import '../../application/usecases/get_credit_cards_usecase.dart';
import '../../application/usecases/add_credit_card_usecase.dart';
import '../../application/usecases/update_credit_card_usecase.dart';
import '../../application/usecases/delete_credit_card_usecase.dart';
import '../../application/usecases/get_credit_card_transactions_usecase.dart';
import '../../application/usecases/add_credit_card_transaction_usecase.dart';
import '../../application/usecases/update_credit_card_transaction_usecase.dart';
import '../../application/usecases/delete_credit_card_transaction_usecase.dart';
import '../../application/usecases/get_emi_schedules_usecase.dart';
import '../../application/usecases/add_emi_schedule_usecase.dart';
import '../../application/usecases/update_emi_schedule_usecase.dart';
import '../../application/usecases/delete_emi_schedule_usecase.dart';

final creditCardRepositoryProvider = Provider<CreditCardRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return CreditCardRepositoryImpl(db);
});

final getCreditCardsUseCaseProvider = Provider<GetCreditCardsUseCase>((ref) {
  final repo = ref.watch(creditCardRepositoryProvider);
  return GetCreditCardsUseCase(repo);
});

final addCreditCardUseCaseProvider = Provider<AddCreditCardUseCase>((ref) {
  final repo = ref.watch(creditCardRepositoryProvider);
  return AddCreditCardUseCase(repo);
});

final updateCreditCardUseCaseProvider = Provider<UpdateCreditCardUseCase>((ref) {
  final repo = ref.watch(creditCardRepositoryProvider);
  return UpdateCreditCardUseCase(repo);
});

final deleteCreditCardUseCaseProvider = Provider<DeleteCreditCardUseCase>((ref) {
  final repo = ref.watch(creditCardRepositoryProvider);
  return DeleteCreditCardUseCase(repo);
});

final creditCardsProvider = FutureProvider((ref) async {
  final useCase = ref.watch(getCreditCardsUseCaseProvider);
  return await useCase();
});

final creditCardTransactionRepositoryProvider = Provider<CreditCardTransactionRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return CreditCardTransactionRepositoryImpl(db);
});

final getCreditCardTransactionsUseCaseProvider = Provider<GetCreditCardTransactionsUseCase>((ref) {
  final repo = ref.watch(creditCardTransactionRepositoryProvider);
  return GetCreditCardTransactionsUseCase(repo);
});

final addCreditCardTransactionUseCaseProvider = Provider<AddCreditCardTransactionUseCase>((ref) {
  final repo = ref.watch(creditCardTransactionRepositoryProvider);
  return AddCreditCardTransactionUseCase(repo);
});

final updateCreditCardTransactionUseCaseProvider = Provider<UpdateCreditCardTransactionUseCase>((ref) {
  final repo = ref.watch(creditCardTransactionRepositoryProvider);
  return UpdateCreditCardTransactionUseCase(repo);
});

final deleteCreditCardTransactionUseCaseProvider = Provider<DeleteCreditCardTransactionUseCase>((ref) {
  final repo = ref.watch(creditCardTransactionRepositoryProvider);
  return DeleteCreditCardTransactionUseCase(repo);
});

final emiScheduleRepositoryProvider = Provider<EmiScheduleRepositoryImpl>((ref) {
  final db = ref.watch(databaseProvider);
  return EmiScheduleRepositoryImpl(db);
});

final getEmiSchedulesUseCaseProvider = Provider<GetEmiSchedulesUseCase>((ref) {
  final repo = ref.watch(emiScheduleRepositoryProvider);
  return GetEmiSchedulesUseCase(repo);
});

final addEmiScheduleUseCaseProvider = Provider<AddEmiScheduleUseCase>((ref) {
  final repo = ref.watch(emiScheduleRepositoryProvider);
  return AddEmiScheduleUseCase(repo);
});

final updateEmiScheduleUseCaseProvider = Provider<UpdateEmiScheduleUseCase>((ref) {
  final repo = ref.watch(emiScheduleRepositoryProvider);
  return UpdateEmiScheduleUseCase(repo);
});

final deleteEmiScheduleUseCaseProvider = Provider<DeleteEmiScheduleUseCase>((ref) {
  final repo = ref.watch(emiScheduleRepositoryProvider);
  return DeleteEmiScheduleUseCase(repo);
}); 