import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/services/firebase_auth_service.dart';

final firebaseAuthServiceProvider = Provider<FirebaseAuthService>((ref) {
  return FirebaseAuthService();
});

final userProvider = StreamProvider<AppUser?>((ref) {
  final authService = ref.watch(firebaseAuthServiceProvider);
  return authService.userStream;
});

final signInProvider = FutureProvider.family<bool, Map<String, String>>((ref, credentials) async {
  final authService = ref.watch(firebaseAuthServiceProvider);
  return await authService.signInWithEmailAndPassword(
    credentials['email'] ?? '',
    credentials['password'] ?? '',
  );
}); 