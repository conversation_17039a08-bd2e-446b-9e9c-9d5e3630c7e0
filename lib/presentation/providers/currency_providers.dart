import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/database/database_provider.dart' as db;
import '../../core/services/currency_service.dart';
import '../../core/database/app_database.dart';

// Currency service provider
final currencyServiceProvider = Provider<CurrencyService>((ref) {
  final database = ref.watch(db.databaseProvider);
  return CurrencyService(database);
});

// Current currency provider
final currentCurrencyProvider = FutureProvider<CurrencySetting?>((ref) async {
  final currencyService = ref.watch(currencyServiceProvider);
  return await currencyService.getDefaultCurrency();
});

// All currencies provider
final allCurrenciesProvider = FutureProvider<List<CurrencySetting>>((ref) async {
  final currencyService = ref.watch(currencyServiceProvider);
  return await currencyService.getAllCurrencies();
});

// Currency formatter provider
final currencyFormatterProvider = Provider<CurrencyService>((ref) {
  return ref.watch(currencyServiceProvider);
});

// Initialize currencies provider
final initializeCurrenciesProvider = FutureProvider<void>((ref) async {
  final currencyService = ref.watch(currencyServiceProvider);
  await currencyService.initializeDefaultCurrencies();
}); 