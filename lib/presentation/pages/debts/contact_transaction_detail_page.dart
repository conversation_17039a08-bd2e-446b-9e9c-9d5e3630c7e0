import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/debt_providers.dart';
import '../../providers/database_provider.dart';
import '../../../core/database/app_database.dart';
import 'package:intl/intl.dart';
import 'package:drift/drift.dart' as drift;

final getContactTransactionsProvider = FutureProvider.family<List<Transaction>, String?>((ref, contactId) async {
  // Removed all references to getDebtPaymentsUseCaseProvider and legacy debt payment logic. Use Transactions model or add TODO for reimplementation.
  return await Future.value([]);
});

class ContactTransactionDetailPage extends ConsumerWidget {
  final String? contactId;
  final String? contactName;
  final String? contactPhone;

  const ContactTransactionDetailPage({
    super.key,
    required this.contactId,
    required this.contactName,
    required this.contactPhone,
  });

  void _showAddTransactionDialog(BuildContext context, WidgetRef ref) {
    final formKey = GlobalKey<FormState>();
    final amountController = TextEditingController();
    final descController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    String type = 'Borrowing';
    bool isSaving = false;
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            void clearFields() {
              amountController.clear();
              descController.clear();
              selectedDate = DateTime.now();
              type = 'Borrowing';
              setState(() {});
            }
            return AlertDialog(
              title: const Text('Add Transaction'),
              content: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 2.0),
                  child: Form(
                    key: formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        const Text('Transaction Details', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 10),
                        TextFormField(
                          controller: amountController,
                          decoration: const InputDecoration(
                            labelText: 'Amount',
                            prefixText: '₹ ',
                            helperText: 'Enter the transaction amount',
                          ),
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                          autofocus: true,
                          validator: (val) {
                            final v = double.tryParse(val ?? '');
                            if (v == null || v <= 0) return 'Enter a valid amount';
                            return null;
                          },
                        ),
                        const SizedBox(height: 12),
                        TextFormField(
                          controller: descController,
                          decoration: const InputDecoration(
                            labelText: 'Description',
                            helperText: 'E.g. Lunch, Repayment, etc.',
                          ),
                          textCapitalization: TextCapitalization.sentences,
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(child: Text('Date: ${DateFormat('dd MMM yyyy').format(selectedDate)}')),
                            IconButton(
                              icon: const Icon(Icons.calendar_today),
                              onPressed: () async {
                                final picked = await showDatePicker(
                                  context: context,
                                  initialDate: selectedDate,
                                  firstDate: DateTime(DateTime.now().year - 2),
                                  lastDate: DateTime(DateTime.now().year + 2),
                                );
                                if (picked != null) setState(() => selectedDate = picked);
                              },
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        DropdownButtonFormField<String>(
                          value: type,
                          items: const [
                            DropdownMenuItem(value: 'Borrowing', child: Text('Borrowing')),
                            DropdownMenuItem(value: 'Repayment', child: Text('Repayment')),
                          ],
                          onChanged: (val) => setState(() => type = val!),
                          decoration: const InputDecoration(labelText: 'Type'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: isSaving ? null : () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton.icon(
                  icon: isSaving ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)) : const Icon(Icons.save),
                  label: const Text('Save'),
                  onPressed: isSaving
                      ? null
                      : () async {
                          if (!formKey.currentState!.validate()) return;
                          setState(() => isSaving = true);
                          final amount = double.tryParse(amountController.text) ?? 0;
                          final isBorrowing = type == 'Borrowing';
                          final companion = TransactionsCompanion(
                            amount: drift.Value(amount),
                            date: drift.Value(selectedDate),
                            type: drift.Value(isBorrowing ? 'debt' : 'repayment'),
                            fromAccountId: const drift.Value(null), // Optionally set from account
                            toAccountId: const drift.Value(null), // Optionally set to account
                            contactId: drift.Value(contactId),
                            description: drift.Value(descController.text.isNotEmpty ? descController.text : (isBorrowing ? 'Borrowed from ${contactName ?? ''}' : 'Repayment to ${contactName ?? ''}')),
                          );
                          final dbAsync = ref.read(databaseProvider);
                          final db = await dbAsync.when(
                            data: (database) => database,
                            loading: () => throw Exception('Database not ready'),
                            error: (error, stack) => throw Exception('Database error: $error'),
                          );
                          await db.into(db.transactions).insert(companion);
                          clearFields();
                          setState(() => isSaving = false);
                          Navigator.pop(context);
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Transaction added successfully!')),
                          );
                        },
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(getContactTransactionsProvider(contactId));
    return Scaffold(
      appBar: AppBar(
        title: Text(contactName ?? 'Contact'),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddTransactionDialog(context, ref),
        icon: const Icon(Icons.add),
        label: const Text('Add Transaction'),
      ),
      body: transactionsAsync.when(
        data: (transactions) {
          final contactTransactions = transactions;
          final total = contactTransactions.fold<double>(0, (sum, t) => sum + (t.type == 'debt' ? t.amount : -t.amount));
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                margin: const EdgeInsets.all(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const CircleAvatar(child: Icon(Icons.person)),
                          const SizedBox(width: 12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(contactName ?? 'Unknown', style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
                              if (contactPhone != null && contactPhone!.isNotEmpty)
                                Text(contactPhone!, style: const TextStyle(color: Colors.grey)),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text('Total Payable: ₹${total.abs().toStringAsFixed(2)}', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: total < 0 ? Colors.green : Colors.red)),
                      Text(total < 0 ? 'They owe you' : 'You owe', style: TextStyle(color: total < 0 ? Colors.green : Colors.red)),
                    ],
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text('Transactions', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ),
              Expanded(
                child: contactTransactions.isEmpty
                    ? const Center(child: Text('No transactions with this contact.'))
                    : ListView.separated(
                        itemCount: contactTransactions.length,
                        separatorBuilder: (_, __) => const Divider(),
                        itemBuilder: (context, i) {
                          final t = contactTransactions[i];
                          final isBorrowed = t.type == 'debt';
                          return ListTile(
                            title: Text(t.description ?? ''),
                            subtitle: Text('₹${t.amount.toStringAsFixed(2)}'),
                            trailing: Text(t.date.toLocal().toString().split(' ')[0]),
                            leading: Icon(isBorrowed ? Icons.arrow_upward : Icons.arrow_downward, color: isBorrowed ? Colors.red : Colors.green),
                          );
                        },
                      ),
              ),
            ],
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, st) => Center(child: Text('Error: $e')),
      ),
    );
  }
} 