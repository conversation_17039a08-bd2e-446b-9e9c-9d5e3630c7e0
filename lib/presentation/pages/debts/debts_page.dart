import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/debt_providers.dart';
import '../../providers/database_provider.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../../domain/repositories/debt_repository.dart';
import '../../providers/expense_providers.dart';
import 'contact_transaction_detail_page.dart';
import '../../widgets/universal_scaffold.dart';

class DebtsPage extends ConsumerWidget {
  const DebtsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final debtsAsync = ref.watch(debtsProvider);
    final borrowedContactsAsync = ref.watch(borrowedContactsSummaryProvider);

    return UniversalScaffold(
      title: 'Debts & Loans',
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Borrowed Contacts Section
            borrowedContactsAsync.when(
              data: (contacts) => contacts.isEmpty
                  ? const SizedBox.shrink()
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Borrowed from Contacts', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: contacts.length,
                          separatorBuilder: (_, __) => const SizedBox(height: 8),
                          itemBuilder: (context, i) {
                            final c = contacts[i];
                            return Card(
                              elevation: 1,
                              child: ListTile(
                                leading: const Icon(Icons.person),
                                title: Text(c.contactName ?? 'Unknown'),
                                subtitle: c.contactPhone != null && c.contactPhone!.isNotEmpty
                                    ? Text(c.contactPhone!)
                                    : null,
                                trailing: Text('₹${c.totalAmount.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ContactTransactionDetailPage(
                                        contactId: c.contactId,
                                        contactName: c.contactName,
                                        contactPhone: c.contactPhone,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 24),
                      ],
                    ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => Text('Error: $e'),
            ),
            // Debts List
            Expanded(
              child: debtsAsync.when(
                data: (debts) => debts.isEmpty 
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.account_balance_wallet_outlined,
                            size: 64,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No debts or loans yet',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add your first debt or loan to start tracking',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: debts.length,
                      itemBuilder: (context, index) {
                        final debt = debts[index];
                        final remaining = debt.totalAmount - debt.paidAmount;
                        final progress = debt.totalAmount > 0 ? debt.paidAmount / debt.totalAmount : 0.0;
                        return Card(
                          elevation: 1,
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          child: ListTile(
                            leading: Icon(Icons.account_balance_wallet, color: Theme.of(context).colorScheme.primary),
                            title: Text(debt.loanName),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Total: ₹${debt.totalAmount.toStringAsFixed(2)}'),
                                Text('Paid: ₹${debt.paidAmount.toStringAsFixed(2)}'),
                                Text('Remaining: ₹${remaining.toStringAsFixed(2)}'),
                                LinearProgressIndicator(
                                  value: progress,
                                  minHeight: 6,
                                  backgroundColor: Colors.grey[200],
                                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                                ),
                              ],
                            ),
                            trailing: Icon(Icons.chevron_right),
                            onTap: () {
                              // TODO: Navigate to debt details
                            },
                          ),
                        );
                      },
                    ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, st) => Center(child: Text('Error: $e')),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          // TODO: Add new debt
        },
        icon: const Icon(Icons.add),
        label: const Text('Add Debt'),
      ),
    );
  }

  Widget _buildDetailCard(BuildContext context, String title, String value, IconData icon) {
    return Card(
      elevation: 0,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, size: 20, color: Theme.of(context).colorScheme.onSurfaceVariant),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Debt debt) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Debt/Loan'),
        content: Text('Are you sure you want to delete "${debt.partyName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await ref.read(deleteDebtUseCaseProvider).call(debt.id);
              ref.refresh(debtsProvider);
              Navigator.pop(context);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showDebtDialog(BuildContext context, WidgetRef ref, {Debt? debt}) {
    final partyController = TextEditingController(text: debt?.partyName ?? '');
    final amountController = TextEditingController(text: debt?.totalAmount.toString() ?? '');
    final interestController = TextEditingController(text: debt?.interestRate.toString() ?? '');
    final paidController = TextEditingController(text: debt?.paidAmount.toString() ?? '');
    final foreclosureController = TextEditingController(text: debt?.foreclosureCharges?.toString() ?? '');
    DateTime startDate = debt?.startDate ?? DateTime.now();
    DateTime dueDate = debt?.dueDate ?? DateTime.now().add(const Duration(days: 30));
    bool isLending = debt?.isLending ?? false;
    String type = debt?.type ?? 'Personal';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(debt == null ? 'Add Debt/Loan' : 'Edit Debt/Loan'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: type,
                decoration: const InputDecoration(
                  labelText: 'Type',
                  border: OutlineInputBorder(),
                ),
                items: ['Personal', 'Business', 'Other']
                    .map((t) => DropdownMenuItem(value: t, child: Text(t)))
                    .toList(),
                onChanged: (val) => type = val ?? type,
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                value: isLending,
                onChanged: (val) => isLending = val,
                title: const Text('Is Lending?'),
                subtitle: Text(isLending ? 'You lent money to someone' : 'You borrowed money from someone'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: partyController,
                decoration: const InputDecoration(
                  labelText: 'Party Name',
                  hintText: 'e.g., John Doe, Bank Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: amountController,
                      decoration: const InputDecoration(
                        labelText: 'Total Amount',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '₹',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: paidController,
                      decoration: const InputDecoration(
                        labelText: 'Paid Amount',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '₹',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: interestController,
                      decoration: const InputDecoration(
                        labelText: 'Interest Rate',
                        hintText: '0.0',
                        border: OutlineInputBorder(),
                        suffixText: '%',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: foreclosureController,
                      decoration: const InputDecoration(
                        labelText: 'Foreclosure Charges',
                        hintText: '0.00',
                        border: OutlineInputBorder(),
                        prefixText: '₹',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: const Text('Start Date'),
                      subtitle: Text(startDate.toLocal().toString().split(' ')[0]),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: startDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) startDate = picked;
                      },
                    ),
                  ),
                  Expanded(
                    child: ListTile(
                      contentPadding: EdgeInsets.zero,
                      title: const Text('Due Date'),
                      subtitle: Text(dueDate.toLocal().toString().split(' ')[0]),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: dueDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) dueDate = picked;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              if (partyController.text.isNotEmpty && amountController.text.isNotEmpty) {
                final companion = DebtsCompanion.insert(
                  type: type,
                  loanName: partyController.text,
                  isLending: isLending,
                  partyName: partyController.text,
                  principalAmount: double.tryParse(amountController.text) ?? 0,
                  currentBalance: double.tryParse(amountController.text) ?? 0,
                  interestRate: double.tryParse(interestController.text) ?? 0,
                  interestType: 'Simple',
                  interestFrequency: 'Monthly',
                  startDate: startDate,
                  dueDate: dueDate,
                  paidAmount: double.tryParse(paidController.text) ?? 0,
                  createdAt: DateTime.now(),
                  lastUpdated: DateTime.now(),
                );
                if (debt == null) {
                  await ref.read(addDebtUseCaseProvider).call(companion);
                } else {
                  await ref.read(updateDebtUseCaseProvider).call(debt.id, companion);
                }
                ref.refresh(debtsProvider);
                Navigator.pop(context);
              }
            },
            child: Text(debt == null ? 'Add' : 'Update'),
          ),
        ],
      ),
    );
  }

  void _showPaymentDialog(BuildContext context, WidgetRef ref, {required int debtId}) {
    final amountController = TextEditingController();
    final noteController = TextEditingController();
    DateTime selectedDate = DateTime.now();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Payment Amount',
                hintText: '0.00',
                border: OutlineInputBorder(),
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'Note (Optional)',
                hintText: 'e.g., Monthly payment',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text('Payment Date'),
              subtitle: Text(selectedDate.toLocal().toString().split(' ')[0]),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: selectedDate,
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );
                if (picked != null) selectedDate = picked;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              if (amountController.text.isNotEmpty) {
                final companion = TransactionsCompanion(
                  amount: drift.Value(double.tryParse(amountController.text) ?? 0),
                  date: drift.Value(DateTime.now()),
                  type: drift.Value('repayment'),
                  fromAccountId: const drift.Value(1), // TODO: Map to correct account
                  toAccountId: const drift.Value(2), // TODO: Map to correct contact account
                  contactId: const drift.Value(null), // TODO: Map to correct contact id
                  note: drift.Value(noteController.text),
                );
                // Instead of addDebtPaymentUseCaseProvider, use debtRepositoryProvider to add a transaction if supported, or leave a TODO
                // await ref.read(addDebtPaymentUseCaseProvider).call(companion);
                // TODO: Implement payment logic using debtRepositoryProvider or Transactions table
                ref.refresh(debtsProvider);
                Navigator.pop(context);
              }
            },
            child: const Text('Add Payment'),
          ),
        ],
      ),
    );
  }
} 