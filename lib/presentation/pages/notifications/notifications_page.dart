import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/alert_providers.dart';
import '../../../core/services/alert_service.dart' show BaseAlert, AlertSeverity;
import '../../widgets/universal_scaffold.dart';

class NotificationsPage extends ConsumerStatefulWidget {
  const NotificationsPage({super.key});

  @override
  ConsumerState<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends ConsumerState<NotificationsPage> {
  @override
  Widget build(BuildContext context) {
    final alertsAsync = ref.watch(allAlertsProvider);
    
    return UniversalScaffold(
      title: 'Alerts & Notifications',
      body: alertsAsync.when(
        data: (alerts) => alerts.isEmpty
            ? _buildEmptyState()
            : _buildAlertsList(alerts),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (_, __) => _buildErrorState(),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80,
            color: Theme.of(context).colorScheme.outline,
          ),
          const SizedBox(height: 16),
          Text(
            'No alerts',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'re all caught up!',
            style: TextStyle(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading alerts',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => ref.refresh(allAlertsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsList(List<BaseAlert> alerts) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: alerts.length,
      itemBuilder: (context, index) {
        final alert = alerts[index];
        return Card(
          elevation: alert.isRead ? 1 : 3,
          margin: const EdgeInsets.only(bottom: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: alert.isRead
                ? BorderSide.none
                : BorderSide(
                    color: _getAlertColor(alert.severity).withOpacity(0.3),
                    width: 1,
                  ),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () => _markAsRead(alert.id),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Alert Icon
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getAlertColor(alert.severity).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getAlertIcon(alert),
                      color: _getAlertColor(alert.severity),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  
                  // Alert Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                alert.title,
                                style: TextStyle(
                                  fontWeight: alert.isRead ? FontWeight.w500 : FontWeight.w600,
                                  fontSize: 16,
                                  color: alert.isRead
                                      ? Theme.of(context).colorScheme.onSurface.withOpacity(0.7)
                                      : Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                            ),
                            if (!alert.isRead)
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _getAlertColor(alert.severity),
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          alert.message,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _getTimeAgo(alert.dueDate),
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.outline,
                                fontSize: 12,
                              ),
                            ),
                            const Spacer(),
                            _buildSeverityChip(alert.severity),
                            const SizedBox(width: 8),
                            PopupMenuButton<String>(
                              icon: Icon(
                                Icons.more_vert,
                                size: 16,
                                color: Theme.of(context).colorScheme.outline,
                              ),
                              onSelected: (value) {
                                if (value == 'mark_read') {
                                  _markAsRead(alert.id);
                                } else if (value == 'delete') {
                                  _deleteAlert(alert.id);
                                }
                              },
                              itemBuilder: (context) => [
                                if (!alert.isRead)
                                  const PopupMenuItem(
                                    value: 'mark_read',
                                    child: Row(
                                      children: [
                                        Icon(Icons.mark_email_read),
                                        SizedBox(width: 8),
                                        Text('Mark as read'),
                                      ],
                                    ),
                                  ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('Delete', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSeverityChip(AlertSeverity severity) {
    Color color;
    String text;
    
    switch (severity) {
      case AlertSeverity.info:
        color = Colors.blue;
        text = 'Info';
        break;
      case AlertSeverity.warning:
        color = Colors.orange;
        text = 'Warning';
        break;
      case AlertSeverity.critical:
        color = Colors.red;
        text = 'Critical';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  IconData _getAlertIcon(BaseAlert alert) {
    // Fallback: infer icon from alert.title or alert.severity
    if (alert.title.toLowerCase().contains('emi')) return Icons.payment;
    if (alert.title.toLowerCase().contains('credit card')) return Icons.credit_card;
    if (alert.title.toLowerCase().contains('debt')) return Icons.account_balance;
    if (alert.title.toLowerCase().contains('budget')) return Icons.account_balance_wallet;
    return Icons.notifications;
  }

  Color _getAlertColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.info:
        return Colors.blue;
      case AlertSeverity.warning:
        return Colors.orange;
      case AlertSeverity.critical:
        return Colors.red;
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  void _markAsRead(String alertId) {
    final alertService = ref.read(alertServiceProvider);
    alertService.markAlertAsRead(alertId);
    ref.refresh(allAlertsProvider);
    ref.refresh(unreadAlertsCountProvider);
  }

  void _markAllAsRead() {
    final alertService = ref.read(alertServiceProvider);
    alertService.markAllAlertsAsRead();
    ref.refresh(allAlertsProvider);
    ref.refresh(unreadAlertsCountProvider);
  }

  void _deleteAlert(String alertId) {
    // In a real app, you would delete from database
    // For now, just refresh the alerts
    ref.refresh(allAlertsProvider);
    ref.refresh(unreadAlertsCountProvider);
  }
} 