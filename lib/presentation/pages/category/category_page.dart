import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/category_providers.dart';
import '../../providers/expense_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import 'package:fl_chart/fl_chart.dart';
import '../../../domain/repositories/transaction_repository.dart';
import '../../widgets/universal_scaffold.dart';

class CategoryPage extends ConsumerStatefulWidget {
  const CategoryPage({super.key});

  @override
  ConsumerState<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends ConsumerState<CategoryPage> {
  String _searchQuery = '';
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Income', 'Expense'];

  @override
  void initState() {
    super.initState();
    _seedCategories();
  }

  Future<void> _seedCategories() async {
    final categories = await ref.read(categoriesProvider.future);
    if (categories.isEmpty) {
      // Seed default categories
      final defaultCategories = [
        CategoriesCompanion.insert(
          name: 'Food & Dining',
          type: drift.Value('Expense'),
          icon: drift.Value('restaurant'),
          color: drift.Value('#FF6B6B'),
        ),
        CategoriesCompanion.insert(
          name: 'Transportation',
          type: drift.Value('Expense'),
          icon: drift.Value('directions_car'),
          color: drift.Value('#4ECDC4'),
        ),
        CategoriesCompanion.insert(
          name: 'Shopping',
          type: drift.Value('Expense'),
          icon: drift.Value('shopping_bag'),
          color: drift.Value('#45B7D1'),
        ),
        CategoriesCompanion.insert(
          name: 'Entertainment',
          type: drift.Value('Expense'),
          icon: drift.Value('movie'),
          color: drift.Value('#96CEB4'),
        ),
        CategoriesCompanion.insert(
          name: 'Healthcare',
          type: drift.Value('Expense'),
          icon: drift.Value('local_hospital'),
          color: drift.Value('#FFEAA7'),
        ),
        CategoriesCompanion.insert(
          name: 'Salary',
          type: drift.Value('Income'),
          icon: drift.Value('account_balance_wallet'),
          color: drift.Value('#DDA0DD'),
        ),
        CategoriesCompanion.insert(
          name: 'Freelance',
          type: drift.Value('Income'),
          icon: drift.Value('work'),
          color: drift.Value('#98D8C8'),
        ),
        CategoriesCompanion.insert(
          name: 'Investment',
          type: drift.Value('Income'),
          icon: drift.Value('trending_up'),
          color: drift.Value('#F7DC6F'),
        ),
      ];

      for (final category in defaultCategories) {
        await ref.read(addCategoryUseCaseProvider).call(category);
      }
      ref.refresh(categoriesProvider);
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);
    final expensesAsync = ref.watch(expensesProvider);

    return UniversalScaffold(
      title: 'Categories',
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'Search categories...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () => setState(() => _searchQuery = ''),
                      )
                    : null,
              ),
            ),
          ),
          if (_selectedFilter != 'All')
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Chip(
                    label: Text(_selectedFilter),
                    onDeleted: () => setState(() => _selectedFilter = 'All'),
                    deleteIcon: const Icon(Icons.close, size: 18),
                  ),
                ],
              ),
            ),
          Expanded(
            child: categoriesAsync.when(
              data: (categories) {
                final filteredCategories = categories.where((category) {
                  final matchesSearch = category.name.toLowerCase().contains(_searchQuery.toLowerCase());
                  final matchesFilter = _selectedFilter == 'All' || category.type == _selectedFilter;
                  return matchesSearch && matchesFilter;
                }).toList();

                if (filteredCategories.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.category_outlined, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No categories found', style: TextStyle(fontSize: 18, color: Colors.grey)),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredCategories.length,
                  itemBuilder: (context, index) {
                    final category = filteredCategories[index];
                    return _buildCategoryCard(category, expensesAsync);
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => Center(child: Text('Error: $e')),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildCategoryCard(Category category, AsyncValue<List<TransactionWithCategory>> expensesAsync) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showCategoryDetails(category, expensesAsync),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _parseColor(category.color),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getIconData(category.icon),
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          category.type,
                          style: TextStyle(
                            fontSize: 14,
                            color: category.type == 'Income' ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showCategoryDialog(category: category);
                          break;
                        case 'delete':
                          _deleteCategory(category);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 20),
                            SizedBox(width: 8),
                            Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              expensesAsync.when(
                data: (expenses) {
                  final categoryExpenses = expenses.where((e) => e.transaction.categoryId == category.id).toList();
                  final totalAmount = categoryExpenses.fold<double>(0, (sum, e) => sum + e.transaction.amount);
                  final expenseCount = categoryExpenses.length;

                  return Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '₹${totalAmount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '$expenseCount transactions',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (categoryExpenses.isNotEmpty)
                        SizedBox(
                          width: 60,
                          height: 60,
                          child: PieChart(
                            PieChartData(
                              sections: [
                                PieChartSectionData(
                                  value: totalAmount,
                                  color: _parseColor(category.color),
                                  radius: 25,
                                  title: '',
                                ),
                              ],
                              centerSpaceRadius: 20,
                            ),
                          ),
                        ),
                    ],
                  );
                },
                loading: () => const SizedBox(height: 40, child: Center(child: CircularProgressIndicator())),
                error: (e, st) => Text('Error loading expenses: $e'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCategoryDetails(Category category, AsyncValue<List<TransactionWithCategory>> expensesAsync) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: _parseColor(category.color),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getIconData(category.icon),
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            category.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            category.type,
                            style: TextStyle(
                              fontSize: 16,
                              color: category.type == 'Income' ? Colors.green : Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: expensesAsync.when(
                  data: (expenses) {
                    final categoryExpenses = expenses.where((e) => e.transaction.categoryId == category.id).toList();
                    if (categoryExpenses.isEmpty) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                            SizedBox(height: 16),
                            Text('No transactions in this category', style: TextStyle(fontSize: 16, color: Colors.grey)),
                          ],
                        ),
                      );
                    }

                    return ListView.builder(
                      controller: scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: categoryExpenses.length,
                      itemBuilder: (context, index) {
                        final expense = categoryExpenses[index];
                        return Card(
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: _parseColor(category.color).withOpacity(0.2),
                              child: Icon(
                                _getIconData(category.icon),
                                color: _parseColor(category.color),
                              ),
                            ),
                            title: Text(expense.transaction.description ?? ''),
                            subtitle: Text(expense.transaction.date.toString().split(' ')[0]),
                            trailing: Text(
                              '₹${expense.transaction.amount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  },
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (e, st) => Center(child: Text('Error: $e')),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Categories'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filterOptions.map((filter) => RadioListTile<String>(
            title: Text(filter),
            value: filter,
            groupValue: _selectedFilter,
            onChanged: (value) {
              setState(() => _selectedFilter = value!);
              Navigator.pop(context);
            },
          )).toList(),
        ),
      ),
    );
  }

  void _showCategoryDialog({Category? category}) {
    final nameController = TextEditingController(text: category?.name ?? '');
    String selectedType = category?.type ?? 'Expense';
    String selectedIcon = category?.icon ?? 'restaurant';
    String selectedColor = category?.color ?? '#FF6B6B';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(category == null ? 'Add Category' : 'Edit Category'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Category Name',
                    hintText: 'Enter category name',
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedType,
                  decoration: const InputDecoration(labelText: 'Type'),
                  items: const [
                    DropdownMenuItem(value: 'Income', child: Text('Income')),
                    DropdownMenuItem(value: 'Expense', child: Text('Expense')),
                  ],
                  onChanged: (value) => setState(() => selectedType = value!),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedIcon,
                  decoration: const InputDecoration(labelText: 'Icon'),
                  items: const [
                    DropdownMenuItem(value: 'restaurant', child: Text('🍽️ Food')),
                    DropdownMenuItem(value: 'directions_car', child: Text('🚗 Transport')),
                    DropdownMenuItem(value: 'shopping_bag', child: Text('🛍️ Shopping')),
                    DropdownMenuItem(value: 'movie', child: Text('🎬 Entertainment')),
                    DropdownMenuItem(value: 'local_hospital', child: Text('🏥 Healthcare')),
                    DropdownMenuItem(value: 'account_balance_wallet', child: Text('💰 Salary')),
                    DropdownMenuItem(value: 'work', child: Text('💼 Work')),
                    DropdownMenuItem(value: 'trending_up', child: Text('📈 Investment')),
                  ],
                  onChanged: (value) => setState(() => selectedIcon = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.isNotEmpty) {
                  final companion = CategoriesCompanion(
                    name: drift.Value(nameController.text),
                    type: drift.Value(selectedType),
                    icon: drift.Value(selectedIcon),
                    color: drift.Value(selectedColor),
                  );
                  
                  if (category == null) {
                    await ref.read(addCategoryUseCaseProvider).call(companion);
                  } else {
                    await ref.read(updateCategoryUseCaseProvider).call(category.id, companion);
                  }
                  
                  ref.refresh(categoriesProvider);
                  Navigator.pop(context);
                }
              },
              child: Text(category == null ? 'Add' : 'Update'),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteCategory(Category category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Category'),
        content: Text('Are you sure you want to delete "${category.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteCategoryUseCaseProvider).call(category.id);
              ref.refresh(categoriesProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.grey;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'directions_car': return Icons.directions_car;
      case 'shopping_bag': return Icons.shopping_bag;
      case 'movie': return Icons.movie;
      case 'local_hospital': return Icons.local_hospital;
      case 'account_balance_wallet': return Icons.account_balance_wallet;
      case 'work': return Icons.work;
      case 'trending_up': return Icons.trending_up;
      default: return Icons.category;
    }
  }
} 