import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/investment_providers.dart';
import '../../providers/stock_api_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../../core/services/stock_api_service.dart' show StockSymbol;
import '../../widgets/universal_scaffold.dart';

class InvestmentsPage extends ConsumerWidget {
  const InvestmentsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final investmentsAsync = ref.watch(investmentsProvider);
    
    return UniversalScaffold(
      title: 'Investments',
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: investmentsAsync.when(
          data: (investments) => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: investments.isEmpty 
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.analytics_outlined,
                            size: 64,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No investments yet',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Add your first investment to start tracking',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: investments.length,
                      itemBuilder: (context, index) {
                        final inv = investments[index];
                        return Consumer(
                          builder: (context, ref, child) {
                            final stockPriceAsync = ref.watch(stockPriceProvider(inv.ticker));
                            
                            return stockPriceAsync.when(
                              data: (stockPrice) {
                                final livePrice = stockPrice?.price ?? inv.buyPrice * 1.1;
                                final gain = (livePrice - inv.buyPrice) * inv.quantity;
                                final gainPercentage = ((livePrice - inv.buyPrice) / inv.buyPrice) * 100;
                                final isPositive = gain >= 0;
                                
                                return Card(
                                  elevation: 0,
                                  margin: const EdgeInsets.only(bottom: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                    side: BorderSide(
                                      color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 48,
                                          height: 48,
                                          decoration: BoxDecoration(
                                            color: isPositive 
                                              ? Colors.green.withOpacity(0.1)
                                              : Colors.red.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Icon(
                                            isPositive ? Icons.trending_up : Icons.trending_down,
                                            color: isPositive ? Colors.green : Colors.red,
                                          ),
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                inv.ticker,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                '${inv.quantity} units • ₹${inv.buyPrice.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                                  fontSize: 14,
                                                ),
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                'Live: ₹${livePrice.toStringAsFixed(2)}',
                                                style: TextStyle(
                                                  color: isPositive ? Colors.green : Colors.red,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                'Gain: ${gain >= 0 ? '+' : ''}${gain.toStringAsFixed(2)} (₹${gainPercentage.toStringAsFixed(2)}%)',
                                                style: TextStyle(
                                                  color: isPositive ? Colors.green : Colors.red,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                              loading: () => const LinearProgressIndicator(),
                              error: (e, st) => Text('Error: $e'),
                            );
                          },
                        );
                      },
                    ),
              ),
            ],
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (e, st) => Center(child: Text('Error: $e')),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showInvestmentDialog(context, ref),
        icon: const Icon(Icons.add),
        label: const Text('Add Investment'),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Investment investment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Investment'),
        content: Text('Are you sure you want to delete "${investment.ticker}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await ref.read(deleteInvestmentUseCaseProvider).call(investment.id);
              ref.refresh(investmentsProvider);
              Navigator.pop(context);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showInvestmentDialog(BuildContext context, WidgetRef ref, {Investment? investment}) {
    final tickerController = TextEditingController(text: investment?.ticker ?? '');
    final typeController = TextEditingController(text: investment?.type ?? '');
    final quantityController = TextEditingController(text: investment?.quantity.toString() ?? '');
    final buyPriceController = TextEditingController(text: investment?.buyPrice.toString() ?? '');
    DateTime buyDate = investment?.buyDate ?? DateTime.now();
    StockSymbol? selectedSymbol;
    
    showDialog(
      context: context,
      builder: (context) => Consumer(
        builder: (context, ref, child) {
          final searchQuery = tickerController.text;
          final symbolsAsync = searchQuery.isNotEmpty 
              ? ref.watch(searchSymbolsProvider(searchQuery))
              : ref.watch(getPopularSymbolsProvider);
          
          return AlertDialog(
            title: Text(investment == null ? 'Add Investment' : 'Edit Investment'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Symbol Search with Autocomplete
                  Autocomplete<StockSymbol>(
                    fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
                      textEditingController.text = tickerController.text;
                      return TextField(
                        controller: textEditingController,
                        focusNode: focusNode,
                        decoration: InputDecoration(
                          labelText: 'Ticker Symbol',
                          hintText: 'Search for stock symbol...',
                          border: const OutlineInputBorder(),
                          suffixIcon: const Icon(Icons.search),
                        ),
                        onChanged: (value) {
                          tickerController.text = value;
                        },
                      );
                    },
                    optionsBuilder: (TextEditingValue textEditingValue) {
                      if (textEditingValue.text.isEmpty) {
                        return symbolsAsync.when(
                          data: (symbols) => symbols,
                          loading: () => <StockSymbol>[],
                          error: (_, __) => <StockSymbol>[],
                        );
                      }
                      
                      return symbolsAsync.when(
                        data: (symbols) => symbols,
                        loading: () => <StockSymbol>[],
                        error: (_, __) => <StockSymbol>[],
                      );
                    },
                    displayStringForOption: (StockSymbol option) => '${option.symbol} - ${option.name}',
                    onSelected: (StockSymbol selection) {
                      selectedSymbol = selection;
                      tickerController.text = selection.symbol;
                      typeController.text = selection.type;
                    },
                    optionsViewBuilder: (context, onSelected, options) {
                      return Material(
                        elevation: 4.0,
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            itemCount: options.length,
                            itemBuilder: (BuildContext context, int index) {
                              final option = options.elementAt(index);
                              return ListTile(
                                title: Text(option.symbol),
                                subtitle: Text(option.name),
                                trailing: Text(option.exchange, style: const TextStyle(fontSize: 12)),
                                onTap: () => onSelected(option),
                              );
                            },
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Investment Type
                  DropdownButtonFormField<String>(
                    value: typeController.text.isEmpty ? 'Stock' : typeController.text,
                    decoration: const InputDecoration(
                      labelText: 'Investment Type',
                      border: OutlineInputBorder(),
                    ),
                    items: ['Stock', 'Mutual Fund', 'ETF', 'Bond', 'Crypto', 'Other']
                        .map((t) => DropdownMenuItem(value: t, child: Text(t)))
                        .toList(),
                    onChanged: (value) => typeController.text = value ?? 'Stock',
                  ),
                  const SizedBox(height: 16),
                  
                  // Quantity and Buy Price
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: quantityController,
                          decoration: const InputDecoration(
                            labelText: 'Quantity',
                            hintText: '0',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: buyPriceController,
                          decoration: const InputDecoration(
                            labelText: 'Buy Price',
                            hintText: '0.00',
                            border: OutlineInputBorder(),
                            prefixText: '₹',
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Buy Date
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    title: const Text('Buy Date'),
                    subtitle: Text(buyDate.toLocal().toString().split(' ')[0]),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: buyDate,
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) buyDate = picked;
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              FilledButton(
                onPressed: () async {
                  if (tickerController.text.isNotEmpty && typeController.text.isNotEmpty) {
                    final companion = InvestmentsCompanion(
                      ticker: drift.Value(tickerController.text),
                      type: drift.Value(typeController.text),
                      quantity: drift.Value(double.tryParse(quantityController.text) ?? 0),
                      buyPrice: drift.Value(double.tryParse(buyPriceController.text) ?? 0),
                      buyDate: drift.Value(buyDate),
                    );
                    if (investment == null) {
                      await ref.read(addInvestmentUseCaseProvider).call(companion);
                    } else {
                      await ref.read(updateInvestmentUseCaseProvider).call(investment.id, companion);
                    }
                    ref.refresh(investmentsProvider);
                    Navigator.pop(context);
                  }
                },
                child: Text(investment == null ? 'Add' : 'Update'),
              ),
            ],
          );
        },
      ),
    );
  }
} 