import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/firebase_auth_providers.dart';
import '../../providers/firebase_sync_providers.dart';
import '../../providers/biometric_providers.dart';
import '../../providers/database_provider.dart';
import '../../providers/currency_providers.dart';
import '../../providers/theme_providers.dart';
import '../../providers/app_providers.dart';
import '../../../core/services/biometric_service.dart';
import '../../../core/services/currency_service.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/database/app_database.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../providers/auth_providers.dart';

class SettingsPage extends ConsumerWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userAsync = ref.watch(currentUserProvider);
    final backupAsync = ref.watch(firebaseBackupProvider);
    final restoreAsync = ref.watch(firebaseRestoreProvider);
    final themeMode = ref.watch(themeModeProvider);
    final authMethodAsync = ref.watch(authenticationMethodProvider);
    final biometricAvailableAsync = ref.watch(biometricAvailableProvider);
    final currentCurrencyAsync = ref.watch(currentCurrencyProvider);
    final allCurrenciesAsync = ref.watch(allCurrenciesProvider);
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      color: colorScheme.surface,
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        children: [
          _sectionHeader(context, icon: Icons.person, title: 'Account'),
          _buildProfileSection(context, ref, AsyncValue.data(userAsync)),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.currency_exchange, title: 'Currency'),
          _buildCurrencySection(context, ref, currentCurrencyAsync, allCurrenciesAsync),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.palette, title: 'Appearance'),
          _buildAppearanceSection(context, ref),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.security, title: 'Security'),
          _buildSecuritySection(context, ref, authMethodAsync, biometricAvailableAsync),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.cloud, title: 'Data & Backup'),
          _buildDataBackupSection(context, ref, backupAsync, restoreAsync),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.warning_amber_rounded, title: 'Danger Zone', color: Colors.redAccent),
          _buildDangerZoneSection(context, ref),
          const SizedBox(height: 32),
          _sectionHeader(context, icon: Icons.bug_report, title: 'Debug', color: Colors.orange),
          _buildDebugSection(context, ref),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _sectionHeader(BuildContext context, {required IconData icon, required String title, Color? color}) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(left: 4, bottom: 12, top: 8),
      child: Row(
        children: [
          Icon(icon, color: color ?? theme.colorScheme.primary, size: 20),
          const SizedBox(width: 12),
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: color ?? theme.colorScheme.onSurface,
              letterSpacing: 0.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, WidgetRef ref, AsyncValue<dynamic> userAsync) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: colorScheme.primary.withOpacity(0.1),
                child: Icon(Icons.person, color: colorScheme.primary, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Account',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    userAsync.when(
                      data: (user) => Text(
                        user != null ? (user.email ?? user.id) : 'Not signed in',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      loading: () => const Text('Loading...'),
                      error: (_, __) => const Text('Error loading account'),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          userAsync.when(
            data: (user) => user == null
                ? SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _showSignInDialog(context, ref),
                      icon: const Icon(Icons.login, size: 18),
                      label: const Text('Sign In'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        backgroundColor: colorScheme.primary,
                        foregroundColor: colorScheme.onPrimary,
                      ),
                    ),
                  )
                : SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () async {
                        await ref.read(signOutProvider.future);
                      },
                      icon: const Icon(Icons.logout, size: 18),
                      label: const Text('Sign Out'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        side: BorderSide(color: colorScheme.outline),
                      ),
                    ),
                  ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => const Text('Error'),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencySection(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<CurrencySetting?> currentCurrencyAsync,
    AsyncValue<List<CurrencySetting>> allCurrenciesAsync,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.currency_exchange,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Currency',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          currentCurrencyAsync.when(
            data: (currentCurrency) => currentCurrency != null
                ? Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          currentCurrency.currencySymbol,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currentCurrency.currencyName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                currentCurrency.currencyCode,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.check_circle,
                          color: colorScheme.primary,
                          size: 20,
                        ),
                      ],
                    ),
                  )
                : const Text('No currency selected'),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => const Text('Error loading currency'),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showCurrencySelectionDialog(context, ref, allCurrenciesAsync),
              icon: const Icon(Icons.change_circle, size: 18),
              label: const Text('Change Currency'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppearanceSection(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeModeProvider);
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.palette,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Appearance',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<ThemeMode>(
                value: themeMode,
                onChanged: (mode) {
                  if (mode != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(mode);
                  }
                },
                items: const [
                  DropdownMenuItem(
                    value: ThemeMode.system,
                    child: Text('System Default'),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.light,
                    child: Text('Light Theme'),
                  ),
                  DropdownMenuItem(
                    value: ThemeMode.dark,
                    child: Text('Dark Theme'),
                  ),
                ],
                isExpanded: true,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySection(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<String> authMethodAsync,
    AsyncValue<bool> biometricAvailableAsync,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Security',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          authMethodAsync.when(
            data: (authMethod) => Column(
              children: [
                // Security Status
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: authMethod == 'none' 
                        ? Colors.orange.withOpacity(0.1)
                        : Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: authMethod == 'none' 
                          ? Colors.orange.withOpacity(0.3)
                          : Colors.green.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        authMethod == 'none' ? Icons.warning : Icons.check_circle,
                        color: authMethod == 'none' ? Colors.orange : Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              authMethod == 'none' ? 'No Security' : 'Security Active',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            Text(
                              authMethod == 'none' 
                                  ? 'Your app is not protected'
                                  : authMethod == 'pin' 
                                      ? 'Protected with PIN'
                                      : 'Protected with biometric',
                              style: TextStyle(
                                color: colorScheme.onSurfaceVariant,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                
                // PIN Security
                _buildSecurityTile(
                  context: context,
                  ref: ref,
                  icon: Icons.pin,
                  title: 'PIN Security',
                  subtitle: authMethod == 'pin' || authMethod == 'biometric' 
                      ? 'PIN is configured' 
                      : 'No PIN set',
                  isEnabled: authMethod == 'pin' || authMethod == 'biometric',
                  onTap: () => _handlePinSecurity(context, ref, authMethod),
                ),
                const SizedBox(height: 8),
                
                // Biometric Security
                biometricAvailableAsync.when(
                  data: (isAvailable) => isAvailable
                      ? _buildSecurityTile(
                          context: context,
                          ref: ref,
                          icon: Icons.fingerprint,
                          title: 'Biometric Authentication',
                          subtitle: authMethod == 'biometric' 
                              ? 'Biometric enabled' 
                              : 'Biometric disabled',
                          isEnabled: authMethod == 'biometric',
                          onTap: () => _toggleBiometric(context, ref, authMethod != 'biometric'),
                        )
                      : _buildSecurityTile(
                          context: context,
                          ref: ref,
                          icon: Icons.fingerprint,
                          title: 'Biometric Authentication',
                          subtitle: 'Not available on this device',
                          isEnabled: false,
                          onTap: null,
                        ),
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (_, __) => _buildSecurityTile(
                    context: context,
                    ref: ref,
                    icon: Icons.fingerprint,
                    title: 'Biometric Authentication',
                    subtitle: 'Error checking availability',
                    isEnabled: false,
                    onTap: null,
                  ),
                ),
              ],
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => const Text('Error loading security settings'),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTile({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isEnabled,
    required VoidCallback? onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Icon(
          icon,
          color: isEnabled 
              ? colorScheme.primary 
              : colorScheme.onSurfaceVariant,
          size: 20,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: isEnabled ? colorScheme.onSurface : colorScheme.onSurfaceVariant,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: isEnabled
            ? Icon(
                Icons.check_circle,
                color: colorScheme.primary,
                size: 20,
              )
            : Icon(
                Icons.arrow_forward_ios,
                color: colorScheme.onSurfaceVariant,
                size: 16,
              ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildDataBackupSection(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<void> backupAsync,
    AsyncValue<void> restoreAsync,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.backup,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Data & Backup',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildBackupTile(
            context: context,
            ref: ref,
            icon: Icons.cloud_upload,
            title: 'Backup Data',
            subtitle: 'Upload your data to the cloud',
            isLoading: backupAsync.isLoading,
            onTap: () => ref.refresh(firebaseBackupProvider),
          ),
          const SizedBox(height: 8),
          _buildBackupTile(
            context: context,
            ref: ref,
            icon: Icons.cloud_download,
            title: 'Restore Data',
            subtitle: 'Download data from the cloud',
            isLoading: restoreAsync.isLoading,
            onTap: () => ref.refresh(firebaseRestoreProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupTile({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isLoading,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
                ),
              )
            : Icon(
                icon,
                color: colorScheme.primary,
                size: 20,
              ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: isLoading
            ? null
            : Icon(
                Icons.arrow_forward_ios,
                color: colorScheme.onSurfaceVariant,
                size: 16,
              ),
        onTap: isLoading ? null : onTap,
      ),
    );
  }

  Widget _buildDangerZoneSection(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withOpacity(0.2)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: colorScheme.error,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Danger Zone',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.error,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDangerTile(
            context: context,
            ref: ref,
            icon: Icons.delete_forever,
            title: 'Clear All Data',
            subtitle: 'Delete all your financial data permanently',
            color: colorScheme.error,
            onTap: () => _showClearDataDialog(context, ref),
          ),
          const SizedBox(height: 8),
          _buildDangerTile(
            context: context,
            ref: ref,
            icon: Icons.person_remove,
            title: 'Delete Account',
            subtitle: 'Permanently delete your account and all data',
            color: colorScheme.error,
            onTap: () => _showDeleteAccountDialog(context, ref),
          ),
          const SizedBox(height: 8),
          _buildDangerTile(
            context: context,
            ref: ref,
            icon: Icons.refresh,
            title: 'Reset Database',
            subtitle: 'Reset all data and settings to default',
            color: colorScheme.error,
            onTap: () => _showResetDatabaseDialog(context, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildDangerTile({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Icon(
          icon,
          color: color,
          size: 20,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: colorScheme.onSurfaceVariant,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  void _testAlerts(BuildContext context, WidgetRef ref) async {
    final notificationService = NotificationService();
    
    // Show test notifications
    await notificationService.showImmediateNotification(
      title: 'Budget Alert Test',
      body: 'You\'ve spent 85% of your food budget this month',
      priority: 2,
    );
    
    await Future.delayed(const Duration(seconds: 2));
    
    await notificationService.showImmediateNotification(
      title: 'EMI Due Test',
      body: 'EMI payment of ₹5,000 is due in 2 days',
      priority: 1,
    );
    
    await Future.delayed(const Duration(seconds: 2));
    
    await notificationService.showImmediateNotification(
      title: 'Credit Card Due Test',
      body: 'Credit card payment is due in 3 days',
      priority: 1,
    );
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Test notifications sent!'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handlePinSecurity(BuildContext context, WidgetRef ref, String authMethod) {
    final hasPin = authMethod == 'pin' || authMethod == 'biometric';
    
    if (hasPin) {
      showModalBottomSheet(
        context: context,
        builder: (context) => Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('Change PIN'),
                onTap: () {
                  Navigator.pop(context);
                  _changePin(context, ref);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Remove PIN', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _removePin(context, ref);
                },
              ),
            ],
          ),
        ),
      );
    } else {
      _setupPin(context, ref);
    }
  }

  void _setupPin(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => _PinSetupDialog(
        title: 'Set PIN',
        onConfirm: (pin) async {
          final service = ref.read(biometricServiceProvider);
          final success = await service.setPin(pin);
          if (success && context.mounted) {
            Navigator.pop(context);
            ref.invalidate(authenticationMethodProvider);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('PIN set successfully')),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to set PIN')),
            );
          }
        },
      ),
    );
  }

  void _changePin(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => _PinChangeDialog(
        onConfirm: (currentPin, newPin) async {
          final service = ref.read(biometricServiceProvider);
          final success = await service.changePin(currentPin, newPin);
          if (success && context.mounted) {
            Navigator.pop(context);
            ref.invalidate(authenticationMethodProvider);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('PIN changed successfully')),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to change PIN')),
            );
          }
        },
      ),
    );
  }

  void _removePin(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => _PinRemoveDialog(
        onConfirm: (currentPin) async {
          final service = ref.read(biometricServiceProvider);
          final success = await service.removePin(currentPin);
          if (success && context.mounted) {
            Navigator.pop(context);
            ref.invalidate(authenticationMethodProvider);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('PIN removed successfully')),
            );
          } else if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Failed to remove PIN')),
            );
          }
        },
      ),
    );
  }

  void _toggleBiometric(BuildContext context, WidgetRef ref, bool enable) async {
    final service = ref.read(biometricServiceProvider);
    
    if (enable) {
      // Check if PIN is set first
      final hasPin = await service.isPinSet();
      if (!hasPin) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Please set a PIN first')),
          );
        }
        return;
      }

      final success = await service.enableBiometric();
      if (success && context.mounted) {
        ref.invalidate(authenticationMethodProvider);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Biometric authentication enabled')),
        );
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to enable biometric authentication')),
        );
      }
    } else {
      final success = await service.disableBiometric();
      if (success && context.mounted) {
        ref.invalidate(authenticationMethodProvider);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Biometric authentication disabled')),
        );
      } else if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to disable biometric authentication')),
        );
      }
    }
  }

  void _showSignInDialog(BuildContext context, WidgetRef ref) {
    final emailController = TextEditingController();
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign In'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: 'Password',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final success = await ref.read(signInProvider({'email': emailController.text, 'password': passwordController.text}).future);
              if (success && context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Signed in successfully')),
                );
              } else if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Failed to sign in')),
                );
              }
            },
            child: const Text('Sign In'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your financial data including expenses, incomes, budgets, and settings. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final databaseAsync = ref.read(databaseProvider);
              final database = await databaseAsync.when(
                data: (db) => db,
                loading: () => throw Exception('Database not ready'),
                error: (error, stack) => throw Exception('Database error: $error'),
              );
              await database.deleteAllData();
              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('All data cleared')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('Clear Data'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context, WidgetRef ref) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'This will permanently delete your account and all associated data. This action cannot be undone.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: 'Enter your password to confirm',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Implement account deletion logic
              if (context.mounted) {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Account deletion feature coming soon')),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  void _showResetDatabaseDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Database'),
        content: const Text(
          'This will permanently delete all your financial data including expenses, incomes, budgets, and settings. This action cannot be undone.\n\nAfter reset, the app will be reinitialized with default categories and cash account.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                // Reset and reinitialize database
                await ref.read(resetDatabaseProvider.future);
                
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Database reset successfully. App has been reinitialized.'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error resetting database: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset Database'),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugSection(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: Colors.orange.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withOpacity(0.2)),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.bug_report,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 12),
              Text(
                'Debug Tools',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDebugTile(
            context: context,
            ref: ref,
            icon: Icons.refresh,
            title: 'Reinitialize Database',
            subtitle: 'Recreate cash account and categories',
            color: Colors.orange,
            onTap: () => _reinitializeDatabase(context, ref),
          ),
          const SizedBox(height: 8),
          _buildDebugTile(
            context: context,
            ref: ref,
            icon: Icons.notifications,
            title: 'Test Notifications',
            subtitle: 'Send test notifications',
            color: Colors.orange,
            onTap: () => _testAlerts(context, ref),
          ),
        ],
      ),
    );
  }

  Widget _buildDebugTile({
    required BuildContext context,
    required WidgetRef ref,
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Icon(
          icon,
          color: color,
          size: 20,
        ),
        title: Text(
          title,
          style: TextStyle(
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 12,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: colorScheme.onSurfaceVariant,
          size: 16,
        ),
        onTap: onTap,
      ),
    );
  }

  void _reinitializeDatabase(BuildContext context, WidgetRef ref) async {
    try {
      await ref.read(initializeDatabaseProvider.future);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Database reinitialized successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error reinitializing database: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCurrencySelectionDialog(
    BuildContext context,
    WidgetRef ref,
    AsyncValue<List<CurrencySetting>> allCurrenciesAsync,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: allCurrenciesAsync.when(
            data: (currencies) => ListView.builder(
              itemCount: currencies.length,
              itemBuilder: (context, index) {
                final currency = currencies[index];
                return ListTile(
                  leading: Text(
                    currency.currencySymbol,
                    style: const TextStyle(fontSize: 20),
                  ),
                  title: Text(currency.currencyName),
                  subtitle: Text(currency.currencyCode),
                  trailing: currency.isDefault
                      ? Icon(Icons.check_circle, color: Theme.of(context).colorScheme.primary)
                      : null,
                  onTap: () async {
                    final service = ref.read(currencyServiceProvider);
                    await service.setDefaultCurrency(currency.currencyCode);
                    ref.invalidate(currentCurrencyProvider);
                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Currency changed to ${currency.currencyName}')),
                      );
                    }
                  },
                );
              },
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (_, __) => const Center(child: Text('Error loading currencies')),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

// PIN Setup Dialog
class _PinSetupDialog extends StatefulWidget {
  final String title;
  final Function(String) onConfirm;

  const _PinSetupDialog({
    required this.title,
    required this.onConfirm,
  });

  @override
  State<_PinSetupDialog> createState() => _PinSetupDialogState();
}

class _PinSetupDialogState extends State<_PinSetupDialog> {
  final TextEditingController _pinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();
  bool _obscurePin = true;
  bool _obscureConfirmPin = true;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _pinController,
            decoration: InputDecoration(
              labelText: 'Enter PIN',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(_obscurePin ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscurePin = !_obscurePin),
              ),
            ),
            obscureText: _obscurePin,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _confirmPinController,
            decoration: InputDecoration(
              labelText: 'Confirm PIN',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: Icon(_obscureConfirmPin ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureConfirmPin = !_obscureConfirmPin),
              ),
            ),
            obscureText: _obscureConfirmPin,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_pinController.text == _confirmPinController.text &&
                _pinController.text.length >= 4) {
              widget.onConfirm(_pinController.text);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('PINs do not match or are too short')),
              );
            }
          },
          child: const Text('Set PIN'),
        ),
      ],
    );
  }
}

// PIN Change Dialog
class _PinChangeDialog extends StatefulWidget {
  final Function(String, String) onConfirm;

  const _PinChangeDialog({required this.onConfirm});

  @override
  State<_PinChangeDialog> createState() => _PinChangeDialogState();
}

class _PinChangeDialogState extends State<_PinChangeDialog> {
  final TextEditingController _currentPinController = TextEditingController();
  final TextEditingController _newPinController = TextEditingController();
  final TextEditingController _confirmPinController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return AlertDialog(
      title: const Text('Change PIN'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _currentPinController,
            decoration: InputDecoration(
              labelText: 'Current PIN',
              border: const OutlineInputBorder(),
            ),
            obscureText: true,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _newPinController,
            decoration: InputDecoration(
              labelText: 'New PIN',
              border: const OutlineInputBorder(),
            ),
            obscureText: true,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _confirmPinController,
            decoration: InputDecoration(
              labelText: 'Confirm New PIN',
              border: const OutlineInputBorder(),
            ),
            obscureText: true,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_newPinController.text == _confirmPinController.text &&
                _newPinController.text.length >= 4) {
              widget.onConfirm(_currentPinController.text, _newPinController.text);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('New PINs do not match or are too short')),
              );
            }
          },
          child: const Text('Change PIN'),
        ),
      ],
    );
  }
}

// PIN Remove Dialog
class _PinRemoveDialog extends StatefulWidget {
  final Function(String) onConfirm;

  const _PinRemoveDialog({required this.onConfirm});

  @override
  State<_PinRemoveDialog> createState() => _PinRemoveDialogState();
}

class _PinRemoveDialogState extends State<_PinRemoveDialog> {
  final TextEditingController _pinController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return AlertDialog(
      title: const Text('Remove PIN'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Enter your current PIN to remove it:'),
          const SizedBox(height: 16),
          TextField(
            controller: _pinController,
            decoration: InputDecoration(
              labelText: 'Current PIN',
              border: const OutlineInputBorder(),
            ),
            obscureText: true,
            keyboardType: TextInputType.number,
            maxLength: 6,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_pinController.text.isNotEmpty) {
              widget.onConfirm(_pinController.text);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Please enter your PIN')),
              );
            }
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          child: const Text('Remove PIN'),
        ),
      ],
    );
  }
} 