import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/allocation_providers.dart';
import '../../providers/category_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../widgets/universal_scaffold.dart';

class AllocationPage extends ConsumerWidget {
  const AllocationPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final allocationsAsync = ref.watch(allocationsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return UniversalScaffold(
      title: 'Budget Allocations',
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: allocationsAsync.when(
          data: (allocations) => Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: allocations.isEmpty 
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.account_balance_wallet_outlined,
                            size: 64,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No budget allocations yet',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Create your first budget allocation to start tracking',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: allocations.length,
                      itemBuilder: (context, index) {
                        final allocation = allocations[index];
                        final progress = allocation.allocatedAmount > 0 
                          ? allocation.actualSpent / allocation.allocatedAmount 
                          : 0.0;
                        final progressPercentage = (progress * 100).clamp(0.0, 100.0);
                        final remaining = allocation.allocatedAmount - allocation.actualSpent;
                        final isOverBudget = allocation.actualSpent > allocation.allocatedAmount;
                        return Card(
                          elevation: 0,
                          margin: const EdgeInsets.only(bottom: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Container(
                                      width: 48,
                                      height: 48,
                                      decoration: BoxDecoration(
                                        color: isOverBudget 
                                          ? Colors.red.withOpacity(0.1)
                                          : Theme.of(context).colorScheme.primaryContainer,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Icon(
                                        isOverBudget ? Icons.warning : Icons.account_balance_wallet,
                                        color: isOverBudget ? Colors.red : Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            allocation.category,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            ' ₹${allocation.actualSpent.toStringAsFixed(2)} of  ₹${allocation.allocatedAmount.toStringAsFixed(2)}',
                                            style: TextStyle(
                                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          LinearProgressIndicator(
                                            value: progress,
                                            minHeight: 6,
                                            backgroundColor: Colors.grey[200],
                                            valueColor: AlwaysStoppedAnimation<Color>(isOverBudget ? Colors.red : Theme.of(context).colorScheme.primary),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            isOverBudget ? 'Over Budget' : 'Within Budget',
                                            style: TextStyle(
                                              color: isOverBudget ? Colors.red : Colors.green,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
              ),
            ],
          ),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (e, st) => Center(child: Text('Error: $e')),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAllocationDialog(context, ref, categoriesAsync),
        icon: const Icon(Icons.add),
        label: const Text('Add Allocation'),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Allocation allocation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Allocation'),
        content: Text('Are you sure you want to delete the allocation for "${allocation.category}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              await ref.read(deleteAllocationUseCaseProvider).call(allocation.id);
              ref.refresh(allocationsProvider);
              Navigator.pop(context);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showAllocationDialog(BuildContext context, WidgetRef ref, AsyncValue<List<Category>> categoriesAsync, {Allocation? allocation}) {
    final allocatedController = TextEditingController(text: allocation?.allocatedAmount.toString() ?? '');
    final actualController = TextEditingController(text: allocation?.actualSpent.toString() ?? '');
    String selectedCategory = allocation?.category ?? 'Groceries';
    int selectedMonth = allocation?.month ?? DateTime.now().month;
    int selectedYear = allocation?.year ?? DateTime.now().year;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(allocation == null ? 'Add Budget Allocation' : 'Edit Budget Allocation'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              categoriesAsync.when(
                data: (categories) => Column(
                  children: [
                    DropdownButtonFormField<String>(
                      value: selectedCategory,
                      decoration: const InputDecoration(
                        labelText: 'Category',
                        border: OutlineInputBorder(),
                      ),
                      items: ['Groceries', 'Transport', 'Utilities', 'Dining', 'Shopping', 'Health', 'Education', 'Entertainment', 'Other']
                          .map((cat) => DropdownMenuItem(value: cat, child: Text(cat)))
                          .toList(),
                      onChanged: (val) => selectedCategory = val ?? 'Groceries',
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: allocatedController,
                            decoration: const InputDecoration(
                              labelText: 'Allocated Amount',
                              hintText: '0.00',
                              border: OutlineInputBorder(),
                              prefixText: '₹',
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextField(
                            controller: actualController,
                            decoration: const InputDecoration(
                              labelText: 'Actual Spent',
                              hintText: '0.00',
                              border: OutlineInputBorder(),
                              prefixText: '₹',
                            ),
                            keyboardType: TextInputType.number,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<int>(
                            value: selectedMonth,
                            decoration: const InputDecoration(
                              labelText: 'Month',
                              border: OutlineInputBorder(),
                            ),
                            items: List.generate(12, (index) => DropdownMenuItem(
                              value: index + 1,
                              child: Text((index + 1).toString()),
                            )),
                            onChanged: (val) => selectedMonth = val!,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: DropdownButtonFormField<int>(
                            value: selectedYear,
                            decoration: const InputDecoration(
                              labelText: 'Year',
                              border: OutlineInputBorder(),
                            ),
                            items: List.generate(5, (index) => DropdownMenuItem(
                              value: DateTime.now().year - 2 + index,
                              child: Text((DateTime.now().year - 2 + index).toString()),
                            )),
                            onChanged: (val) => selectedYear = val!,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, st) => Text('Error loading categories: $e'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              if (selectedCategory.isNotEmpty && allocatedController.text.isNotEmpty && actualController.text.isNotEmpty) {
                final companion = AllocationsCompanion(
                  category: drift.Value(selectedCategory),
                  allocatedAmount: drift.Value(double.parse(allocatedController.text)),
                  actualSpent: drift.Value(double.parse(actualController.text)),
                  month: drift.Value(selectedMonth),
                  year: drift.Value(selectedYear),
                );
                if (allocation == null) {
                  ref.read(addAllocationUseCaseProvider).call(companion);
                } else {
                  ref.read(updateAllocationUseCaseProvider).call(allocation.id, companion);
                }
                ref.refresh(allocationsProvider);
                Navigator.pop(context);
              }
            },
            child: Text(allocation == null ? 'Add' : 'Update'),
          ),
        ],
      ),
    );
  }
} 