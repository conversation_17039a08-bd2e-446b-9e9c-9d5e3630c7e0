import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/transaction_providers.dart';
import '../../providers/savings_providers.dart';
import '../../providers/ai_providers.dart';
import '../../../core/database/app_database.dart';
import '../../../domain/repositories/transaction_repository.dart' show TransactionWithCategory;
import '../../widgets/universal_scaffold.dart';

// Comment out or patch usages of missing types and extension getters (Saving, Savings, savingTipsProvider, etc.)
// extension ExpenseWithCategoryX on ExpenseWithCategory {
//   double get amount => expense.amount;
//   String get categoryName => category.name;
// }

class SavingTipsPage extends ConsumerWidget {
  const SavingTipsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(transactionsProvider);
    final savingsAsync = ref.watch(savingsProvider);

    return UniversalScaffold(
      title: 'Saving Tips',
      body: transactionsAsync.when(
        data: (transactions) => savingsAsync.when(
          data: (savings) => _buildContent(context, ref, transactions, savings),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (_, __) => _buildErrorState(context),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (_, __) => _buildErrorState(context),
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    WidgetRef ref,
    List<TransactionWithCategory> transactions,
    List<Saving> savings,
  ) {
    // Separate income and expense transactions
    final incomeTransactions = transactions.where((t) => t.transaction.type == 'income').toList();
    final expenseTransactions = transactions.where((t) => t.transaction.type == 'expense').toList();
    
    final monthlyIncome = incomeTransactions.fold<double>(0, (sum, transaction) => sum + transaction.transaction.amount);
    final monthlyExpenses = expenseTransactions.fold<double>(0, (sum, transaction) => sum + transaction.transaction.amount);
    final savingsGoal = savings.fold<double>(0, (sum, saving) => sum + saving.targetAmount);
    
    // Get top spending categories
    final categorySpending = <String, double>{};
    for (final transaction in expenseTransactions) {
      final categoryName = transaction.category?.name ?? 'Unknown';
      categorySpending[categoryName] = (categorySpending[categoryName] ?? 0) + transaction.transaction.amount;
    }
    final topCategories = categorySpending.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value));
    final topCategoryNames = topCategories.take(3).map((e) => e.key).toList();

    final tipsParams = {
      'monthlyIncome': monthlyIncome,
      'monthlyExpenses': monthlyExpenses,
      'topCategories': topCategoryNames,
      'savingsGoal': savingsGoal,
    };

    final tipsAsync = ref.watch(savingTipsProvider(tipsParams));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context, monthlyIncome, monthlyExpenses, savingsGoal),
          const SizedBox(height: 24),
          
          // Financial Summary
          _buildFinancialSummary(context, monthlyIncome, monthlyExpenses, topCategories.map((e) => e.key).toList()),
          const SizedBox(height: 24),
          
          // AI Tips
          _buildAITips(context, ref, tipsAsync),
          const SizedBox(height: 24),
          
          // Additional Tips
          _buildAdditionalTips(context),
        ],
      ),
    );
  }

  Widget _buildHeader(
    BuildContext context,
    double monthlyIncome,
    double monthlyExpenses,
    double savingsGoal,
  ) {
    final savingsRate = monthlyIncome > 0 ? ((monthlyIncome - monthlyExpenses) / monthlyIncome) * 100 : 0;
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primaryContainer,
            ],
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.lightbulb,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  'Personalized Saving Tips',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Savings Rate',
                    '${savingsRate.toStringAsFixed(1)}%',
                    savingsRate >= 20 ? Colors.green : savingsRate >= 10 ? Colors.orange : Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Monthly Savings',
                    '\$${(monthlyIncome - monthlyExpenses).toStringAsFixed(0)}',
                    Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onPrimary.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialSummary(
    BuildContext context,
    double monthlyIncome,
    double monthlyExpenses,
    List<String> topCategories,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  'Financial Summary',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Monthly Income', '\$${monthlyIncome.toStringAsFixed(2)}', Colors.green),
            const SizedBox(height: 8),
            _buildSummaryRow('Monthly Expenses', '\$${monthlyExpenses.toStringAsFixed(2)}', Colors.red),
            const SizedBox(height: 8),
            _buildSummaryRow('Net Savings', '\$${(monthlyIncome - monthlyExpenses).toStringAsFixed(2)}', Colors.blue),
            if (topCategories.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Top Spending Categories:',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: topCategories.map((category) => Chip(
                  label: Text(category),
                  backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                )).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildAITips(BuildContext context, WidgetRef ref, AsyncValue<List<String>> tipsAsync) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  'AI-Powered Tips',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Personalized recommendations based on your spending patterns',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 16),
            tipsAsync.when(
              data: (tips) => Column(
                children: tips.asMap().entries.map((entry) {
                  final index = entry.key;
                  final tip = entry.value;
                  return _buildTipCard(context, index + 1, tip);
                }).toList(),
              ),
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (_, __) => _buildErrorTips(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipCard(BuildContext context, int number, String tip) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                '$number',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              tip,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorTips(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Unable to load AI tips. Please check your connection and try again.',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalTips(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.tips_and_updates,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 12),
                Text(
                  'General Saving Tips',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildGeneralTip(
              context,
              'Emergency Fund',
              'Aim to save 3-6 months of expenses for emergencies',
              Icons.shield,
            ),
            const SizedBox(height: 8),
            _buildGeneralTip(
              context,
              '50/30/20 Rule',
              '50% needs, 30% wants, 20% savings',
              Icons.pie_chart,
            ),
            const SizedBox(height: 8),
            _buildGeneralTip(
              context,
              'Automate Savings',
              'Set up automatic transfers to build savings effortlessly',
              Icons.sync,
            ),
            const SizedBox(height: 8),
            _buildGeneralTip(
              context,
              'Track Spending',
              'Monitor your expenses to identify saving opportunities',
              Icons.track_changes,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralTip(BuildContext context, String title, String description, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading data',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please try again later',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
} 