import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/bank_account_providers.dart';
import '../../providers/database_provider.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../../domain/repositories/bank_account_repository.dart';
import 'package:financial_app/domain/repositories/expense_repository.dart';
import 'package:intl/intl.dart';
import '../../providers/expense_providers.dart';
import '../../../domain/repositories/expense_repository.dart';
import '../../providers/transaction_providers.dart';
import '../../../core/services/transaction_service.dart';

// Comment out or patch usages of missing extension getters (name, type, currency) on BankAccount
// extension BankAccountX on BankAccount {
//   String get name => bankName;
//   String get type => accountType;
//   String get currency => '';
// }

class BankAccountsPage extends ConsumerStatefulWidget {
  const BankAccountsPage({super.key});

  @override
  ConsumerState<BankAccountsPage> createState() => _BankAccountsPageState();
}

class _BankAccountsPageState extends ConsumerState<BankAccountsPage> {
  String _search = '';
  String? _selectedType;
  double? _minBalance;
  double? _maxBalance;

  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(bankAccountsProvider);
    
    return Scaffold(
      body: Column(
        children: [
          // Compact Header Section
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                _buildCompactBankInsight(accountsAsync),
                const SizedBox(height: 12),
                _buildCompactFilterBar(),
              ],
            ),
          ),
          // Accounts List with more space
          Expanded(
            child: Container(
              color: Theme.of(context).colorScheme.surfaceContainerLowest,
              child: accountsAsync.when(
                data: (accounts) => _buildAccountsList(context, ref, accounts),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, st) => Center(child: Text('Error: $e')),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAccountDialog(context, ref),
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildCompactBankInsight(AsyncValue<List<BankAccount>> accountsAsync) {
    return accountsAsync.when(
      data: (accounts) {
        if (accounts.isEmpty) {
          return Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance_outlined,
                  color: Theme.of(context).colorScheme.outline,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'No accounts yet',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.outline,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          );
        }

        final totalBalance = accounts.fold<double>(0, (sum, acc) => sum + acc.currentBalance);
        final savingsAccounts = accounts.where((acc) => acc.accountType.toLowerCase().contains('savings')).length;
        final currentAccounts = accounts.where((acc) => acc.accountType.toLowerCase().contains('current')).length;
        final cashAccounts = accounts.where((acc) => acc.bankName.toLowerCase() == 'cash').length;

        return _ProfessionalBankInsightCard(
          totalBalance: totalBalance,
          accounts: accounts,
          savingsCount: savingsAccounts,
          currentCount: currentAccounts,
          cashCount: cashAccounts,
        );
      },
      loading: () => Container(
        padding: const EdgeInsets.all(12),
        child: const Center(child: CircularProgressIndicator()),
      ),
      error: (e, st) => Container(
        padding: const EdgeInsets.all(12),
        child: Text('Error: $e', style: const TextStyle(color: Colors.red)),
      ),
    );
  }

  Widget _buildInsightItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 9,
            color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactFilterBar() {
    return Row(
      children: [
        // Search
        Expanded(
          child: Container(
            height: 36,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              onChanged: (value) => setState(() => _search = value),
              decoration: InputDecoration(
                hintText: 'Search accounts...',
                prefixIcon: const Icon(Icons.search, size: 18),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                hintStyle: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Filter Button
        Container(
          height: 36,
          child: PopupMenuButton<String?>(
            initialValue: _selectedType,
            onSelected: (value) => setState(() => _selectedType = value),
            itemBuilder: (context) => [
              const PopupMenuItem<String?>(value: null, child: Text('All Types')),
              const PopupMenuItem<String?>(value: 'Savings', child: Text('Savings')),
              const PopupMenuItem<String?>(value: 'Current', child: Text('Current')),
              const PopupMenuItem<String?>(value: 'Cash', child: Text('Cash')),
            ],
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _selectedType ?? 'All',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAccountsList(BuildContext context, WidgetRef ref, List<BankAccount> accounts) {
    // Filter accounts based on search and type
    List<BankAccount> filteredAccounts = accounts.where((account) {
      final matchesSearch = account.bankName.toLowerCase().contains(_search.toLowerCase()) ||
                           account.accountType.toLowerCase().contains(_search.toLowerCase());
      final matchesType = _selectedType == null || 
                         account.accountType.toLowerCase().contains(_selectedType!.toLowerCase()) ||
                         (account.bankName.toLowerCase() == 'cash' && _selectedType!.toLowerCase() == 'cash');
      return matchesSearch && matchesType;
    }).toList();

    if (filteredAccounts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.account_balance_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
                          Text(
                accounts.isEmpty ? 'No accounts yet' : 'No accounts match your search',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                accounts.isEmpty 
                  ? 'Add your first account to start tracking'
                  : 'Try adjusting your search or filters',
              style: TextStyle(
                color: Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(12),
      itemCount: filteredAccounts.length,
      itemBuilder: (context, index) {
        final account = filteredAccounts[index];
        return _buildModernAccountCard(context, ref, account);
      },
    );
  }

  Widget _buildModernAccountCard(BuildContext context, WidgetRef ref, BankAccount account) {
    final isCash = account.bankName.toLowerCase() == 'cash';
    final dateStr = DateFormat('dd MMM yyyy').format(account.lastUpdated);
    
    // Get account type icon and color
    IconData accountIcon;
    Color accountColor;
    
    if (isCash) {
      accountIcon = Icons.money;
      accountColor = Colors.green;
    } else if (account.accountType.toLowerCase().contains('savings')) {
      accountIcon = Icons.savings;
      accountColor = Colors.blue;
    } else {
      accountIcon = Icons.account_balance;
      accountColor = Colors.purple;
    }

    return Card(
      elevation: 3,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () => _showAccountDetails(context, ref, account),
        onLongPress: () => _showAccountOptions(context, ref, account),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.surface,
                Theme.of(context).colorScheme.surfaceContainerHighest,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Row with Account and Balance
              Row(
                children: [
                  // Account Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          accountColor.withOpacity(0.2),
                          accountColor.withOpacity(0.1),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      accountIcon,
                      size: 20,
                      color: accountColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Account Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          account.bankName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          account.accountType,
                          style: TextStyle(
                            fontSize: 13,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Balance
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '₹${account.currentBalance.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: account.currentBalance >= 0 ? Colors.green : Colors.red,
                        ),
                      ),
                      Text(
                        dateStr,
                        style: TextStyle(
                          fontSize: 11,
                          color: Theme.of(context).colorScheme.outline.withOpacity(0.7),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // Account Number if available
              if (account.lastFourDigits != null && account.lastFourDigits!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: accountColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: accountColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.credit_card,
                        size: 12,
                        color: accountColor,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '****${account.lastFourDigits}',
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: accountColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _showAccountDetails(BuildContext context, WidgetRef ref, BankAccount account) {
    // Navigate to account details or transaction history
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AccountTransactionHistoryPage(account: account),
      ),
    );
  }

  void _showAccountOptions(BuildContext context, WidgetRef ref, BankAccount account) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit Account'),
              onTap: () {
                Navigator.pop(context);
                _showAccountDialog(context, ref, account: account);
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Transaction History'),
              onTap: () {
                Navigator.pop(context);
                _showAccountDetails(context, ref, account);
              },
            ),
            if (account.bankName.toLowerCase() != 'cash')
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('Delete Account', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  _showDeleteConfirmation(context, ref, account);
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, BankAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete ${account.bankName}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteBankAccountUseCaseProvider).call(account.id);
              ref.refresh(bankAccountsProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAccountDialog(BuildContext context, WidgetRef ref, {BankAccount? account}) {
    final nameController = TextEditingController(text: account?.bankName ?? '');
    String selectedType = account?.accountType ?? 'Savings';
    final balanceController = TextEditingController(text: account?.currentBalance.toString() ?? '');
    final lastFourDigitsController = TextEditingController(text: account?.lastFourDigits ?? '');
    DateTime lastUpdated = account?.lastUpdated ?? DateTime.now();
    
    // Ensure selectedType is valid
    final validAccountTypes = [
      'Savings',
      'Current',
      'Fixed Deposit',
      'Recurring Deposit',
      'PPF',
      'NPS',
      'Mutual Fund',
      'Stocks',
      'Gold',
      'Cash',
      'Other',
    ];
    
    if (!validAccountTypes.contains(selectedType)) {
      selectedType = 'Savings';
    }
    
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(account == null ? 'Add Bank Account' : 'Edit Bank Account'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'Account Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Account Type Dropdown
                    DropdownButtonFormField<String>(
                      value: selectedType,
                      decoration: const InputDecoration(
                        labelText: 'Account Type',
                        border: OutlineInputBorder(),
                      ),
                      items: validAccountTypes.map((type) => DropdownMenuItem(
                        value: type,
                        child: Text(type),
                      )).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: balanceController,
                      decoration: const InputDecoration(
                        labelText: 'Balance',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: lastFourDigitsController,
                      decoration: const InputDecoration(
                        labelText: 'Last 4 Digits of Account Number',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('Last Updated'),
                      subtitle: Text(lastUpdated.toLocal().toString().split(' ')[0]),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: lastUpdated,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) {
                          setState(() {
                            lastUpdated = picked;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (nameController.text.isNotEmpty && selectedType.isNotEmpty) {
                      // Check if trying to create a cash account when one already exists
                      if (nameController.text.toLowerCase() == 'cash' && account == null) {
                        final existingAccounts = ref.read(bankAccountsProvider).value ?? [];
                        final cashExists = existingAccounts.any((acc) => acc.bankName.toLowerCase() == 'cash');
                        if (cashExists) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Cash account already exists. Only one cash account is allowed.'),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }
                      }

                      final companion = BankAccountsCompanion.insert(
                        bankName: nameController.text,
                        accountType: selectedType,
                        openingBalance: drift.Value(double.tryParse(balanceController.text) ?? 0.0),
                        currentBalance: drift.Value(double.tryParse(balanceController.text) ?? 0.0),
                        lastFourDigits: lastFourDigitsController.text.isNotEmpty 
                          ? drift.Value(lastFourDigitsController.text) 
                          : drift.Value(null),
                        createdAt: lastUpdated,
                        lastUpdated: lastUpdated,
                      );
                      if (account == null) {
                        await ref.read(addBankAccountUseCaseProvider).call(companion);
                      } else {
                        await ref.read(updateBankAccountUseCaseProvider).call(account.id, companion);
                      }
                      ref.refresh(bankAccountsProvider);
                      Navigator.pop(context);
                    }
                  },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

final getAccountTransactionsProvider = FutureProvider.family<List<TransactionWithCategory>, int>((ref, accountId) async {
  final dbAsync = ref.watch(databaseProvider);
  final db = await dbAsync.when(
    data: (database) => database,
    loading: () => throw Exception('Database not ready'),
    error: (error, stack) => throw Exception('Database error: $error'),
  );
  
  // Get all transactions with categories
  final allTransactions = await (db.select(db.transactions).join([
    drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
  ])).get();
  
  // Filter transactions by account ID (both income and expenses)
  return allTransactions
      .where((row) {
        final transaction = row.readTable(db.transactions);
        // Include transactions where this account is either the source (expenses) or destination (income)
        return transaction.fromAccountId == accountId || transaction.toAccountId == accountId;
      })
      .map((row) {
        final transaction = row.readTable(db.transactions);
        final category = row.readTableOrNull(db.categories);
        return TransactionWithCategory(
          transaction: transaction, 
          category: category,
        );
      })
      .toList();
});

final getAllExpensesWithAccountIdProvider = FutureProvider<List<TransactionWithCategory>>((ref) async {
  final dbAsync = ref.watch(databaseProvider);
  final db = await dbAsync.when(
    data: (database) => database,
    loading: () => throw Exception('Database not ready'),
    error: (error, stack) => throw Exception('Database error: $error'),
  );
  
  final allTransactions = await (db.select(db.transactions).join([
    drift.leftOuterJoin(db.categories, db.categories.id.equalsExp(db.transactions.categoryId)),
  ])).get();
  
  return allTransactions.map((row) {
    final transaction = row.readTable(db.transactions);
    final category = row.readTableOrNull(db.categories);
    return TransactionWithCategory(
      transaction: transaction, 
      category: category,
    );
  }).toList();
});

class AccountTransactionHistoryPage extends ConsumerWidget {
  final BankAccount account;
  const AccountTransactionHistoryPage({super.key, required this.account});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final transactionsAsync = ref.watch(getAccountTransactionsProvider(account.id));
    
    return Scaffold(
      appBar: AppBar(
        title: Text('${account.bankName} Account'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () {
              ref.refresh(getAccountTransactionsProvider(account.id));
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Account Summary Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Account Info
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            account.bankName.toLowerCase() == 'cash' 
                              ? Colors.green.withOpacity(0.2)
                              : Colors.blue.withOpacity(0.2),
                            account.bankName.toLowerCase() == 'cash'
                              ? Colors.green.withOpacity(0.1)
                              : Colors.blue.withOpacity(0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        account.bankName.toLowerCase() == 'cash' ? Icons.money : Icons.account_balance,
                        color: account.bankName.toLowerCase() == 'cash' ? Colors.green : Colors.blue,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            account.bankName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            account.accountType,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '₹${account.currentBalance.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: account.currentBalance >= 0 ? Colors.green : Colors.red,
                          ),
                        ),
                        Text(
                          'Current Balance',
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Transaction Summary
                transactionsAsync.when(
                  data: (transactions) {
                    final totalExpenses = transactions.where((t) => t.transaction.type == 'expense').fold<double>(0, (sum, t) => sum + t.transaction.amount);
                    final totalIncome = transactions.where((t) => t.transaction.type == 'income').fold<double>(0, (sum, t) => sum + t.transaction.amount);
                    final transactionCount = transactions.length;
                    return Row(
                      children: [
                        Expanded(
                          child: _buildSummaryItem(
                            context,
                            'Transactions',
                            transactionCount.toString(),
                            Icons.receipt_long,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            context,
                            'Income',
                            '₹${totalIncome.toStringAsFixed(0)}',
                            Icons.trending_up,
                            color: Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildSummaryItem(
                            context,
                            'Expenses',
                            '₹${totalExpenses.toStringAsFixed(0)}',
                            Icons.trending_down,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    );
                  },
                  loading: () => const Row(
                    children: [
                      Expanded(child: Center(child: CircularProgressIndicator())),
                      Expanded(child: Center(child: CircularProgressIndicator())),
                      Expanded(child: Center(child: CircularProgressIndicator())),
                    ],
                  ),
                  error: (_, __) => const Row(
                    children: [
                      Expanded(child: Center(child: Text('Error loading data'))),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Transactions List
          Expanded(
            child: Container(
              color: Theme.of(context).colorScheme.surfaceContainerLowest,
              child: transactionsAsync.when(
                data: (transactions) {
                  if (transactions.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long_outlined,
                            size: 64,
                            color: Theme.of(context).colorScheme.outline,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'No transactions yet',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).colorScheme.outline,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Transactions will appear here when you add expenses, income, or transfers',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          // Debug: Show account ID
                          Text(
                            'Account ID: ${account.id}',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.outline,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // Group by date
                  final grouped = <String, List<TransactionWithCategory>>{};
                  for (final t in transactions) {
                    final dateStr = DateFormat('yyyy-MM-dd').format(t.transaction.date);
                    grouped.putIfAbsent(dateStr, () => []).add(t);
                  }

                  final sortedDates = grouped.keys.toList()..sort((a, b) => b.compareTo(a));

                  return ListView.builder(
                    padding: const EdgeInsets.all(12),
                    itemCount: sortedDates.length,
                    itemBuilder: (context, index) {
                      final date = sortedDates[index];
                      final dayTransactions = grouped[date]!;
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Date Header
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                            child: Text(
                              DateFormat('EEE, MMM d, yyyy').format(DateTime.parse(date)),
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ),
                          // Transactions for this date
                          ...dayTransactions.map((t) => _buildModernTransactionCard(context, t)),
                        ],
                      );
                    },
                  );
                },
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (e, st) => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading transactions',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Please try again later',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.outline,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(BuildContext context, String label, String value, IconData icon, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: color ?? Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color ?? Theme.of(context).colorScheme.onSurface,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: Theme.of(context).colorScheme.outline,
          ),
        ),
      ],
    );
  }

  Widget _buildModernTransactionCard(BuildContext context, TransactionWithCategory t) {
    final transaction = t.transaction;
    final category = t.category;
    final dateStr = DateFormat('dd MMM yyyy').format(transaction.date);
    final timeStr = DateFormat('HH:mm').format(transaction.date);
    
    // Get payment method icon and color
    IconData paymentIcon;
    Color paymentColor;
    String paymentText;
    switch (transaction.type) {
      case 'expense':
        paymentIcon = Icons.trending_down;
        paymentColor = Colors.red;
        paymentText = 'Expense';
        break;
      case 'income':
        paymentIcon = Icons.trending_up;
        paymentColor = Colors.green;
        paymentText = 'Income';
        break;
      case 'transfer':
        paymentIcon = Icons.swap_horiz;
        paymentColor = Colors.blue;
        paymentText = 'Transfer';
        break;
      default:
        paymentIcon = Icons.receipt_long;
        paymentColor = Colors.grey;
        paymentText = transaction.type;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Payment type icon
            Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: paymentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(paymentIcon, color: paymentColor, size: 20),
            ),
            const SizedBox(width: 12),
            // Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (category != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            category.name,
                            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 13),
                          ),
                        ),
                      if (category != null) const SizedBox(width: 8),
                      Text(
                        paymentText,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: paymentColor,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                  if (transaction.description != null && transaction.description!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 2),
                      child: Text(
                        transaction.description!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.outline,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
            // Amount
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '₹${transaction.amount.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: paymentColor,
                  ),
                ),
                Text(
                  timeStr,
                  style: TextStyle(
                    fontSize: 10,
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _ProfessionalBankInsightCard extends StatefulWidget {
  final double totalBalance;
  final List<BankAccount> accounts;
  final int savingsCount;
  final int currentCount;
  final int cashCount;

  const _ProfessionalBankInsightCard({
    required this.totalBalance,
    required this.accounts,
    required this.savingsCount,
    required this.currentCount,
    required this.cashCount,
  });

  @override
  State<_ProfessionalBankInsightCard> createState() => _ProfessionalBankInsightCardState();
}

class _ProfessionalBankInsightCardState extends State<_ProfessionalBankInsightCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.primaryContainer,
            Theme.of(context).colorScheme.primaryContainer.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header - Always visible
          InkWell(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  // Icon
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.account_balance_outlined,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Main content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Financial Overview',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onPrimaryContainer,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.green.withOpacity(0.3),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.trending_up,
                                    size: 12,
                                    color: Colors.green,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${widget.accounts.length} Accounts',
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '₹${widget.totalBalance.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        Text(
                          'Total balance across all accounts',
                          style: TextStyle(
                            fontSize: 10,
                            color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Expand/Collapse Button
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      isExpanded ? Icons.expand_less : Icons.expand_more,
                      size: 18,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Expanded content
          if (isExpanded) ...[
            Container(
              margin: const EdgeInsets.fromLTRB(12, 0, 12, 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildAccountBreakdown(),
                  const SizedBox(height: 12),
                  _buildTopAccounts(),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountBreakdown() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Savings',
            '${widget.savingsCount}',
            Icons.savings,
            Colors.blue,
          ),
        ),
        Container(
          width: 1,
          height: 40,
          color: Colors.white.withOpacity(0.2),
        ),
        Expanded(
          child: _buildStatItem(
            'Current',
            '${widget.currentCount}',
            Icons.account_balance,
            Colors.purple,
          ),
        ),
        Container(
          width: 1,
          height: 40,
          color: Colors.white.withOpacity(0.2),
        ),
        Expanded(
          child: _buildStatItem(
            'Cash',
            '${widget.cashCount}',
            Icons.money,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimaryContainer,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 9,
            color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildTopAccounts() {
    // Get top 3 accounts by balance
    final topAccounts = widget.accounts
        .where((acc) => acc.currentBalance > 0)
        .toList()
      ..sort((a, b) => b.currentBalance.compareTo(a.currentBalance));
    
    final displayAccounts = topAccounts.take(3).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 14,
              color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
            ),
            const SizedBox(width: 6),
            Text(
              'Top Accounts',
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...displayAccounts.map((account) => Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  account.bankName,
                  style: TextStyle(
                    fontSize: 10,
                    color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.9),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                '₹${account.currentBalance.toStringAsFixed(0)}',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }
}
