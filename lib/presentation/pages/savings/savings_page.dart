import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../providers/savings_providers.dart';
import '../../providers/bank_account_providers.dart';
import '../../providers/expense_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' show Value;
import 'package:drift/drift.dart' as drift;
import '../../widgets/universal_scaffold.dart';

class SavingsPage extends ConsumerStatefulWidget {
  const SavingsPage({super.key});

  @override
  ConsumerState<SavingsPage> createState() => _SavingsPageState();
}

class _SavingsPageState extends ConsumerState<SavingsPage> {
  @override
  Widget build(BuildContext context) {
    final savingsAsync = ref.watch(savingsProvider);
    final bankAccountsAsync = ref.watch(bankAccountsProvider);
    final expensesAsync = ref.watch(expensesProvider);

    return UniversalScaffold(
      title: 'Savings',
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(child: _buildSavingsGoals(savingsAsync)),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddSavingsDialog(context, savingsAsync),
        icon: const Icon(Icons.add),
        label: const Text('Add Savings'),
      ),
    );
  }

  Widget _buildSavingsOverview(AsyncValue<List<Saving>> savingsAsync) {
    return savingsAsync.when(
      data: (savings) {
        final totalSaved = savings.fold<double>(0, (sum, s) => sum + s.actualSaved);
        final totalTarget = savings.fold<double>(0, (sum, s) => sum + s.targetAmount);
        final progress = totalTarget > 0 ? totalSaved / totalTarget : 0.0;

        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Icon(
                        Icons.savings,
                        color: Colors.green,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Total Saved',
                            style: TextStyle(
                              fontSize: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          Text(
                            '₹${totalSaved.toStringAsFixed(0)}',
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Target: ₹${totalTarget.toStringAsFixed(0)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        Text(
                          '${(progress * 100).toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: Colors.grey.shade200,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  minHeight: 8,
                ),
              ],
            ),
          ),
        );
      },
      loading: () => const SizedBox(height: 120, child: Center(child: CircularProgressIndicator())),
      error: (e, st) => Card(
        color: Colors.red.shade50,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('Error: $e', style: const TextStyle(color: Colors.red)),
        ),
      ),
    );
  }

  Widget _buildLinkedAccounts(AsyncValue<List<BankAccount>> bankAccountsAsync) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.account_balance, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Linked Savings Accounts',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: () => _showLinkAccountDialog(context),
                  icon: const Icon(Icons.add),
                  label: const Text('Link Account'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            bankAccountsAsync.when(
              data: (accounts) {
                final savingsAccounts = accounts.where((a) => a.accountType == 'savings').toList();
                if (savingsAccounts.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.grey.shade600),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'No linked accounts. Link your savings account to track automatic transfers.',
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return Column(
                  children: savingsAccounts.map((account) => _buildAccountCard(account)).toList(),
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => Text('Error: $e'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountCard(BankAccount account) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.account_balance, color: Colors.blue),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  account.bankName,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (account.lastFourDigits != null)
                  Text(
                    '****${account.lastFourDigits}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '₹${account.currentBalance.toStringAsFixed(0)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              Text(
                'Balance',
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              if (value == 'edit') {
                _showEditAccountDialog(context, account);
              } else if (value == 'delete') {
                _showDeleteAccountDialog(context, account);
              } else if (value == 'transactions') {
                _showAccountTransactions(context, account);
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit),
                    SizedBox(width: 8),
                    Text('Edit'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'transactions',
                child: Row(
                  children: [
                    Icon(Icons.history),
                    SizedBox(width: 8),
                    Text('Transactions'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSavingsGoals(AsyncValue<List<Saving>> savingsAsync) {
    return savingsAsync.when(
      data: (savings) {
        if (savings.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.savings, size: 64, color: Theme.of(context).colorScheme.outline),
                const SizedBox(height: 16),
                Text(
                  'No Savings Goals',
                  style: TextStyle(fontSize: 18, color: Theme.of(context).colorScheme.outline),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create your first savings goal to start tracking your progress.',
                  style: TextStyle(color: Theme.of(context).colorScheme.outline),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }
        return ListView.builder(
          itemCount: savings.length,
          itemBuilder: (context, index) => _buildSavingsGoalCard(savings[index]),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, st) => Center(child: Text('Error: $e')),
    );
  }

  Widget _buildSavingsGoalCard(Saving savings) {
    final progress = savings.targetAmount > 0 ? savings.actualSaved / savings.targetAmount : 0.0;
    final remaining = savings.targetAmount - savings.actualSaved;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Icon(Icons.savings, color: Colors.green),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '₹${savings.targetAmount.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Target Amount',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '₹${savings.actualSaved.toStringAsFixed(0)}',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      'Saved',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showEditSavingsDialog(context, savings);
                    } else if (value == 'delete') {
                      _showDeleteSavingsDialog(context, savings);
                    } else if (value == 'add') {
                      _showAddAmountDialog(context, savings);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'add',
                      child: Row(
                        children: [
                          Icon(Icons.add),
                          SizedBox(width: 8),
                          Text('Add Amount'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey.shade200,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
              minHeight: 6,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Text(
                  '${(progress * 100).toStringAsFixed(1)}% Complete',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const Spacer(),
                Text(
                  '₹${remaining.toStringAsFixed(0)} remaining',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            if (savings.note != null) ...[
              const SizedBox(height: 8),
              Text(
                savings.note!,
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showAddSavingsDialog(BuildContext context, AsyncValue<List<Saving>> savingsAsync) {
    final targetController = TextEditingController();
    final noteController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Savings Goal'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: targetController,
              decoration: const InputDecoration(
                labelText: 'Target Amount',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'Note (Optional)',
                hintText: 'e.g., Emergency Fund, Vacation Savings',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final targetAmount = double.tryParse(targetController.text) ?? 0;
              if (targetAmount > 0) {
                final companion = SavingsCompanion.insert(
                  month: DateTime.now().month,
                  year: DateTime.now().year,
                  targetAmount: targetAmount,
                  actualSaved: 0,
                  note: Value(noteController.text.isNotEmpty ? noteController.text : null),
                );
                await ref.read(addSavingsUseCaseProvider).call(companion);
                ref.refresh(savingsProvider);
                Navigator.pop(context);
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showLinkAccountDialog(BuildContext context) {
    final bankNameController = TextEditingController();
    final accountNumberController = TextEditingController();
    final balanceController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Link Savings Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: bankNameController,
              decoration: const InputDecoration(
                labelText: 'Bank Name',
                hintText: 'e.g., HDFC Bank, SBI',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: accountNumberController,
              decoration: const InputDecoration(
                labelText: 'Account Number',
                hintText: 'Enter your account number',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: balanceController,
              decoration: const InputDecoration(
                labelText: 'Current Balance',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final balance = double.tryParse(balanceController.text) ?? 0;
              final accountNumber = accountNumberController.text;
              final lastFourDigits = accountNumber.length >= 4 
                  ? accountNumber.substring(accountNumber.length - 4)
                  : accountNumber;

              final companion = BankAccountsCompanion.insert(
                bankName: bankNameController.text,
                accountNumber: accountNumberController.text.isNotEmpty ? drift.Value(accountNumberController.text) : drift.Value(null),
                accountType: 'savings',
                openingBalance: drift.Value(balance),
                currentBalance: drift.Value(balance),
                lastFourDigits: lastFourDigits != null ? drift.Value(lastFourDigits) : drift.Value(null),
                createdAt: DateTime.now(),
                lastUpdated: DateTime.now(),
              );
              
              await ref.read(addBankAccountUseCaseProvider).call(companion);
              ref.refresh(bankAccountsProvider);
              Navigator.pop(context);
            },
            child: const Text('Link Account'),
          ),
        ],
      ),
    );
  }

  void _showEditAccountDialog(BuildContext context, BankAccount account) {
    final bankNameController = TextEditingController(text: account.bankName);
    final balanceController = TextEditingController(text: account.currentBalance.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: bankNameController,
              decoration: const InputDecoration(
                labelText: 'Bank Name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: balanceController,
              decoration: const InputDecoration(
                labelText: 'Current Balance',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final balance = double.tryParse(balanceController.text) ?? 0;
              final companion = BankAccountsCompanion(
                bankName: Value(bankNameController.text),
                openingBalance: Value(balance),
                currentBalance: Value(balance),
                lastUpdated: Value(DateTime.now()),
              );
              
              await ref.read(updateBankAccountUseCaseProvider).call(account.id, companion);
              ref.refresh(bankAccountsProvider);
              Navigator.pop(context);
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context, BankAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: Text('Are you sure you want to delete ${account.bankName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteBankAccountUseCaseProvider).call(account.id);
              ref.refresh(bankAccountsProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showEditSavingsDialog(BuildContext context, Saving savings) {
    final targetController = TextEditingController(text: savings.targetAmount.toString());
    final actualController = TextEditingController(text: savings.actualSaved.toString());
    final noteController = TextEditingController(text: savings.note ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Savings Goal'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: targetController,
              decoration: const InputDecoration(
                labelText: 'Target Amount',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: actualController,
              decoration: const InputDecoration(
                labelText: 'Current Saved Amount',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: noteController,
              decoration: const InputDecoration(
                labelText: 'Note',
              ),
              maxLines: 2,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final targetAmount = double.tryParse(targetController.text) ?? 0;
              final actualSaved = double.tryParse(actualController.text) ?? 0;
              
              final companion = SavingsCompanion(
                targetAmount: Value(targetAmount),
                actualSaved: Value(actualSaved),
                note: Value(noteController.text.isNotEmpty ? noteController.text : null),
              );
              
              await ref.read(updateSavingsUseCaseProvider).call(savings.id, companion);
              ref.refresh(savingsProvider);
              Navigator.pop(context);
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showDeleteSavingsDialog(BuildContext context, Saving savings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Savings Goal'),
        content: const Text('Are you sure you want to delete this savings goal?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteSavingsUseCaseProvider).call(savings.id);
              ref.refresh(savingsProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showAddAmountDialog(BuildContext context, Saving savings) {
    final amountController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add to Savings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: amountController,
              decoration: const InputDecoration(
                labelText: 'Amount to Add',
                prefixText: '₹',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final amount = double.tryParse(amountController.text) ?? 0;
              if (amount > 0) {
                final newAmount = savings.actualSaved + amount;
                final companion = SavingsCompanion(
                  actualSaved: Value(newAmount),
                );
                
                await ref.read(updateSavingsUseCaseProvider).call(savings.id, companion);
                ref.refresh(savingsProvider);
                Navigator.pop(context);
              }
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showTransactionHistory(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Savings Transaction History'),
        content: const SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Center(
            child: Text('Transaction history feature coming soon!'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAccountTransactions(BuildContext context, BankAccount account) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${account.bankName} Transactions'),
        content: const SizedBox(
          width: double.maxFinite,
          height: 400,
          child: Center(
            child: Text('Account transaction history coming soon!'),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
} 