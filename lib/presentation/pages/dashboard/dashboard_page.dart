import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/bank_account_providers.dart';
import '../../providers/database_provider.dart';
import '../../../core/database/app_database.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

final getAllTransactionsProvider = FutureProvider<List<Transaction>>((ref) async {
  final dbAsync = ref.watch(databaseProvider);
  final db = await dbAsync.when(
    data: (database) => database,
    loading: () => throw Exception('Database not ready'),
    error: (error, stack) => throw Exception('Database error: $error'),
  );
  return await db.select(db.transactions).get();
});

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final accountsAsync = ref.watch(bankAccountsProvider);
    final transactionsAsync = ref.watch(getAllTransactionsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Greeting
          Text(
            'Welcome back!',
            style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'Here is your financial overview',
            style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),
          // Account Summary Card
          accountsAsync.when(
            data: (accounts) {
              final totalBalance = accounts.fold<double>(0, (sum, acc) => sum + acc.currentBalance);
              final savingsAccounts = accounts.where((acc) => acc.accountType.toLowerCase().contains('savings')).length;
              final currentAccounts = accounts.where((acc) => acc.accountType.toLowerCase().contains('current')).length;
              return Card(
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                            child: Icon(Icons.account_balance_wallet, color: theme.colorScheme.primary),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Total Balance', style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600])),
                                const SizedBox(height: 8),
                                Text(
                                  '₹${totalBalance.toStringAsFixed(2)}',
                                  style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _iconProgress(
                            context,
                            icon: Icons.account_balance_wallet,
                            label: 'Accounts',
                            value: accounts.length,
                            color: theme.colorScheme.primary,
                          ),
                          _iconProgress(
                            context,
                            icon: Icons.savings,
                            label: 'Savings',
                            value: savingsAccounts,
                            color: Colors.green,
                          ),
                          _iconProgress(
                            context,
                            icon: Icons.account_balance,
                            label: 'Current',
                            value: currentAccounts,
                            color: Colors.blue,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
            loading: () => _loadingCard(height: 120),
            error: (e, st) => _errorCard(e),
          ),
          const SizedBox(height: 24),
          // Pie Chart for Top Categories (using description as a proxy for category)
          transactionsAsync.when(
            data: (transactions) {
              final categoryTotals = <String, double>{};
              for (var t in transactions.where((t) => t.type == 'expense')) {
                final cat = t.description ?? 'Other';
                categoryTotals[cat] = (categoryTotals[cat] ?? 0) + t.amount;
              }
              final topCategories = categoryTotals.entries.toList()
                ..sort((a, b) => b.value.compareTo(a.value));
              if (topCategories.isEmpty) {
                return Container();
              }
              return Card(
                elevation: 0,
                color: theme.colorScheme.surfaceContainerHighest,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                  child: Row(
                    children: [
                      SizedBox(
                        width: 80,
                        height: 80,
                        child: PieChart(
                          PieChartData(
                            sections: topCategories.take(4).map((entry) {
                              final color = _colorFromHex('#${entry.key.hashCode.toRadixString(16).padLeft(6, '0')}');
                              return PieChartSectionData(
                                color: color,
                                value: entry.value,
                                title: '',
                                radius: 30,
                              );
                            }).toList(),
                            sectionsSpace: 0,
                            centerSpaceRadius: 22,
                          ),
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Top Categories', style: theme.textTheme.bodyMedium),
                            ...topCategories.take(4).map((entry) {
                              final color = _colorFromHex('#${entry.key.hashCode.toRadixString(16).padLeft(6, '0')}');
                              return Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2),
                                child: Row(
                                  children: [
                                    Container(width: 12, height: 12, decoration: BoxDecoration(color: color, shape: BoxShape.circle)),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(entry.key, style: theme.textTheme.bodySmall)),
                                    Text('₹${entry.value.toStringAsFixed(0)}', style: theme.textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold)),
                                  ],
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
            loading: () => _loadingCard(height: 80),
            error: (e, st) => _errorCard(e),
          ),
          const SizedBox(height: 24),
          // Expenses Progress (Minimalistic)
          transactionsAsync.when(
            data: (transactions) {
              final now = DateTime.now();
              final thisMonth = transactions.where((t) => t.type == 'expense' && t.date.month == now.month && t.date.year == now.year);
              final totalSpent = thisMonth.fold<double>(0, (sum, t) => sum + t.amount);
              return Card(
                elevation: 0,
                color: theme.colorScheme.surfaceContainerHighest,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.trending_down, color: theme.colorScheme.primary),
                          const SizedBox(width: 8),
                          Text('This Month\'s Expenses', style: theme.textTheme.bodyMedium),
                        ],
                      ),
                      const SizedBox(height: 12),
                      LinearProgressIndicator(
                        value: (totalSpent / 100000).clamp(0, 1), // Assume 1L as a sample budget
                        minHeight: 8,
                        backgroundColor: Colors.grey[200],
                        color: theme.colorScheme.primary,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      const SizedBox(height: 8),
                      Text('₹${totalSpent.toStringAsFixed(0)} spent', style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
                    ],
                  ),
                ),
              );
            },
            loading: () => _loadingCard(height: 80),
            error: (e, st) => _errorCard(e),
          ),
          const SizedBox(height: 32),
          // Recent Transactions
          Text(
            'Recent Transactions',
            style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          transactionsAsync.when(
            data: (transactions) {
              final recent = transactions.take(5).toList();
              if (recent.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 32),
                  child: Center(child: Text('No recent transactions', style: theme.textTheme.bodyMedium)),
                );
              }
              return ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: recent.length,
                separatorBuilder: (_, __) => const Divider(height: 1),
                itemBuilder: (context, index) {
                  final tx = recent[index];
                  final isExpense = tx.type == 'expense' || tx.type == 'debt';
                  final color = isExpense ? Colors.red : theme.colorScheme.primary;
                  return Card(
                    elevation: 0,
                    color: theme.colorScheme.surfaceContainerHighest,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: color.withOpacity(0.15),
                        child: Icon(isExpense ? Icons.arrow_upward : Icons.arrow_downward, color: color),
                      ),
                      title: Text(tx.description ?? tx.type, style: theme.textTheme.bodyLarge),
                      subtitle: Text(tx.type),
                      trailing: Text(
                        '${isExpense ? '-' : '+'}₹${tx.amount.toStringAsFixed(0)}',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: color,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              );
            },
            loading: () => _loadingCard(height: 120),
            error: (e, st) => _errorCard(e),
          ),
        ],
      ),
    );
  }

  Widget _iconProgress(BuildContext context, {required IconData icon, required String label, required int value, required Color color}) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color.withOpacity(0.12),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 6),
        Text('$value', style: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold)),
        Text(label, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Colors.grey[600])),
      ],
    );
  }

  Widget _loadingCard({double height = 100}) => Container(
    height: height,
    margin: const EdgeInsets.symmetric(vertical: 8),
    decoration: BoxDecoration(
      color: Colors.grey[100],
      borderRadius: BorderRadius.circular(16),
    ),
  );

  Widget _errorCard(Object e) => Container(
    padding: const EdgeInsets.all(16),
    margin: const EdgeInsets.symmetric(vertical: 8),
    decoration: BoxDecoration(
      color: Colors.red[50],
      borderRadius: BorderRadius.circular(16),
    ),
    child: Text('Error: $e', style: const TextStyle(color: Colors.red)),
  );

  Color _colorFromHex(String hex) {
    hex = hex.replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    return Color(int.parse(hex, radix: 16));
  }
} 
