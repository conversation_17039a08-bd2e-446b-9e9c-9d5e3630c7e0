import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../main_scaffold.dart';
import '../../widgets/universal_scaffold.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _controller = PageController();
  int _page = 0;

  final List<_OnboardingData> _pages = [
    _OnboardingData(
      title: 'Welcome to Financial App',
      description: 'Track your expenses, budgets, savings, debts, and more in one place.',
      image: Icons.account_balance_wallet,
    ),
    _OnboardingData(
      title: 'Powerful Features',
      description: 'Biometric lock, voice input, cloud sync, notifications, and analytics.',
      image: Icons.star,
    ),
    _OnboardingData(
      title: 'Get Started',
      description: "Let's take control of your finances!",
      image: Icons.rocket_launch,
    ),
  ];

  void _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('onboarding_complete', true);
    if (!mounted) return;
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (_) => const MainScaffold()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalScaffold(
      title: 'Welcome',
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _controller,
                itemCount: _pages.length,
                onPageChanged: (i) => setState(() => _page = i),
                itemBuilder: (context, i) => _OnboardingScreen(data: _pages[i]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_pages.length, (i) => Container(
                margin: const EdgeInsets.all(4),
                width: 10, height: 10,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: i == _page ? Theme.of(context).colorScheme.primary : Colors.grey[300],
                ),
              )),
            ),
            Padding(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_page > 0)
                    TextButton(
                      onPressed: () => _controller.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.ease),
                      child: const Text('Back'),
                    ),
                  if (_page < _pages.length - 1)
                    ElevatedButton(
                      onPressed: () => _controller.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.ease),
                      child: const Text('Next'),
                    ),
                  if (_page == _pages.length - 1)
                    ElevatedButton(
                      onPressed: _completeOnboarding,
                      child: const Text('Get Started'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _OnboardingData {
  final String title;
  final String description;
  final IconData image;
  const _OnboardingData({required this.title, required this.description, required this.image});
}

class _OnboardingScreen extends StatelessWidget {
  final _OnboardingData data;
  const _OnboardingScreen({required this.data});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(data.image, size: 120, color: Theme.of(context).colorScheme.primary),
          const SizedBox(height: 32),
          Text(data.title, style: Theme.of(context).textTheme.headlineMedium, textAlign: TextAlign.center),
          const SizedBox(height: 16),
          Text(data.description, style: Theme.of(context).textTheme.bodyLarge, textAlign: TextAlign.center),
        ],
      ),
    );
  }
} 