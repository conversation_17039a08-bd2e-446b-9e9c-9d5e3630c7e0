import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/daily_limit_providers.dart';
import '../../providers/category_providers.dart';
import '../../providers/expense_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../../domain/repositories/transaction_repository.dart';
import '../../widgets/universal_scaffold.dart';

class DailyLimitPage extends ConsumerStatefulWidget {
  const DailyLimitPage({super.key});

  @override
  ConsumerState<DailyLimitPage> createState() => _DailyLimitPageState();
}

class _DailyLimitPageState extends ConsumerState<DailyLimitPage> {
  String _searchQuery = '';
  String _selectedFilter = 'All';
  final List<String> _filterOptions = ['All', 'Active', 'Exceeded', 'Under Limit'];

  @override
  Widget build(BuildContext context) {
    final dailyLimitsAsync = ref.watch(dailyLimitsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);
    final expensesAsync = ref.watch(expensesProvider);

    return UniversalScaffold(
      title: 'Daily Limits',
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'Search daily limits...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () => setState(() => _searchQuery = ''),
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                if (_selectedFilter != 'All')
                  Chip(
                    label: Text(_selectedFilter),
                    onDeleted: () => setState(() => _selectedFilter = 'All'),
                    deleteIcon: const Icon(Icons.close, size: 18),
                  ),
              ],
            ),
          ),
          Expanded(
            child: dailyLimitsAsync.when(
              data: (limits) {
                final filteredLimits = _filterLimits(limits, categoriesAsync, expensesAsync);
                if (filteredLimits.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.timer_outlined, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('No daily limits found', style: TextStyle(fontSize: 18, color: Colors.grey)),
                        SizedBox(height: 8),
                        Text('Add your first daily limit to get started', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  );
                }
                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredLimits.length,
                  itemBuilder: (context, index) {
                    final limit = filteredLimits[index];
                    return _buildDailyLimitCard(limit, categoriesAsync, expensesAsync);
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => Center(child: Text('Error: $e')),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showDailyLimitDialog(context, ref),
        icon: const Icon(Icons.add),
        label: const Text('Add Limit'),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String amount, IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              amount,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDailyLimitCard(
    DailyLimit limit,
    AsyncValue<List<Category>> categoriesAsync,
    AsyncValue<List<TransactionWithCategory>> expensesAsync,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(limit.categoryId, categoriesAsync).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getCategoryIcon(limit.categoryId, categoriesAsync),
                    color: _getCategoryColor(limit.categoryId, categoriesAsync),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getCategoryName(limit.categoryId, categoriesAsync),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Daily Limit: ₹${limit.maxDailyAmount.toStringAsFixed(2)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showDailyLimitDialog(context, ref, limit: limit);
                        break;
                      case 'delete':
                        _deleteDailyLimit(limit);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 20),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            expensesAsync.when(
              data: (expenses) {
                final categoryExpenses = expenses.where((e) => e.transaction.categoryId == limit.categoryId).toList();
                final today = DateTime.now();
                final todayExpenses = categoryExpenses.where((e) {
                  final expenseDate = e.transaction.date;
                  return expenseDate.year == today.year &&
                         expenseDate.month == today.month &&
                         expenseDate.day == today.day;
                }).toList();
                final totalSpent = todayExpenses.fold<double>(0, (sum, e) => sum + e.transaction.amount);
                final remaining = limit.maxDailyAmount - totalSpent;
                final progress = totalSpent / limit.maxDailyAmount;
                final isExceeded = totalSpent > limit.maxDailyAmount;

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Today\'s Spending',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '₹${totalSpent.toStringAsFixed(2)} / ₹${limit.maxDailyAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isExceeded ? Colors.red : Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: progress.clamp(0.0, 1.0),
                      backgroundColor: Colors.grey[300],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isExceeded ? Colors.red : Colors.green,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          isExceeded 
                            ? 'Exceeded by ₹${(totalSpent - limit.maxDailyAmount).toStringAsFixed(2)}'
                            : 'Remaining: ₹${remaining.toStringAsFixed(2)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: isExceeded ? Colors.red : Colors.green,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (todayExpenses.isNotEmpty)
                          Text(
                            '${todayExpenses.length} transactions',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[500],
                            ),
                          ),
                      ],
                    ),
                  ],
                );
              },
              loading: () => const SizedBox(height: 40, child: Center(child: CircularProgressIndicator())),
              error: (e, st) => Text('Error loading expenses: $e'),
            ),
          ],
        ),
      ),
    );
  }

  List<DailyLimit> _filterLimits(
    List<DailyLimit> limits,
    AsyncValue<List<Category>> categoriesAsync,
    AsyncValue<List<TransactionWithCategory>> expensesAsync,
  ) {
    return limits.where((limit) {
      final categoryName = _getCategoryName(limit.categoryId, categoriesAsync);
      final matchesSearch = categoryName.toLowerCase().contains(_searchQuery.toLowerCase());
      
      if (!matchesSearch) return false;
      
      if (_selectedFilter == 'All') return true;
      
      // Calculate today's spending for this limit
      final today = DateTime.now();
      final categoryExpenses = expensesAsync.when(
        data: (expenses) => expenses.where((e) => e.transaction.categoryId == limit.categoryId).toList(),
        loading: () => <TransactionWithCategory>[],
        error: (e, st) => <TransactionWithCategory>[],
      );
      
      final todayExpenses = categoryExpenses.where((e) {
        final expenseDate = e.transaction.date;
        return expenseDate.year == today.year &&
               expenseDate.month == today.month &&
               expenseDate.day == today.day;
      }).toList();
      
      final totalSpent = todayExpenses.fold<double>(0, (sum, e) => sum + e.transaction.amount);
      
      switch (_selectedFilter) {
        case 'Active':
          return totalSpent > 0;
        case 'Exceeded':
          return totalSpent > limit.maxDailyAmount;
        case 'Under Limit':
          return totalSpent > 0 && totalSpent <= limit.maxDailyAmount;
        default:
          return true;
      }
    }).toList();
  }

  void _showDailyLimitDialog(BuildContext context, WidgetRef ref, {DailyLimit? limit}) {
    final amountController = TextEditingController(text: limit?.maxDailyAmount.toString() ?? '');
    int? selectedCategoryId = limit?.categoryId;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(limit == null ? 'Add Daily Limit' : 'Edit Daily Limit'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ref.watch(categoriesProvider).when(
                data: (categories) => DropdownButtonFormField<int>(
                  value: selectedCategoryId,
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                  ),
                  items: categories
                      .where((cat) => cat.type == 'Expense')
                      .map((cat) => DropdownMenuItem(
                        value: cat.id,
                        child: Row(
                          children: [
                            Icon(_getIconData(cat.icon), size: 20),
                            const SizedBox(width: 8),
                            Text(cat.name),
                          ],
                        ),
                      ))
                      .toList(),
                  onChanged: (value) => setState(() => selectedCategoryId = value),
                ),
                loading: () => const CircularProgressIndicator(),
                error: (e, st) => Text('Error: $e'),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: amountController,
                decoration: const InputDecoration(
                  labelText: 'Daily Limit Amount',
                  border: OutlineInputBorder(),
                  prefixText: '₹',
                ),
                keyboardType: TextInputType.number,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (amountController.text.isNotEmpty && selectedCategoryId != null) {
                  final companion = DailyLimitsCompanion(
                    categoryId: drift.Value(selectedCategoryId!),
                    maxDailyAmount: drift.Value(double.tryParse(amountController.text) ?? 0),
                  );
                  
                  if (limit == null) {
                    await ref.read(addDailyLimitUseCaseProvider).call(companion);
                  } else {
                    await ref.read(updateDailyLimitUseCaseProvider).call(limit.id, companion);
                  }
                  
                  ref.refresh(dailyLimitsProvider);
                  Navigator.pop(context);
                }
              },
              child: Text(limit == null ? 'Add' : 'Update'),
            ),
          ],
        ),
      ),
    );
  }

  void _deleteDailyLimit(DailyLimit limit) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Daily Limit'),
        content: Text('Are you sure you want to delete this daily limit?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteDailyLimitUseCaseProvider).call(limit.id);
              ref.refresh(dailyLimitsProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _getCategoryName(int categoryId, AsyncValue<List<Category>> categoriesAsync) {
    return categoriesAsync.when(
      data: (categories) {
        final category = categories.firstWhere(
          (cat) => cat.id == categoryId,
          orElse: () => Category(id: 0, name: 'Unknown', type: 'Expense', icon: 'category', color: '#FF6B6B'),
        );
        return category.name;
      },
      loading: () => 'Loading...',
      error: (e, st) => 'Error',
    );
  }

  Color _getCategoryColor(int categoryId, AsyncValue<List<Category>> categoriesAsync) {
    return categoriesAsync.when(
      data: (categories) {
        final category = categories.firstWhere(
          (cat) => cat.id == categoryId,
          orElse: () => Category(id: 0, name: 'Unknown', type: 'Expense', icon: 'category', color: '#FF6B6B'),
        );
        return _parseColor(category.color);
      },
      loading: () => Colors.grey,
      error: (e, st) => Colors.grey,
    );
  }

  IconData _getCategoryIcon(int categoryId, AsyncValue<List<Category>> categoriesAsync) {
    return categoriesAsync.when(
      data: (categories) {
        final category = categories.firstWhere(
          (cat) => cat.id == categoryId,
          orElse: () => Category(id: 0, name: 'Unknown', type: 'Expense', icon: 'category', color: '#FF6B6B'),
        );
        return _getIconData(category.icon);
      },
      loading: () => Icons.category,
      error: (e, st) => Icons.category,
    );
  }

  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceAll('#', '0xFF')));
    } catch (e) {
      return Colors.grey;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'directions_car': return Icons.directions_car;
      case 'shopping_bag': return Icons.shopping_bag;
      case 'movie': return Icons.movie;
      case 'local_hospital': return Icons.local_hospital;
      case 'account_balance_wallet': return Icons.account_balance_wallet;
      case 'work': return Icons.work;
      case 'trending_up': return Icons.trending_up;
      default: return Icons.category;
    }
  }
} 