import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/loan_providers.dart';
import '../../providers/payment_source_providers.dart';
import '../../../core/database/app_database.dart';
import '../../../domain/repositories/loan_repository.dart';
import '../../../domain/models/loan.dart';
import '../../../domain/models/payment_source.dart';
import 'package:drift/drift.dart' as drift;
import '../../widgets/universal_scaffold.dart';

final emiScheduleRepositoryProvider = Provider.autoDispose((ref) => null);
final calculateEmiUseCaseProvider = Provider.autoDispose((ref) => null);

class LoanPlannerPage extends ConsumerWidget {
  const LoanPlannerPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loansAsync = ref.watch(unifiedLoansProvider);

    return UniversalScaffold(
      title: 'Loan Planner',
      body: loansAsync.when(
        data: (loans) => _LoanList(loans: loans),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, st) => Center(child: Text('Error: $e')),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddLoanDialog(context, ref),
        icon: const Icon(Icons.add),
        label: const Text('Add Loan'),
      ),
    );
  }
}

class _LoanList extends StatefulWidget {
  final List<Debt> loans;
  const _LoanList({required this.loans});

  @override
  State<_LoanList> createState() => _LoanListState();
}

class _LoanListState extends State<_LoanList> {
  String filterType = 'All';
  String sortBy = 'Due Date';

  @override
  Widget build(BuildContext context) {
    final filtered = filterType == 'All'
        ? widget.loans
        : widget.loans.where((l) => l.type == filterType).toList();
    final sorted = [...filtered]..sort((a, b) {
      switch (sortBy) {
        case 'Amount':
          return b.currentBalance.compareTo(a.currentBalance);
        case 'Due Date':
        default:
          return a.dueDate.compareTo(b.dueDate);
      }
    });
    final totalOutstanding = sorted.fold<double>(0, (sum, l) => sum + l.currentBalance);
    final activeLoans = sorted.where((l) => l.isActive).length;
    final overdueLoans = 0;
    final nextDue = sorted.isNotEmpty ? sorted.first.dueDate : null;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Total Outstanding', style: Theme.of(context).textTheme.labelLarge),
                  Text('₹${totalOutstanding.toStringAsFixed(2)}', style: Theme.of(context).textTheme.headlineSmall),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text('Active Loans: $activeLoans'),
                  Text('Overdue: $overdueLoans', style: TextStyle(color: overdueLoans > 0 ? Colors.red : Colors.green)),
                  if (nextDue != null)
                    Text('Next Due: ${nextDue.toLocal().toString().split(' ')[0]}'),
                ],
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              DropdownButton<String>(
                value: filterType,
                items: ['All', ...{...widget.loans.map((l) => l.type)}]
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) => setState(() => filterType = val ?? 'All'),
                hint: const Text('Filter by Type'),
              ),
              const SizedBox(width: 16),
              DropdownButton<String>(
                value: sortBy,
                items: ['Due Date', 'Amount']
                    .map((s) => DropdownMenuItem(value: s, child: Text('Sort by $s')))
                    .toList(),
                onChanged: (val) => setState(() => sortBy = val ?? 'Due Date'),
                hint: const Text('Sort by'),
              ),
            ],
          ),
        ),
        const Divider(),
        Expanded(
          child: ListView.separated(
            itemCount: sorted.length,
            separatorBuilder: (_, __) => const Divider(),
            itemBuilder: (context, i) {
              final loan = sorted[i];
              return ListTile(
                title: Text('${loan.loanName} (${loan.type})'),
                subtitle: Text('Party: ${loan.partyName} | Due: ${loan.dueDate.toLocal().toString().split(' ')[0]}'),
                trailing: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('₹${loan.currentBalance.toStringAsFixed(2)}', style: const TextStyle(fontWeight: FontWeight.bold)),
                  ],
                ),
                onTap: () => _showLoanDetailsDialog(context, loan),
              );
            },
          ),
        ),
      ],
    );
  }
}

void _showLoanDetailsDialog(BuildContext context, Debt loan, {WidgetRef? ref}) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('${loan.loanName} Details'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${loan.type}'),
            Text('Party: ${loan.partyName}'),
            Text('Principal: ₹${loan.principalAmount.toStringAsFixed(2)}'),
            Text('Current Balance: ₹${loan.currentBalance.toStringAsFixed(2)}'),
            Text('Interest: ${loan.interestRate}% (${loan.interestType}, ${loan.interestFrequency})'),
            Text('Start: ${loan.startDate.toLocal().toString().split(' ')[0]}'),
            Text('Due: ${loan.dueDate.toLocal().toString().split(' ')[0]}'),
            Text('EMI: ${loan.isEmi ? 'Yes' : 'No'}'),
            if (loan.isEmi && loan.monthlyEmi != null)
              Text('Monthly EMI: ₹${loan.monthlyEmi!.toStringAsFixed(2)}'),
            if (loan.loanPurpose != null) Text('Purpose: ${loan.loanPurpose}'),
            if (loan.collateral != null) Text('Collateral: ${loan.collateral}'),
            const SizedBox(height: 16),
            Text('Status: ${loan.isActive ? 'Active' : 'Closed'}', style: TextStyle(color: loan.isActive ? Colors.green : Colors.grey)),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
        if (loan.isActive) ...[
          TextButton(
            onPressed: () {
              if (ref != null) _showMakePaymentDialog(context, ref, loan);
            },
            child: const Text('Make Payment'),
          ),
          if (loan.isEmi)
            TextButton(
              onPressed: () {
                if (ref != null) _showAddEmiDialog(context, ref, loan);
              },
              child: const Text('Add EMI'),
            ),
          TextButton(
            onPressed: () {
              if (ref != null) _showAddLoanDialog(context, ref, loan: loan);
            },
            child: const Text('Edit'),
          ),
          TextButton(
            onPressed: () async {
              if (ref != null) {
                await ref.read(loanRepositoryProvider).updateLoan(
                  loan.id, DebtsCompanion(id: drift.Value(loan.id), isActive: drift.Value(false)),
                );
                ref.refresh(unifiedLoansProvider);
                Navigator.pop(context);
              }
            },
            child: const Text('Mark as Closed'),
          ),
          TextButton(
            onPressed: () async {
              if (ref != null) {
                await ref.read(loanRepositoryProvider).deleteLoan(loan.id);
                ref.refresh(unifiedLoansProvider);
                Navigator.pop(context);
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ],
    ),
  );
}

void _showMakePaymentDialog(BuildContext context, WidgetRef ref, Debt loan) {
  final formKey = GlobalKey<FormState>();
  final amountController = TextEditingController();
  final noteController = TextEditingController();
  DateTime paymentDate = DateTime.now();
  int? selectedSourceId;
  PaymentSourceType? selectedSourceType;

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Make Payment'),
      content: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: amountController,
                decoration: const InputDecoration(labelText: 'Amount'),
                keyboardType: TextInputType.number,
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: noteController,
                decoration: const InputDecoration(labelText: 'Notes'),
              ),
              TextButton(
                onPressed: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: paymentDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (picked != null) paymentDate = picked;
                },
                child: Text('Date: ${paymentDate.toLocal().toString().split(' ')[0]}'),
              ),
              FutureBuilder(
                future: ref.read(paymentSourcesProvider.future),
                builder: (context, snapshot) {
                  if (!snapshot.hasData) return const CircularProgressIndicator();
                  final sources = snapshot.data as List<PaymentSource>;
                  return DropdownButtonFormField<int>(
                    value: selectedSourceId,
                    items: sources
                        .map((s) => DropdownMenuItem(
                              value: s.sourceId,
                              child: Text('${s.name} (${s.type.name})'),
                            ))
                        .toList(),
                    onChanged: (val) {
                      selectedSourceId = val;
                      selectedSourceType = sources.firstWhere((s) => s.sourceId == val).type;
                    },
                    decoration: const InputDecoration(labelText: 'Payment Source'),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (formKey.currentState?.validate() ?? false && selectedSourceId != null) {
              final paymentCompanion = TransactionsCompanion(
                amount: drift.Value(double.tryParse(amountController.text) ?? 0),
                date: drift.Value(DateTime.now()),
                type: drift.Value('repayment'),
                fromAccountId: drift.Value(selectedSourceId),
                toAccountId: const drift.Value(null),
                contactId: const drift.Value(null),
                note: drift.Value(noteController.text),
              );
              // TODO: Implement payment logic for loans using Transactions table or appropriate repository
              ref.refresh(unifiedLoansProvider);
              Navigator.pop(context);
            }
          },
          child: const Text('Pay'),
        ),
      ],
    ),
  );
}

void _showAddEmiDialog(BuildContext context, WidgetRef ref, Debt loan) {
  final formKey = GlobalKey<FormState>();
  final monthsController = TextEditingController();
  final interestController = TextEditingController(text: loan.interestRate.toString());
  final feeController = TextEditingController();
  final gstController = TextEditingController();
  DateTime startDate = DateTime.now();

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('Add EMI Schedule'),
      content: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: monthsController,
                decoration: const InputDecoration(labelText: 'Months'),
                keyboardType: TextInputType.number,
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: interestController,
                decoration: const InputDecoration(labelText: 'Interest Rate'),
                keyboardType: TextInputType.number,
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: feeController,
                decoration: const InputDecoration(labelText: 'Processing Fee'),
                keyboardType: TextInputType.number,
              ),
              TextFormField(
                controller: gstController,
                decoration: const InputDecoration(labelText: 'GST Amount'),
                keyboardType: TextInputType.number,
              ),
              TextButton(
                onPressed: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: startDate,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (picked != null) startDate = picked;
                },
                child: Text('Start: ${startDate.toLocal().toString().split(' ')[0]}'),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (formKey.currentState?.validate() ?? false) {
              final emiCompanion = EmiSchedulesCompanion(
                debtId: drift.Value(loan.id),
                monthlyPayment: drift.Value(loan.monthlyEmi ?? 0),
                emiNumber: drift.Value(1), // Should be incremented for each EMI
                totalEmis: drift.Value(int.tryParse(monthsController.text) ?? 0),
                remainingEmis: drift.Value(int.tryParse(monthsController.text) ?? 0),
                dueDate: drift.Value(startDate),
                principalComponent: drift.Value(loan.principalAmount),
                interestComponent: drift.Value(double.tryParse(interestController.text) ?? 0),
                processingFee: drift.Value(double.tryParse(feeController.text)),
                gstAmount: drift.Value(double.tryParse(gstController.text)),
                isPaid: drift.Value(false),
                paidDate: const drift.Value(null),
                paymentMethod: const drift.Value(null),
                fromAccountId: const drift.Value(0),
              );
              ref.refresh(unifiedLoansProvider);
              Navigator.pop(context);
            }
          },
          child: const Text('Add EMI'),
        ),
      ],
    ),
  );
}

void _showAddLoanDialog(BuildContext context, WidgetRef ref, {Debt? loan}) {
  final formKey = GlobalKey<FormState>();
  final typeController = TextEditingController(text: loan?.type ?? 'Personal');
  final nameController = TextEditingController(text: loan?.loanName ?? '');
  final partyController = TextEditingController(text: loan?.partyName ?? '');
  final principalController = TextEditingController(text: loan?.principalAmount.toString() ?? '');
  final interestController = TextEditingController(text: loan?.interestRate.toString() ?? '');
  final interestTypeController = TextEditingController(text: loan?.interestType ?? 'Simple');
  final interestFreqController = TextEditingController(text: loan?.interestFrequency ?? 'Monthly');
  final emiMonthsController = TextEditingController(text: loan?.emiMonths?.toString() ?? '');
  final monthlyEmiController = TextEditingController(text: loan?.monthlyEmi?.toString() ?? '');
  final purposeController = TextEditingController(text: loan?.loanPurpose ?? '');
  final collateralController = TextEditingController(text: loan?.collateral ?? '');
  DateTime startDate = loan?.startDate ?? DateTime.now();
  DateTime dueDate = loan?.dueDate ?? DateTime.now().add(const Duration(days: 365));
  bool isEmi = loan?.isEmi ?? false;
  bool isActive = loan?.isActive ?? true;

  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(loan == null ? 'Add Loan' : 'Edit Loan'),
      content: SingleChildScrollView(
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: typeController.text,
                items: ['Personal', 'Gold', 'Car', 'Home', 'Business', 'Credit Card']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) => typeController.text = val ?? 'Personal',
                decoration: const InputDecoration(labelText: 'Loan Type'),
              ),
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Loan Name'),
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: partyController,
                decoration: const InputDecoration(labelText: 'Party Name'),
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: principalController,
                decoration: const InputDecoration(labelText: 'Principal Amount'),
                keyboardType: TextInputType.number,
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              TextFormField(
                controller: interestController,
                decoration: const InputDecoration(labelText: 'Interest Rate (%)'),
                keyboardType: TextInputType.number,
                validator: (v) => v == null || v.isEmpty ? 'Required' : null,
              ),
              DropdownButtonFormField<String>(
                value: interestTypeController.text,
                items: ['Simple', 'Compound', 'Flat']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) => interestTypeController.text = val ?? 'Simple',
                decoration: const InputDecoration(labelText: 'Interest Type'),
              ),
              DropdownButtonFormField<String>(
                value: interestFreqController.text,
                items: ['Monthly', 'Quarterly', 'Yearly']
                    .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) => interestFreqController.text = val ?? 'Monthly',
                decoration: const InputDecoration(labelText: 'Interest Frequency'),
              ),
              SwitchListTile(
                value: isEmi,
                onChanged: (val) => isEmi = val,
                title: const Text('EMI Enabled'),
              ),
              if (isEmi) ...[
                TextFormField(
                  controller: emiMonthsController,
                  decoration: const InputDecoration(labelText: 'EMI Months'),
                  keyboardType: TextInputType.number,
                  validator: (v) => v == null || v.isEmpty ? 'Required' : null,
                ),
                TextFormField(
                  controller: monthlyEmiController,
                  decoration: const InputDecoration(labelText: 'Monthly EMI (auto-calc)'),
                  keyboardType: TextInputType.number,
                  readOnly: true,
                  onTap: () {
                    // Calculate EMI using use case
                    final principal = double.tryParse(principalController.text) ?? 0;
                    final rate = double.tryParse(interestController.text) ?? 0;
                    final months = int.tryParse(emiMonthsController.text) ?? 0;
                    if (principal > 0 && rate > 0 && months > 0) {
                      monthlyEmiController.text = (principal * rate * months).toStringAsFixed(2);
                    }
                  },
                ),
              ],
              TextFormField(
                controller: purposeController,
                decoration: const InputDecoration(labelText: 'Purpose'),
              ),
              TextFormField(
                controller: collateralController,
                decoration: const InputDecoration(labelText: 'Collateral'),
              ),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: startDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) startDate = picked;
                      },
                      child: Text('Start: ${startDate.toLocal().toString().split(' ')[0]}'),
                    ),
                  ),
                  Expanded(
                    child: TextButton(
                      onPressed: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: dueDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                        );
                        if (picked != null) dueDate = picked;
                      },
                      child: Text('Due: ${dueDate.toLocal().toString().split(' ')[0]}'),
                    ),
                  ),
                ],
              ),
              SwitchListTile(
                value: isActive,
                onChanged: (val) => isActive = val,
                title: const Text('Active'),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () async {
            if (formKey.currentState?.validate() ?? false) {
              final loanCompanion = DebtsCompanion(
                type: drift.Value(typeController.text),
                loanName: drift.Value(nameController.text),
                isLending: drift.Value(false),
                partyName: drift.Value(partyController.text),
                principalAmount: drift.Value(double.tryParse(principalController.text) ?? 0),
                currentBalance: drift.Value(double.tryParse(principalController.text) ?? 0),
                interestRate: drift.Value(double.tryParse(interestController.text) ?? 0),
                interestType: drift.Value(interestTypeController.text),
                interestFrequency: drift.Value(interestFreqController.text),
                startDate: drift.Value(startDate),
                dueDate: drift.Value(dueDate),
                paidAmount: drift.Value(0),
                isEmi: drift.Value(isEmi),
                emiMonths: isEmi ? drift.Value(int.tryParse(emiMonthsController.text) ?? 0) : const drift.Value(null),
                monthlyEmi: isEmi ? drift.Value(double.tryParse(monthlyEmiController.text) ?? 0) : const drift.Value(null),
                loanPurpose: drift.Value(purposeController.text),
                collateral: drift.Value(collateralController.text),
                isActive: drift.Value(isActive),
                createdAt: drift.Value(DateTime.now()),
                lastUpdated: drift.Value(DateTime.now()),
              );
              if (loan == null) {
                await ref.read(loanRepositoryProvider).addLoan(loanCompanion);
              } else {
                await ref.read(loanRepositoryProvider).updateLoan(
                  loan.id, loanCompanion.copyWith(id: drift.Value(loan.id)),
                );
              }
              ref.refresh(unifiedLoansProvider);
              Navigator.pop(context);
            }
          },
          child: Text(loan == null ? 'Add' : 'Save'),
        ),
      ],
    ),
  );
} 