import 'package:flutter/material.dart';
import 'dashboard/dashboard_page.dart';
import 'transactions/transactions_page.dart';
import 'budget/budget_page.dart';
import 'bank_accounts/bank_accounts_page.dart';
import 'settings/settings_page.dart';
import '../widgets/universal_scaffold.dart';

class MainScaffold extends StatefulWidget {
  const MainScaffold({super.key});

  @override
  State<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends State<MainScaffold> {
  int _index = 0;

  final List<String> _pageTitles = [
    'Dashboard',
    'Transactions',
    'Budgets',
    'Accounts',
    'Settings',
  ];

  final List<Widget> _pages = [
    const DashboardPage(),
    const TransactionsPage(),
    const BudgetPage(),
    const BankAccountsPage(),
    const SettingsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return UniversalScaffold(
      title: _pageTitles[_index],
      body: _pages[_index],
      currentTab: _index,
      onTabChanged: (i) => setState(() => _index = i),
      showBottomNav: true,
    );
  }
} 