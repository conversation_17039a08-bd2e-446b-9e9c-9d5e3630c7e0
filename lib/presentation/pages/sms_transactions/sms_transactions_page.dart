import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
// import '../../providers/sms_service.dart';
import '../../providers/category_providers.dart';
import '../../providers/expense_providers.dart';
import '../../../core/services/sms_parser_service.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
// import '../../providers/sms_providers.dart';
import '../../providers/categories_providers.dart';
import '../../providers/sms_providers.dart';
import '../../../core/services/sms_service.dart' hide smsServiceProvider;
import '../../../core/services/sms_parser_service.dart' as parser;
import '../../widgets/universal_scaffold.dart';

class SmsTransactionsPage extends ConsumerStatefulWidget {
  const SmsTransactionsPage({super.key});

  @override
  ConsumerState<SmsTransactionsPage> createState() => _SmsTransactionsPageState();
}

class _SmsTransactionsPageState extends ConsumerState<SmsTransactionsPage> {
  bool _isLoading = false;
  String? _error;
  List<parser.SmsTransactionData>? _transactions;

  Future<void> _importSmsTransactions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });
    final smsService = ref.read(smsServiceProvider);
    final hasPermission = await smsService.hasSmsPermission();
    if (!hasPermission) {
      final granted = await smsService.requestSmsPermission();
      if (!granted) {
        setState(() {
          _isLoading = false;
        });
        _showPermissionDialog();
        return;
      }
    }
    try {
      final messages = await smsService.getSmsMessages();
      final parsed = <parser.SmsTransactionData>[];
      for (final msg in messages) {
        if (parser.SmsParserService.isUpiTransaction(msg)) {
          final tx = parser.SmsParserService.parseUpiTransaction(msg);
          if (tx != null) {
            parsed.add(tx);
            // Optionally save to DB here if you want
            // await smsService.saveSmsTransaction(tx);
          }
        }
      }
      setState(() {
        _transactions = parsed;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Failed to import SMS transactions: $e';
        _isLoading = false;
      });
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SMS Permission Required'),
        content: const Text('To import transactions from SMS, please grant SMS permission in your device settings.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return UniversalScaffold(
      title: 'SMS Transactions',
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ElevatedButton.icon(
              icon: const Icon(Icons.sms),
              label: const Text('Import SMS Transactions'),
              onPressed: _isLoading ? null : _importSmsTransactions,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
                elevation: 2,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            const SizedBox(height: 24),
            if (_isLoading)
              const Center(child: CircularProgressIndicator()),
            if (_error != null)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: Text(_error!, style: const TextStyle(color: Colors.red)),
              ),
            if (_transactions != null)
              Expanded(
                child: _transactions!.isEmpty
                  ? const Center(child: Text('No SMS transactions found.'))
                  : ListView.separated(
                      itemCount: _transactions!.length,
                      separatorBuilder: (_, __) => const Divider(),
                      itemBuilder: (context, i) {
                        final tx = _transactions![i];
                        return ListTile(
                          leading: const Icon(Icons.receipt_long),
                          title: Text(' ₹${tx.amount} - ${tx.bankName}'),
                          subtitle: Text('${tx.transactionType} • ${tx.vpa ?? ''}'),
                          trailing: Text(tx.transactionDate != null ? DateFormat('dd MMM yyyy').format(tx.transactionDate) : ''),
                        );
                      },
                    ),
              ),
            if (_transactions == null && !_isLoading && _error == null)
              const Center(child: Text('Tap the button above to import transactions from your SMS inbox.')),
          ],
        ),
      ),
    );
  }
} 