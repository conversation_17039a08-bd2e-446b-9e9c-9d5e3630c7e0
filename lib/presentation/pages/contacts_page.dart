import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/expense_providers.dart';
import '../../core/database/app_database.dart';
import 'debts/contact_transaction_detail_page.dart';
import '../widgets/universal_scaffold.dart';

class ContactsPage extends ConsumerStatefulWidget {
  const ContactsPage({super.key});

  @override
  ConsumerState<ContactsPage> createState() => _ContactsPageState();
}

class _ContactsPageState extends ConsumerState<ContactsPage> {
  String _search = '';

  @override
  Widget build(BuildContext context) {
    final contactsAsync = ref.watch(allContactsSummaryProvider);
    return UniversalScaffold(
      title: 'Contacts',
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: TextField(
              decoration: const InputDecoration(
                prefixIcon: Icon(Icons.search),
                hintText: 'Search contacts',
                border: OutlineInputBorder(),
                isDense: true,
              ),
              onChanged: (val) => setState(() => _search = val),
            ),
          ),
          Expanded(
            child: contactsAsync.when(
              data: (contacts) {
                final filtered = contacts.where((c) {
                  final name = c.contactName?.toLowerCase() ?? '';
                  final phone = c.contactPhone ?? '';
                  return _search.isEmpty ||
                    name.contains(_search.toLowerCase()) ||
                    phone.contains(_search);
                }).toList();
                if (filtered.isEmpty) {
                  return const Center(child: Text('No contacts with personal transactions.'));
                }
                return ListView.separated(
                  itemCount: filtered.length,
                  separatorBuilder: (_, __) => const Divider(),
                  itemBuilder: (context, i) {
                    final c = filtered[i];
                    final isOwed = c.netBalance < 0;
                    final balanceColor = isOwed ? Colors.green : Colors.red;
                    return ListTile(
                      leading: const Icon(Icons.person),
                      title: Text(c.contactName ?? 'Unknown'),
                      subtitle: c.contactPhone != null && c.contactPhone!.isNotEmpty
                          ? Text(c.contactPhone!)
                          : null,
                      trailing: Text(
                        '${isOwed ? '' : 'You owe '} ₹${c.netBalance.abs().toStringAsFixed(2)}',
                        style: TextStyle(fontWeight: FontWeight.bold, color: balanceColor),
                      ),
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => ContactTransactionDetailPage(
                              contactId: c.contactId,
                              contactName: c.contactName,
                              contactPhone: c.contactPhone,
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (e, st) => Center(child: Text('Error: $e')),
            ),
          ),
        ],
      ),
    );
  }
} 