import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/budget_providers.dart';
import '../../providers/category_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../../domain/repositories/budget_repository.dart';

class BudgetPage extends ConsumerWidget {
  const BudgetPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final budgetsAsync = ref.watch(budgetsProvider);
    final categoriesAsync = ref.watch(categoriesProvider);

    return budgetsAsync.when(
      data: (budgets) => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildSummarySection(context, budgets),
            const SizedBox(height: 24),
            _buildBudgetsList(context, ref, budgets, categoriesAsync),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (e, st) => Center(child: Text('Error: $e')),
    );
  }

  Widget _buildSummarySection(BuildContext context, List<BudgetWithCategory> budgets) {
    final totalBudgeted = budgets.fold<double>(0, (sum, b) => sum + b.budget.budgetedAmount);
    final totalSpent = budgets.fold<double>(0, (sum, b) => sum + b.budget.actualSpent);
    final totalRemaining = totalBudgeted - totalSpent;
    final budgetUsage = totalBudgeted > 0 ? (totalSpent / totalBudgeted) * 100 : 0.0;
    final isOverBudget = totalSpent > totalBudgeted;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Budget Management',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        
        // Main Budget Card
        Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                colors: [
                  isOverBudget ? Colors.red : Theme.of(context).colorScheme.primary,
                  isOverBudget ? Colors.red.withOpacity(0.8) : Theme.of(context).colorScheme.primary.withOpacity(0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 25,
                      backgroundColor: Colors.white.withOpacity(0.2),
                      child: Icon(
                        isOverBudget ? Icons.warning : Icons.pie_chart,
                        color: Colors.white,
                        size: 30,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isOverBudget ? 'Over Budget!' : 'Budget Status',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.white.withOpacity(0.9),
                            ),
                          ),
                          Text(
                            '₹${totalSpent.toStringAsFixed(0)} of ₹${totalBudgeted.toStringAsFixed(0)}',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Progress Bar
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Usage',
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          '${budgetUsage.toStringAsFixed(1)}%',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: budgetUsage / 100,
                      backgroundColor: Colors.white.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        isOverBudget ? Colors.orange : Colors.white,
                      ),
                      minHeight: 8,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Budget Stats
                Row(
                  children: [
                    Expanded(
                      child: _buildBudgetStat(
                        'Remaining',
                        '₹${totalRemaining.toStringAsFixed(0)}',
                        Icons.account_balance_wallet,
                      ),
                    ),
                    Expanded(
                      child: _buildBudgetStat(
                        'Categories',
                        '${budgets.length}',
                        Icons.category,
                      ),
                    ),
                    Expanded(
                      child: _buildBudgetStat(
                        'Status',
                        isOverBudget ? 'Over' : 'Good',
                        isOverBudget ? Icons.warning : Icons.check_circle,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildBudgetsList(BuildContext context, WidgetRef ref, List<BudgetWithCategory> budgets, AsyncValue<List<Category>> categoriesAsync) {
    if (budgets.isEmpty) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            children: [
              Icon(
                Icons.pie_chart,
                size: 64,
                color: Theme.of(context).colorScheme.outline,
              ),
              const SizedBox(height: 16),
              Text(
                'No budgets yet',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Create your first budget to start tracking expenses',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.outline,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Budgets',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        ...budgets.map((budget) => _buildBudgetCard(context, ref, budget, categoriesAsync)),
      ],
    );
  }

  Widget _buildBudgetCard(BuildContext context, WidgetRef ref, BudgetWithCategory budget, AsyncValue<List<Category>> categoriesAsync) {
    final progress = budget.budget.budgetedAmount > 0 
      ? budget.budget.actualSpent / budget.budget.budgetedAmount 
      : 0.0;
    final progressPercentage = (progress * 100).clamp(0.0, 100.0);
    final remaining = budget.budget.budgetedAmount - budget.budget.actualSpent;
    final isOverBudget = budget.budget.actualSpent > budget.budget.budgetedAmount;
    final budgetColor = isOverBudget ? Colors.red : Colors.green;
    
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: budgetColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isOverBudget ? Icons.warning : Icons.pie_chart,
                    color: budgetColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        budget.category.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${budget.budget.month}/${budget.budget.year}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${budget.budget.actualSpent.toStringAsFixed(0)} of ₹${budget.budget.budgetedAmount.toStringAsFixed(0)}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: budgetColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${progressPercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: budgetColor,
                      ),
                    ),
                    Text(
                      isOverBudget ? 'Over' : 'Used',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // Progress Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                    Text(
                      '₹${remaining.toStringAsFixed(0)} remaining',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.outline,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress.clamp(0.0, 1.0),
                  backgroundColor: budgetColor.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(budgetColor),
                  minHeight: 6,
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text('Edit'),
                  onPressed: () => _showBudgetDialog(context, ref, categoriesAsync, budget: budget),
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 18),
                  label: const Text('Delete'),
                  style: TextButton.styleFrom(foregroundColor: Colors.red),
                  onPressed: () => _showDeleteDialog(context, ref, budget),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, BudgetWithCategory budget) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Budget'),
        content: Text('Are you sure you want to delete the budget for ${budget.category.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              await ref.read(deleteBudgetUseCaseProvider).call(budget.budget.id);
              ref.refresh(budgetsProvider);
              Navigator.pop(context);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showBudgetDialog(BuildContext context, WidgetRef ref, AsyncValue<List<Category>> categoriesAsync, {BudgetWithCategory? budget}) {
    final budgetedController = TextEditingController(text: budget?.budget.budgetedAmount.toString() ?? '');
    final actualController = TextEditingController(text: budget?.budget.actualSpent.toString() ?? '');
    int? selectedCategoryId = budget?.category.id;
    int selectedMonth = budget?.budget.month ?? DateTime.now().month;
    int selectedYear = budget?.budget.year ?? DateTime.now().year;
    
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(budget == null ? 'Add Budget' : 'Edit Budget'),
          content: categoriesAsync.when(
            data: (categories) => SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<int>(
                    value: selectedCategoryId,
                    items: categories
                        .map((cat) => DropdownMenuItem(
                              value: cat.id,
                              child: Text(cat.name),
                            ))
                        .toList(),
                    onChanged: (val) => selectedCategoryId = val,
                    decoration: const InputDecoration(
                      labelText: 'Category',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: budgetedController,
                          decoration: const InputDecoration(
                            labelText: 'Budgeted Amount',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          controller: actualController,
                          decoration: const InputDecoration(
                            labelText: 'Actual Spent',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<int>(
                          value: selectedMonth,
                          items: List.generate(12, (i) => i + 1)
                              .map((m) => DropdownMenuItem(value: m, child: Text('Month $m')))
                              .toList(),
                          onChanged: (val) => selectedMonth = val ?? selectedMonth,
                          decoration: const InputDecoration(
                            labelText: 'Month',
                            border: OutlineInputBorder(),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: TextField(
                          decoration: const InputDecoration(
                            labelText: 'Year',
                            border: OutlineInputBorder(),
                          ),
                          controller: TextEditingController(text: selectedYear.toString()),
                          onChanged: (val) => selectedYear = int.tryParse(val) ?? selectedYear,
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (e, st) => Text('Error: $e'),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedCategoryId != null && budgetedController.text.isNotEmpty) {
                  final companion = BudgetsCompanion(
                    categoryId: drift.Value(selectedCategoryId!),
                    budgetedAmount: drift.Value(double.tryParse(budgetedController.text) ?? 0),
                    actualSpent: drift.Value(double.tryParse(actualController.text) ?? 0),
                    month: drift.Value(selectedMonth),
                    year: drift.Value(selectedYear),
                  );
                  if (budget == null) {
                    await ref.read(addBudgetUseCaseProvider).call(companion);
                  } else {
                    await ref.read(updateBudgetUseCaseProvider).call(budget.budget.id, companion);
                  }
                  ref.refresh(budgetsProvider);
                  Navigator.pop(context);
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }
} 