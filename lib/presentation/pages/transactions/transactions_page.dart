import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../providers/transaction_providers.dart';
import '../../providers/category_providers.dart';
import '../../providers/bank_account_providers.dart';
import '../../providers/credit_card_providers.dart';
import '../../providers/loan_providers.dart';
import '../../widgets/transaction_list.dart';
import '../../widgets/transaction_form.dart';
import '../../widgets/form_components.dart';
// The following BLoC files exist in lib/presentation/bloc and the imports are correct.
import '../../bloc/transaction_add_bloc.dart';
import '../../bloc/transaction_add_event.dart';
import '../../bloc/transaction_add_state.dart';

class TransactionsPage extends ConsumerStatefulWidget {
  const TransactionsPage({super.key});

  @override
  ConsumerState<TransactionsPage> createState() => _TransactionsPageState();
}

class _TransactionsPageState extends ConsumerState<TransactionsPage> {
  static const int _pageSize = 20;
  final ScrollController _scrollController = ScrollController();
  List transactions = [];
  bool isLoading = false;
  bool hasMore = true;
  int offset = 0;

  // Filter state
  final TextEditingController _searchController = TextEditingController();
  String? _selectedTypeFilter;
  final List<String> _typeOptions = [
    'All',
    'Expense',
    'Income',
    'Transfer',
    'Credit Given',
    'Credit Received',
  ];

  // Advanced filter state
  DateTime? _fromDate;
  DateTime? _toDate;
  String? _selectedBankType;
  RangeValues? _priceRange;

  Set<String> _selectedTypes = {};
  Set<String> _selectedCategories = {};
  Set<String> _selectedBanks = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    WidgetsBinding.instance.addPostFrameCallback((_) => _loadMore());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 && !isLoading && hasMore) {
      _loadMore();
    }
  }

  void _loadMore() async {
    if (isLoading || !hasMore) return;
    setState(() => isLoading = true);
    final provider = ref.read(paginatedTransactionsProvider({'limit': _pageSize, 'offset': offset}).future);
    final newItems = await provider;
    setState(() {
      transactions.addAll(newItems);
      offset += newItems.length;
      isLoading = false;
      if (newItems.length < _pageSize) hasMore = false;
    });
  }

  void _onTypeFilterChanged(String? value) {
    setState(() {
      _selectedTypeFilter = value;
    });
  }

  void _onSearchChanged(String value) {
    setState(() {});
  }

  List get _filteredTransactions {
    final search = _searchController.text.toLowerCase();
    return transactions.where((tx) {
      final matchesType = _selectedTypes.isEmpty || _selectedTypes.contains(tx.transaction.type);
      final matchesSearch = search.isEmpty
        || (tx.transaction.description?.toLowerCase().contains(search) ?? false)
        || (tx.category?.name?.toLowerCase().contains(search) ?? false);
      final matchesDate = (_fromDate == null || tx.transaction.date.isAfter(_fromDate!)) &&
          (_toDate == null || tx.transaction.date.isBefore(_toDate!));
      final matchesBank = _selectedBanks.isEmpty ||
        _selectedBanks.contains(tx.fromAccount?.bankName) || _selectedBanks.contains(tx.toAccount?.bankName);
      final matchesCategory = _selectedCategories.isEmpty ||
        (tx.category != null && _selectedCategories.contains(tx.category.name));
      final matchesPrice = _priceRange == null ||
        (tx.transaction.amount >= _priceRange!.start && tx.transaction.amount <= _priceRange!.end);
      return matchesType && matchesSearch && matchesDate && matchesBank && matchesCategory && matchesPrice;
    }).toList();
  }

  void _showAdvancedFilters(BuildContext context) {
    final categoriesAsync = ref.watch(categoriesProvider);
    final banksAsync = ref.watch(bankAccountsProvider);
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        // Always initialize from latest parent state when modal is opened
        DateTime? tempFrom = _fromDate;
        DateTime? tempTo = _toDate;
        RangeValues tempPrice = _priceRange != null ? RangeValues(_priceRange!.start, _priceRange!.end) : const RangeValues(0, 10000);
        Set<String> tempTypes = Set<String>.from(_selectedTypes);
        Set<String> tempCategories = Set<String>.from(_selectedCategories);
        Set<String> tempBanks = Set<String>.from(_selectedBanks);
        final minController = TextEditingController(text: tempPrice.start.toStringAsFixed(0));
        final maxController = TextEditingController(text: tempPrice.end.toStringAsFixed(0));
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              child: SingleChildScrollView(
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Advanced Filters', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 16),
                      // Multi-select types
                      const Text('Transaction Types'),
                      Wrap(
                        spacing: 8,
                        children: _typeOptions.where((t) => t != 'All').map((type) {
                          // Use the raw type string for filter matching
                          final rawType = type == 'Expense' ? 'expense'
                            : type == 'Income' ? 'income'
                            : type == 'Transfer' ? 'transfer'
                            : type == 'Credit Given' ? 'credit_given'
                            : type == 'Credit Received' ? 'credit_received'
                            : type.toLowerCase();
                          return FilterChip(
                            label: Text(type),
                            selected: tempTypes.contains(rawType),
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  tempTypes.add(rawType);
                                } else {
                                  tempTypes.remove(rawType);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                      const SizedBox(height: 16),
                      // Multi-select categories
                      const Text('Categories'),
                      categoriesAsync.when(
                        data: (categories) => Wrap(
                          spacing: 8,
                          children: categories.map((cat) => FilterChip(
                            label: Text(cat.name),
                            selected: tempCategories.contains(cat.name),
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  tempCategories.add(cat.name);
                                } else {
                                  tempCategories.remove(cat.name);
                                }
                              });
                            },
                          )).toList(),
                        ),
                        loading: () => const CircularProgressIndicator(),
                        error: (e, st) => Text('Error loading categories'),
                      ),
                      const SizedBox(height: 16),
                      // Multi-select banks
                      const Text('Banks'),
                      banksAsync.when(
                        data: (banks) => Wrap(
                          spacing: 8,
                          children: banks.map((bank) => FilterChip(
                            label: Text(bank.bankName),
                            selected: tempBanks.contains(bank.bankName),
                            onSelected: (selected) {
                              setState(() {
                                if (selected) {
                                  tempBanks.add(bank.bankName);
                                } else {
                                  tempBanks.remove(bank.bankName);
                                }
                              });
                            },
                          )).toList(),
                        ),
                        loading: () => const CircularProgressIndicator(),
                        error: (e, st) => Text('Error loading banks'),
                      ),
                      const SizedBox(height: 16),
                      // Date pickers in a row (now minimal, won't overflow)
                      Row(
                        children: [
                          Expanded(
                            child: DatePickerField(
                              label: 'From',
                              selectedDate: tempFrom,
                              onDateChanged: (d) => setState(() => tempFrom = d),
                              allowNull: true,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: DatePickerField(
                              label: 'To',
                              selectedDate: tempTo,
                              onDateChanged: (d) => setState(() => tempTo = d),
                              allowNull: true,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      const Text('Price Range'),
                      const SizedBox(height: 8),
                      // Manual price entry fields
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'Min',
                                isDense: true,
                                border: OutlineInputBorder(),
                              ),
                              controller: minController,
                              onChanged: (val) {
                                final v = double.tryParse(val) ?? 0;
                                setState(() {
                                  tempPrice = RangeValues(v, tempPrice.end < v ? v : tempPrice.end);
                                  minController.text = v.toStringAsFixed(0);
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: TextField(
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'Max',
                                isDense: true,
                                border: OutlineInputBorder(),
                              ),
                              controller: maxController,
                              onChanged: (val) {
                                final v = double.tryParse(val) ?? tempPrice.start;
                                setState(() {
                                  double newEnd = v;
                                  double newStart = tempPrice.start;
                                  if (newEnd < newStart) {
                                    newStart = newEnd;
                                  }
                                  tempPrice = RangeValues(newStart, newEnd);
                                  maxController.text = v.toStringAsFixed(0);
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      RangeSlider(
                        values: tempPrice,
                        min: 0,
                        max: 100000,
                        divisions: 100,
                        labels: RangeLabels('₹${tempPrice.start.toStringAsFixed(0)}', '₹${tempPrice.end.toStringAsFixed(0)}'),
                        onChanged: (v) => setState(() => tempPrice = v),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                // Delay parent setState to after modal closes
                                Future.delayed(const Duration(milliseconds: 10), () {
                                  if (mounted) {
                                    setState(() {
                                      _fromDate = tempFrom;
                                      _toDate = tempTo;
                                      _priceRange = tempPrice;
                                      _selectedTypes = Set<String>.from(tempTypes);
                                      _selectedCategories = Set<String>.from(tempCategories);
                                      _selectedBanks = Set<String>.from(tempBanks);
                                      // Reset and reload transactions
                                      offset = 0;
                                      transactions.clear();
                                      hasMore = true;
                                      _loadMore();
                                    });
                                  }
                                });
                              },
                              child: const Text('Apply'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                Future.delayed(const Duration(milliseconds: 10), () {
                                  if (mounted) {
                                    setState(() {
                                      _fromDate = null;
                                      _toDate = null;
                                      _priceRange = null;
                                      _selectedTypes.clear();
                                      _selectedCategories.clear();
                                      _selectedBanks.clear();
                                      // Reset and reload transactions
                                      offset = 0;
                                      transactions.clear();
                                      hasMore = true;
                                      _loadMore();
                                    });
                                  }
                                });
                              },
                              child: const Text('Clear'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final totalBalanceAsync = ref.watch(totalBalanceProvider);
    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: _TransactionAccordionInsightCard(transactions: _filteredTransactions),
          ),
          // Filter/Search Bar
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: SearchFilterBar(
              searchController: _searchController,
              searchHint: 'Search transactions...',
              selectedFilter: _selectedTypeFilter ?? 'All',
              filterOptions: _typeOptions,
              onFilterChanged: _onTypeFilterChanged,
              onSearchChanged: _onSearchChanged,
            ),
          ),
          Expanded(
            child: TransactionList(
              transactions: _filteredTransactions,
              controller: _scrollController,
              isLoading: isLoading,
              hasMore: hasMore,
              showDateHeader: true, // Always show timeline grouping
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.tune),
                label: const Text('More Filters'),
                onPressed: () => _showAdvancedFilters(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                  foregroundColor: Theme.of(context).colorScheme.onSurface,
                  elevation: 0,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.add_circle, color: Colors.white),
              label: const Text('Add Transaction', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
                elevation: 2,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
              ),
              onPressed: () => _showTransactionForm(context),
            ),
          ),
        ),
      ),
    );
  }

  void _showTransactionForm(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        String expenseSourceType = 'bank'; // 'bank', 'card', 'loan'
        String otherType = 'transfer'; // 'transfer', 'credit_given', 'credit_received'
        return BlocProvider(
          create: (_) => TransactionAddBloc(ref)..add(TransactionTypeChanged('expense')),
          child: StatefulBuilder(
            builder: (context, setModalState) {
              final categoryFocus = FocusNode();
              final fromAccountFocus = FocusNode();
              final toAccountFocus = FocusNode();
              final amountFocus = FocusNode();
              final dateFocus = FocusNode();
              final descriptionFocus = FocusNode();
              final contactFocus = FocusNode();
              return Consumer(
                builder: (context, ref, _) {
                  final categoriesAsync = ref.watch(categoriesProvider);
                  final accountsAsync = ref.watch(bankAccountsProvider);
                  final cardsAsync = ref.watch(creditCardsProvider);
                  final loansAsync = ref.watch(unifiedLoansProvider);
                  // Compute most used category for the current type
                  int? getMostUsedCategoryId(String type, List cats) {
                    final filtered = transactions.where((tx) => tx.transaction.type.toLowerCase() == type.toLowerCase()).toList();
                    if (filtered.isEmpty) return null;
                    final freq = <int, int>{};
                    for (final tx in filtered) {
                      final id = tx.category?.id;
                      if (id != null) freq[id] = (freq[id] ?? 0) + 1;
                    }
                    if (freq.isEmpty) return null;
                    return freq.entries.reduce((a, b) => a.value >= b.value ? a : b).key;
                  }
                  return BlocConsumer<TransactionAddBloc, TransactionAddState>(
                    listener: (context, state) {
                      if (state.isSuccess) {
                        Navigator.of(context).pop();
                        Future.delayed(const Duration(milliseconds: 300), () {
                          if (mounted) {
                            setState(() {
                              offset = 0;
                              transactions.clear();
                              hasMore = true;
                              _loadMore();
                            });
                          }
                        });
                      }
                    },
                    builder: (context, state) {
                      final tabIndex = state.type == 'expense' ? 0 : state.type == 'income' ? 1 : 2;
                      String? otherType = (state.type != 'expense' && state.type != 'income') ? state.type : null;
                      return DefaultTabController(
                        length: 3,
                        initialIndex: 0, // Always start on Expense
                        child: Padding(
                          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                          child: SingleChildScrollView(
                            child: Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface,
                                borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.06),
                                    blurRadius: 16,
                                    offset: const Offset(0, -2),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Container(
                                      width: 40,
                                      height: 4,
                                      margin: const EdgeInsets.only(bottom: 16),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ),
                                  ),
                                  const Text('Add Transaction', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                                  const SizedBox(height: 20),
                                  // Colorful TabBar
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      _ColoredTabButton(
                                        label: 'Expense',
                                        color: Colors.red,
                                        selected: state.type == 'expense',
                                        onTap: () => context.read<TransactionAddBloc>().add(TransactionTypeChanged('expense')),
                                      ),
                                      _ColoredTabButton(
                                        label: 'Income',
                                        color: Colors.green,
                                        selected: state.type == 'income',
                                        onTap: () => context.read<TransactionAddBloc>().add(TransactionTypeChanged('income')),
                                      ),
                                      _ColoredTabButton(
                                        label: 'Other',
                                        color: Colors.grey,
                                        selected: state.type != 'expense' && state.type != 'income',
                                        onTap: () => context.read<TransactionAddBloc>().add(TransactionTypeChanged('transfer')),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),
                                  const Text('Details', style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16)),
                                  const SizedBox(height: 12),
                                  // Payment Method Selection
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      _ColoredTabButton(
                                        label: 'Cash',
                                        color: Colors.blue,
                                        selected: expenseSourceType == 'cash',
                                        onTap: () => setModalState(() => expenseSourceType = 'cash'),
                                      ),
                                      _ColoredTabButton(
                                        label: 'Bank',
                                        color: Colors.green,
                                        selected: expenseSourceType == 'bank',
                                        onTap: () => setModalState(() => expenseSourceType = 'bank'),
                                      ),
                                      _ColoredTabButton(
                                        label: 'Credit Card',
                                        color: Colors.purple,
                                        selected: expenseSourceType == 'card',
                                        onTap: () => setModalState(() => expenseSourceType = 'card'),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  // Category dropdown
                                  categoriesAsync.when(
                                    data: (categories) {
                                      final filteredCats = categories.where((cat) => cat.type.toLowerCase() == state.type.toLowerCase() || state.type == 'transfer').toList();
                                      final validCatIds = filteredCats.map((c) => c.id).toSet();
                                      int? dropdownValue = (state.category != null && validCatIds.contains(state.category))
                                          ? state.category
                                          : (filteredCats.isNotEmpty ? filteredCats.first.id : null);
                                      if (state.category == null && dropdownValue != null) {
                                        context.read<TransactionAddBloc>().add(TransactionCategoryChanged(dropdownValue));
                                      }
                                      return DropdownButtonFormField<int>(
                                        value: dropdownValue,
                                        isExpanded: true,
                                        focusNode: categoryFocus,
                                        items: filteredCats.map((cat) => DropdownMenuItem(
                                          value: cat.id,
                                          child: Row(
                                            children: [
                                              Icon(Icons.category, size: 18, color: Theme.of(context).colorScheme.primary),
                                              const SizedBox(width: 8),
                                              Text(cat.name),
                                            ],
                                          ),
                                        )).toList(),
                                        onChanged: (val) {
                                          context.read<TransactionAddBloc>().add(TransactionCategoryChanged(val));
                                          FocusScope.of(context).requestFocus(fromAccountFocus);
                                        },
                                        decoration: const InputDecoration(labelText: 'Category', border: OutlineInputBorder()),
                                      );
                                    },
                                    loading: () => const Padding(
                                      padding: EdgeInsets.symmetric(vertical: 12),
                                      child: Center(child: CircularProgressIndicator()),
                                    ),
                                    error: (e, st) => Text('Error loading categories'),
                                  ),
                                  const SizedBox(height: 12),
                                  // From Account dropdown
                                  if (expenseSourceType == 'bank')
                                    accountsAsync.when(
                                      data: (accounts) => DropdownButtonFormField<int>(
                                        value: state.fromAccount,
                                        isExpanded: true,
                                        focusNode: fromAccountFocus,
                                        items: accounts.map((acc) => DropdownMenuItem(
                                          value: acc.id,
                                          child: Row(
                                            children: [
                                              Icon(Icons.account_balance_wallet, size: 18, color: Colors.blue),
                                              const SizedBox(width: 8),
                                              Text(acc.bankName),
                                            ],
                                          ),
                                        )).toList(),
                                        onChanged: (val) {
                                          context.read<TransactionAddBloc>().add(TransactionFromAccountChanged(val));
                                          FocusScope.of(context).requestFocus(amountFocus);
                                        },
                                        decoration: const InputDecoration(labelText: 'From Bank Account', border: OutlineInputBorder()),
                                      ),
                                      loading: () => const CircularProgressIndicator(),
                                      error: (e, st) => Text('Error loading accounts'),
                                    ),
                                  if (expenseSourceType == 'card')
                                    cardsAsync.when(
                                      data: (cards) => DropdownButtonFormField<int>(
                                        value: state.fromAccount,
                                        isExpanded: true,
                                        focusNode: fromAccountFocus,
                                        items: cards.map((card) => DropdownMenuItem(
                                          value: card.card.id,
                                          child: Row(
                                            children: [
                                              Icon(Icons.credit_card, size: 18, color: Colors.purple),
                                              const SizedBox(width: 8),
                                              Text(card.card.bankName),
                                            ],
                                          ),
                                        )).toList(),
                                        onChanged: (val) {
                                          context.read<TransactionAddBloc>().add(TransactionFromAccountChanged(val));
                                          FocusScope.of(context).requestFocus(amountFocus);
                                        },
                                        decoration: const InputDecoration(labelText: 'From Credit Card', border: OutlineInputBorder()),
                                      ),
                                      loading: () => const CircularProgressIndicator(),
                                      error: (e, st) => Text('Error loading credit cards'),
                                    ),
                                  if (expenseSourceType == 'loan')
                                    loansAsync.when(
                                      data: (loans) => DropdownButtonFormField<int>(
                                        value: state.fromAccount,
                                        isExpanded: true,
                                        focusNode: fromAccountFocus,
                                        items: loans.map((loan) => DropdownMenuItem(
                                          value: loan.id,
                                          child: Row(
                                            children: [
                                              Icon(Icons.account_balance, size: 18, color: Colors.orange),
                                              const SizedBox(width: 8),
                                              Text(loan.loanName),
                                            ],
                                          ),
                                        )).toList(),
                                        onChanged: (val) {
                                          context.read<TransactionAddBloc>().add(TransactionFromAccountChanged(val));
                                          FocusScope.of(context).requestFocus(amountFocus);
                                        },
                                        decoration: const InputDecoration(labelText: 'From Loan', border: OutlineInputBorder()),
                                      ),
                                      loading: () => const CircularProgressIndicator(),
                                      error: (e, st) => Text('Error loading loans'),
                                    ),
                                  const SizedBox(height: 12),
                                  // Only show 'Other Type' selector if state.type is neither 'expense' nor 'income'.
                                  if (state.type != 'expense' && state.type != 'income')
                                    DropdownButtonFormField<String>(
                                      value: otherType,
                                      isExpanded: true,
                                      items: const [
                                        DropdownMenuItem(value: 'transfer', child: Text('Transfer')),
                                        DropdownMenuItem(value: 'credit_given', child: Text('Credit Given')),
                                        DropdownMenuItem(value: 'credit_received', child: Text('Credit Received')),
                                      ],
                                      onChanged: (val) {
                                        if (val != null) setModalState(() => otherType = val);
                                        if (val != null) context.read<TransactionAddBloc>().add(TransactionTypeChanged(val));
                                      },
                                      decoration: const InputDecoration(labelText: 'Other Type', border: OutlineInputBorder()),
                                    ),
                                  const SizedBox(height: 12),
                                  // Contact picker for credit types
                                  if (otherType == 'credit_given' || otherType == 'credit_received')
                                    Padding(
                                      padding: const EdgeInsets.only(top: 12.0),
                                      child: TextField(
                                        focusNode: contactFocus,
                                        decoration: const InputDecoration(labelText: 'Contact', border: OutlineInputBorder()),
                                        onChanged: (val) {
                                          context.read<TransactionAddBloc>().add(TransactionContactChanged(val));
                                        },
                                        onSubmitted: (_) {
                                          FocusScope.of(context).requestFocus(amountFocus);
                                        },
                                        textInputAction: TextInputAction.next,
                                      ),
                                    ),
                                  const SizedBox(height: 12),
                                  // Amount
                                  TextField(
                                    focusNode: amountFocus,
                                    decoration: const InputDecoration(labelText: 'Amount', border: OutlineInputBorder()),
                                    keyboardType: TextInputType.number,
                                    onChanged: (val) {
                                      final amount = double.tryParse(val) ?? 0;
                                      context.read<TransactionAddBloc>().add(TransactionAmountChanged(amount));
                                    },
                                    onSubmitted: (_) {
                                      FocusScope.of(context).requestFocus(dateFocus);
                                    },
                                    textInputAction: TextInputAction.next,
                                  ),
                                  const SizedBox(height: 12),
                                  // Date picker
                                  Row(
                                    children: [
                                      Expanded(
                                        child: InkWell(
                                          focusNode: dateFocus,
                                          onTap: () async {
                                            final picked = await showDatePicker(
                                              context: context,
                                              initialDate: state.date ?? DateTime.now(),
                                              firstDate: DateTime(2000),
                                              lastDate: DateTime(2100),
                                            );
                                            if (picked != null) {
                                              context.read<TransactionAddBloc>().add(TransactionDateChanged(picked));
                                              FocusScope.of(context).requestFocus(descriptionFocus);
                                            }
                                          },
                                          child: InputDecorator(
                                            decoration: const InputDecoration(labelText: 'Date', border: OutlineInputBorder()),
                                            child: Row(
                                              children: [
                                                Icon(Icons.calendar_today, size: 18, color: Theme.of(context).colorScheme.primary),
                                                const SizedBox(width: 8),
                                                Text(
                                                  (state.date ?? DateTime.now()).toString().split(' ')[0],
                                                  style: const TextStyle(fontSize: 16),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  // Description
                                  TextField(
                                    focusNode: descriptionFocus,
                                    decoration: const InputDecoration(labelText: 'Description', border: OutlineInputBorder()),
                                    onChanged: (val) => context.read<TransactionAddBloc>().add(TransactionDescriptionChanged(val)),
                                    textInputAction: TextInputAction.done,
                                  ),
                                  const SizedBox(height: 24),
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Theme.of(context).colorScheme.primary,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 16),
                                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                                        textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                      ),
                                      onPressed: state.isSubmitting
                                          ? null
                                          : () => context.read<TransactionAddBloc>().add(TransactionSubmitted()),
                                      child: state.isSubmitting
                                          ? const CircularProgressIndicator.adaptive()
                                          : const Text('Add Transaction'),
                                    ),
                                  ),
                                  if (state.error != null)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(state.error!, style: const TextStyle(color: Colors.red)),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        );
      },
    );
  }
}

class _InsightCard extends StatelessWidget {
  final double totalBalance;
  final int transactionCount;
  const _InsightCard({required this.totalBalance, required this.transactionCount});

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 0,
      color: Theme.of(context).colorScheme.primaryContainer,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Total Balance', style: TextStyle(fontSize: 13, color: Colors.black54)),
                Text('₹${totalBalance.toStringAsFixed(2)}', style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                const Text('Transactions', style: TextStyle(fontSize: 13, color: Colors.black54)),
                Text('$transactionCount', style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _TypeButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;
  const _TypeButton({required this.label, required this.icon, required this.color, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 120,
        height: 48,
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color, width: 2),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 8),
            Text(label, style: TextStyle(color: color, fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }
}

class _TransactionAccordionInsightCard extends StatefulWidget {
  final List transactions;
  const _TransactionAccordionInsightCard({required this.transactions});

  @override
  State<_TransactionAccordionInsightCard> createState() => _TransactionAccordionInsightCardState();
}

class _TransactionAccordionInsightCardState extends State<_TransactionAccordionInsightCard> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month);
    final thisDay = DateTime(now.year, now.month, now.day);
    // Filter for this month and today
    final monthTxs = widget.transactions.where((tx) => tx.transaction.date.isAfter(thisMonth)).toList();
    final dayTxs = widget.transactions.where((tx) => tx.transaction.date.isAfter(thisDay)).toList();
    // Totals
    double totalIncome = 0, totalExpense = 0;
    Map<String, double> categoryTotals = {};
    for (final tx in widget.transactions) {
      if (tx.transaction.type == 'income') {
        totalIncome += tx.transaction.amount;
      } else if (tx.transaction.type == 'expense') {
        totalExpense += tx.transaction.amount;
        final cat = tx.category?.name ?? 'Other';
        categoryTotals[cat] = (categoryTotals[cat] ?? 0) + tx.transaction.amount;
      }
    }
    // Averages
    final daysInMonth = DateUtils.getDaysInMonth(now.year, now.month);
    final monthlyAvg = totalExpense / (now.month);
    final dailyAvg = totalExpense / (now.difference(thisMonth).inDays == 0 ? 1 : now.difference(thisMonth).inDays);
    // Top category
    final topCategory = (categoryTotals.entries.toList()..sort((a, b) => b.value.compareTo(a.value))).firstOrNull;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primaryContainer,
            colorScheme.primaryContainer.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          InkWell(
            onTap: () => setState(() => isExpanded = !isExpanded),
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.account_balance_wallet, color: colorScheme.onPrimaryContainer, size: 28),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Financial Overview', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: colorScheme.onPrimaryContainer)),
                        const SizedBox(height: 4),
                        Text('₹${(totalIncome-totalExpense).toStringAsFixed(2)}', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold, color: colorScheme.onPrimaryContainer)),
                        Text('Net Balance', style: TextStyle(fontSize: 11, color: colorScheme.onPrimaryContainer.withOpacity(0.8))),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(isExpanded ? Icons.expand_less : Icons.expand_more, size: 18, color: colorScheme.onPrimaryContainer),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded)
            Container(
              margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.08),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withOpacity(0.15), width: 1),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildStatItem('Income', '₹${totalIncome.toStringAsFixed(2)}', Icons.trending_up, Colors.green, colorScheme),
                      _buildStatItem('Expense', '₹${totalExpense.toStringAsFixed(2)}', Icons.trending_down, Colors.red, colorScheme),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildStatItem('Monthly Avg', '₹${monthlyAvg.toStringAsFixed(2)}', Icons.calendar_month, colorScheme.primary, colorScheme),
                      _buildStatItem('Daily Avg', '₹${dailyAvg.toStringAsFixed(2)}', Icons.today, colorScheme.primary, colorScheme),
                    ],
                  ),
                  const SizedBox(height: 12),
                  if (topCategory != null)
                    Row(
                      children: [
                        Icon(Icons.category, color: colorScheme.primary, size: 18),
                        const SizedBox(width: 8),
                        Text('Top Category: ', style: TextStyle(fontWeight: FontWeight.w600, color: colorScheme.onPrimaryContainer)),
                        Text(topCategory.key, style: TextStyle(fontWeight: FontWeight.bold, color: colorScheme.primary)),
                        const SizedBox(width: 8),
                        Text('₹${topCategory.value.toStringAsFixed(2)}', style: TextStyle(fontWeight: FontWeight.bold, color: colorScheme.primary)),
                      ],
                    ),
                  if (topCategory == null)
                    Text('No expense categories yet', style: TextStyle(color: colorScheme.onPrimaryContainer.withOpacity(0.7))),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color, ColorScheme colorScheme) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, size: 18, color: color),
          const SizedBox(height: 4),
          Text(value, style: TextStyle(fontWeight: FontWeight.bold, color: colorScheme.onPrimaryContainer)),
          Text(label, style: TextStyle(fontSize: 11, color: colorScheme.onPrimaryContainer.withOpacity(0.8))),
        ],
      ),
    );
  }
}

class _ColoredTabButton extends StatelessWidget {
  final String label;
  final Color color;
  final bool selected;
  final VoidCallback onTap;
  const _ColoredTabButton({required this.label, required this.color, required this.selected, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        elevation: selected ? 2 : 0,
        borderRadius: BorderRadius.zero,
        child: InkWell(
          borderRadius: BorderRadius.zero,
          onTap: onTap,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            padding: const EdgeInsets.symmetric(vertical: 8), // Slimmer
            decoration: BoxDecoration(
              color: selected ? color : Colors.transparent,
              borderRadius: BorderRadius.zero,
              // No border
              boxShadow: selected
                  ? [
                      BoxShadow(
                        color: color.withOpacity(0.18),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [],
            ),
            child: Center(
              child: Text(
                label,
                style: TextStyle(
                  color: selected ? Colors.white : color,
                  fontWeight: FontWeight.bold,
                  fontSize: 15, // Slightly smaller
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
} 