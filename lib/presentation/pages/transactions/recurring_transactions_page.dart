import 'package:flutter/material.dart';

class RecurringTransactionsPage extends StatefulWidget {
  const RecurringTransactionsPage({super.key});

  @override
  State<RecurringTransactionsPage> createState() => _RecurringTransactionsPageState();
}

class _RecurringTransactionsPageState extends State<RecurringTransactionsPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recurring Transactions'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Income'),
            Tab(text: 'Expense'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _RecurringList(type: 'income'),
          _RecurringList(type: 'expense'),
        ],
      ),
    );
  }
}

class _RecurringList extends StatelessWidget {
  final String type;
  const _RecurringList({required this.type});

  @override
  Widget build(BuildContext context) {
    // Dummy data for demonstration
    final items = type == 'income'
        ? [
            {'amount': 5000, 'desc': 'Salary', 'frequency': 'Monthly'},
            {'amount': 200, 'desc': 'Interest', 'frequency': 'Quarterly'},
          ]
        : [
            {'amount': 100, 'desc': 'Rent', 'frequency': 'Monthly'},
            {'amount': 50, 'desc': 'Subscription', 'frequency': 'Yearly'},
          ];
    final color = type == 'income' ? Colors.green : Colors.red;
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      separatorBuilder: (_, __) => const Divider(height: 1),
      itemBuilder: (context, i) {
        final item = items[i];
        return ListTile(
          leading: Icon(type == 'income' ? Icons.arrow_downward : Icons.arrow_upward, color: color),
          title: Text(item['desc'] as String),
          subtitle: Text('Frequency: ${(item['frequency'] as String)}'),
          trailing: Text(
            '₹${item['amount']}',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        );
      },
    );
  }
} 