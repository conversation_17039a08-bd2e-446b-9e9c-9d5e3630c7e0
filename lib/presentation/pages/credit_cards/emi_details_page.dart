import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/credit_card_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class EmiDetailsPage extends ConsumerWidget {
  final int transactionId;
  const EmiDetailsPage({super.key, required this.transactionId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emiAsync = ref.watch(FutureProvider((ref) => ref.read(getEmiSchedulesUseCaseProvider).call(transactionId)));
    return Scaffold(
      appBar: AppBar(title: const Text('EMI Details')),
      body: emiAsync.when(
        data: (schedules) => ListView.builder(
          itemCount: schedules.length,
          itemBuilder: (context, index) {
            final emi = schedules[index];
            return ListTile(
              title: Text('₹${emi.monthlyPayment.toStringAsFixed(2)}'),
              subtitle: Text('Next Due: ${emi.dueDate.toLocal().toString().split(' ')[0]} | Remaining: ${emi.remainingEmis}'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      _showEmiDialog(context, ref, emi: emi);
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete),
                    onPressed: () async {
                      await ref.read(deleteEmiScheduleUseCaseProvider).call(emi.id);
                      ref.refresh(FutureProvider((ref) => ref.read(getEmiSchedulesUseCaseProvider).call(transactionId)));
                    },
                  ),
                ],
              ),
            );
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, st) => Center(child: Text('Error: $e')),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showEmiDialog(context, ref, transactionId: transactionId);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showEmiDialog(BuildContext context, WidgetRef ref, {EmiSchedule? emi, int? transactionId}) {
    final monthlyController = TextEditingController(text: emi?.monthlyPayment.toString() ?? '');
    final monthsController = TextEditingController(text: emi?.remainingEmis.toString() ?? '');
    DateTime nextDue = emi?.dueDate ?? DateTime.now().add(const Duration(days: 30));
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(emi == null ? 'Add EMI Schedule' : 'Edit EMI Schedule'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: monthlyController,
                decoration: const InputDecoration(labelText: 'Monthly Payment'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: monthsController,
                decoration: const InputDecoration(labelText: 'Remaining Months'),
                keyboardType: TextInputType.number,
              ),
              TextButton(
                onPressed: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: nextDue,
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (picked != null) nextDue = picked;
                },
                child: Text('Next Due: ${nextDue.toLocal().toString().split(' ')[0]}'),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () async {
                if (monthlyController.text.isNotEmpty && monthsController.text.isNotEmpty) {
                  final companion = EmiSchedulesCompanion(
                    transactionId: drift.Value(emi?.transactionId ?? transactionId!),
                    monthlyPayment: drift.Value(double.tryParse(monthlyController.text) ?? 0),
                    remainingEmis: drift.Value(int.tryParse(monthsController.text) ?? 0),
                    dueDate: drift.Value(nextDue),
                  );
                  if (emi == null) {
                    await ref.read(addEmiScheduleUseCaseProvider).call(companion);
                  } else {
                    await ref.read(updateEmiScheduleUseCaseProvider).call(emi.id, companion);
                  }
                  ref.refresh(FutureProvider((ref) => ref.read(getEmiSchedulesUseCaseProvider).call(emi?.transactionId ?? transactionId!)));
                  Navigator.pop(context);
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }
} 