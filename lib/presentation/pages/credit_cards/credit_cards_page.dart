import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/credit_card_providers.dart';
import '../../../core/database/app_database.dart';
import 'package:drift/drift.dart' as drift;
import '../../providers/payment_source_providers.dart';
import '../../providers/loan_providers.dart';
import '../loan_planner/loan_planner_page.dart';
import '../../../domain/repositories/credit_card_repository.dart';
import '../../../domain/models/payment_source.dart';
import '../../widgets/universal_scaffold.dart';

class CreditCardsPage extends ConsumerWidget {
  const CreditCardsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cardsAsync = ref.watch(creditCardsProvider);

    return UniversalScaffold(
      title: 'Credit Cards',
      body: cardsAsync.when(
        data: (cards) => ListView.builder(
          itemCount: cards.length,
          itemBuilder: (context, index) {
            final card = cards[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.credit_card, size: 36, color: Theme.of(context).colorScheme.primary),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(card.card.bankName, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                              const SizedBox(height: 4),
                              Text('Card: ${_maskedCardNumber(card.card.lastFourDigits)}', style: Theme.of(context).textTheme.bodyMedium),
                              const SizedBox(height: 4),
                              Text('Limit: ₹${card.card.creditLimit.toStringAsFixed(2)}', style: Theme.of(context).textTheme.bodySmall),
                              Text('Due: ${card.card.dueDate.toLocal().toString().split(' ')[0]}', style: Theme.of(context).textTheme.bodySmall),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text('₹${card.card.outstandingBalance.toStringAsFixed(2)}', style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.red, fontWeight: FontWeight.bold)),
                            Text('Util: ${(card.card.outstandingBalance/card.card.creditLimit * 100).toStringAsFixed(1)}%', style: Theme.of(context).textTheme.bodySmall),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.payment),
                          tooltip: 'Pay Card',
                          onPressed: () => _showCardPaymentDialog(context, ref, card.card),
                        ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {
                            _showCardDialog(context, ref, card: card.card);
                          },
                        ),
                        IconButton(
                          icon: const Icon(Icons.delete),
                          onPressed: () async {
                            await ref.read(deleteCreditCardUseCaseProvider).call(card.card.id);
                            ref.refresh(creditCardsProvider);
                          },
                        ),
                      ],
                    ),
                    const Divider(),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
                      child: Text('Transactions', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                    ...card.transactions.map((tx) => ListTile(
                      leading: Icon(Icons.swap_horiz, color: Theme.of(context).colorScheme.primary),
                      title: Text('₹${tx.amount.toStringAsFixed(2)} - ${tx.category}'),
                      subtitle: Text('${tx.transactionDate.toLocal().toString().split(' ')[0]} | EMI: ${tx.isEmi ? 'Yes' : 'No'}'),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () async {
                          await ref.read(deleteCreditCardTransactionUseCaseProvider).call(tx.id);
                          ref.refresh(creditCardsProvider);
                        },
                      ),
                    )),
                    ListTile(
                      title: const Text('Add Transaction'),
                      trailing: IconButton(
                        icon: const Icon(Icons.add),
                        onPressed: () {
                          _showTransactionDialog(context, ref, cardId: card.card.id);
                        },
                      ),
                    ),
                    ListTile(
                      title: const Text('Linked Loans'),
                      trailing: IconButton(
                        icon: const Icon(Icons.link),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => LoanPlannerPage(),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, st) => Center(child: Text('Error: $e')),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCardDialog(context, ref);
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showCardDialog(BuildContext context, WidgetRef ref, {CreditCard? card}) {
    final bankController = TextEditingController(text: card?.bankName ?? '');
    final lastFourDigitsController = TextEditingController(text: card?.lastFourDigits ?? '');
    final limitController = TextEditingController(text: card?.creditLimit.toString() ?? '');
    final balanceController = TextEditingController(text: card?.outstandingBalance.toString() ?? '');
    final interestController = TextEditingController(text: card?.interestRate.toString() ?? '');
    DateTime dueDate = card?.dueDate ?? DateTime.now().add(const Duration(days: 30));
    bool isEmi = card?.isActive ?? false;
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(card == null ? 'Add Credit Card' : 'Edit Credit Card'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: bankController,
                  decoration: const InputDecoration(labelText: 'Bank Name'),
                ),
                TextField(
                  controller: lastFourDigitsController,
                  decoration: const InputDecoration(labelText: 'Last 4 Digits of Card Number'),
                  keyboardType: TextInputType.number,
                  maxLength: 4,
                ),
                TextField(
                  controller: limitController,
                  decoration: const InputDecoration(labelText: 'Limit'),
                  keyboardType: TextInputType.number,
                ),
                TextField(
                  controller: balanceController,
                  decoration: const InputDecoration(labelText: 'Outstanding Balance'),
                  keyboardType: TextInputType.number,
                ),
                TextField(
                  controller: interestController,
                  decoration: const InputDecoration(labelText: 'Interest Rate'),
                  keyboardType: TextInputType.number,
                ),
                SwitchListTile(
                  value: isEmi,
                  onChanged: (val) => isEmi = val,
                  title: const Text('EMI Enabled'),
                ),
                TextButton(
                  onPressed: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: dueDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (picked != null) dueDate = picked;
                  },
                  child: Text('Due: ${dueDate.toLocal().toString().split(' ')[0]}'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () async {
                if (bankController.text.isNotEmpty && lastFourDigitsController.text.length == 4) {
                  final companion = CreditCardsCompanion(
                    bankName: drift.Value(bankController.text),
                    cardNumber: drift.Value('****${lastFourDigitsController.text}'),
                    lastFourDigits: drift.Value(lastFourDigitsController.text),
                    creditLimit: drift.Value(double.tryParse(limitController.text) ?? 0),
                    outstandingBalance: drift.Value(double.tryParse(balanceController.text) ?? 0),
                    interestRate: drift.Value(double.tryParse(interestController.text) ?? 0),
                    dueDate: drift.Value(dueDate),
                    isActive: drift.Value(isEmi),
                  );
                  if (card == null) {
                    await ref.read(addCreditCardUseCaseProvider).call(companion);
                  } else {
                    await ref.read(updateCreditCardUseCaseProvider).call(card.id, companion);
                  }
                  ref.refresh(creditCardsProvider);
                  Navigator.pop(context);
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showTransactionDialog(BuildContext context, WidgetRef ref, {required int cardId}) {
    final amountController = TextEditingController();
    final categoryController = TextEditingController();
    bool isEmi = false;
    final emiMonthsController = TextEditingController();
    DateTime txDate = DateTime.now();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Add Transaction'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: amountController,
                  decoration: const InputDecoration(labelText: 'Amount'),
                  keyboardType: TextInputType.number,
                ),
                TextField(
                  controller: categoryController,
                  decoration: const InputDecoration(labelText: 'Category'),
                ),
                SwitchListTile(
                  value: isEmi,
                  onChanged: (val) => isEmi = val,
                  title: const Text('Is EMI?'),
                ),
                if (isEmi)
                  TextField(
                    controller: emiMonthsController,
                    decoration: const InputDecoration(labelText: 'EMI Months'),
                    keyboardType: TextInputType.number,
                  ),
                TextButton(
                  onPressed: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: txDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (picked != null) txDate = picked;
                  },
                  child: Text('Date: ${txDate.toLocal().toString().split(' ')[0]}'),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () async {
                if (amountController.text.isNotEmpty) {
                  final companion = CreditCardTransactionsCompanion(
                    cardId: drift.Value(cardId),
                    amount: drift.Value(double.tryParse(amountController.text) ?? 0),
                    category: drift.Value(categoryController.text),
                    isEmi: drift.Value(isEmi),
                    emiMonths: drift.Value(isEmi ? int.tryParse(emiMonthsController.text) : null),
                    transactionDate: drift.Value(txDate),
                  );
                  await ref.read(addCreditCardTransactionUseCaseProvider).call(companion);
                  ref.refresh(creditCardsProvider);
                  Navigator.pop(context);
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }

  void _showCardPaymentDialog(BuildContext context, WidgetRef ref, CreditCard card) {
    final formKey = GlobalKey<FormState>();
    final amountController = TextEditingController();
    DateTime paymentDate = DateTime.now();
    int? selectedSourceId;
    PaymentSourceType? selectedSourceType;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Pay Credit Card'),
        content: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: amountController,
                  decoration: const InputDecoration(labelText: 'Amount'),
                  keyboardType: TextInputType.number,
                  validator: (v) => v == null || v.isEmpty ? 'Required' : null,
                ),
                TextButton(
                  onPressed: () async {
                    final picked = await showDatePicker(
                      context: context,
                      initialDate: paymentDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                    );
                    if (picked != null) paymentDate = picked;
                  },
                  child: Text('Date: ${paymentDate.toLocal().toString().split(' ')[0]}'),
                ),
                FutureBuilder(
                  future: ref.read(paymentSourcesProvider.future),
                  builder: (context, snapshot) {
                    if (!snapshot.hasData) return const CircularProgressIndicator();
                    final sources = snapshot.data as List<PaymentSource>;
                    return DropdownButtonFormField<int>(
                      value: selectedSourceId,
                      items: sources
                          .map((s) => DropdownMenuItem(
                                value: s.sourceId,
                                child: Text('${s.name} (${s.type.name})'),
                              ))
                          .toList(),
                      onChanged: (val) {
                        selectedSourceId = val;
                        selectedSourceType = sources.firstWhere((s) => s.sourceId == val).type;
                      },
                      decoration: const InputDecoration(labelText: 'Payment Source'),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState?.validate() ?? false && selectedSourceId != null) {
                // TODO: Integrate with add credit card payment use case
                Navigator.pop(context);
              }
            },
            child: const Text('Pay'),
          ),
        ],
      ),
    );
  }

  String _maskedCardNumber(String? lastFourDigits) {
    if (lastFourDigits == null || lastFourDigits.length < 4) return '****';
    return '****$lastFourDigits';
  }
}

class _CardStats extends StatelessWidget {
  final CreditCard card;
  const _CardStats({required this.card});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text('Util: ${(card.outstandingBalance/card.creditLimit * 100).toStringAsFixed(1)}%', style: const TextStyle(fontSize: 12)),
        Text('Pending: ₹${card.outstandingBalance.toStringAsFixed(0)}', style: const TextStyle(fontSize: 12)),
        Text('Next Due: ${card.dueDate.toLocal().toString().split(' ')[0]}', style: const TextStyle(fontSize: 12)),
      ],
    );
  }
} 