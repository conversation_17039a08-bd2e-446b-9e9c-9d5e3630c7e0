// TransactionList widget for displaying grouped and styled transactions.
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TransactionList extends StatefulWidget {
  final List transactions;
  final ScrollController controller;
  final bool isLoading;
  final bool hasMore;
  final bool showDateHeader;
  const TransactionList({
    super.key,
    required this.transactions,
    required this.controller,
    required this.isLoading,
    required this.hasMore,
    this.showDateHeader = true,
  });

  @override
  State<TransactionList> createState() => _TransactionListState();
}

class _TransactionListState extends State<TransactionList> {
  // Change expandedIndex to a Set to allow multiple expanded tiles
  Set<int> expandedIndices = {};

  @override
  Widget build(BuildContext context) {
    if (widget.transactions.isEmpty && !widget.isLoading) {
      return const Center(child: Text('No transactions found'));
    }
    // Group transactions by date
    final Map<String, List> grouped = {};
    for (final tx in widget.transactions) {
      final date = DateFormat('yyyy-MM-dd').format(tx.transaction.date);
      grouped.putIfAbsent(date, () => []).add(tx);
    }
    final sortedDates = grouped.keys.toList()..sort((a, b) => b.compareTo(a));
    int globalIndex = 0;
    return ListView.builder(
      controller: widget.controller,
      itemCount: sortedDates.length + (widget.isLoading || !widget.hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == sortedDates.length) {
          if (widget.isLoading) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          } else if (!widget.hasMore) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: Text('No more transactions')),
            );
          }
        }
        final date = sortedDates[index];
        final txs = grouped[date]!;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showDateHeader)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Timeline bar/dot (light gray)
                    Container(
                      width: 4,
                      height: 18,
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.13),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      DateFormat('EEE, MMM dd, yyyy').format(DateTime.parse(date)),
                      style: TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 13,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.85),
                        letterSpacing: 0.2,
                      ),
                    ),
                  ],
                ),
              ),
            ...txs.map((tx) {
              final tileIndex = globalIndex++;
              return _buildTransactionCard(context, tx, tileIndex);
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildTransactionCard(BuildContext context, dynamic tx, int tileIndex) {
    final isIncome = tx.transaction.type == 'income';
    final isExpense = tx.transaction.type == 'expense';
    final isTransfer = tx.transaction.type == 'transfer';
    final color = isIncome
        ? Colors.green
        : isExpense
            ? Colors.red
            : isTransfer
                ? Colors.blue
                : Colors.grey;
    final iconData = _getCategoryIcon(tx.category?.icon);
    final dateStr = DateFormat('hh:mm a').format(tx.transaction.date);
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isExpanded = expandedIndices.contains(tileIndex);

    // Bank account info
    String? accountName;
    String? accountType;
    String? lastFour;
    String? toAccountName;
    String? toAccountLastFour;
    if (isIncome) {
      accountName = tx.toAccount?.bankName ?? '';
      accountType = tx.toAccount?.accountType ?? '';
      lastFour = tx.toAccount?.lastFourDigits ?? '';
    } else if (isExpense) {
      accountName = tx.fromAccount?.bankName ?? '';
      accountType = tx.fromAccount?.accountType ?? '';
      lastFour = tx.fromAccount?.lastFourDigits ?? '';
    } else if (isTransfer) {
      accountName = tx.fromAccount?.bankName ?? '';
      accountType = tx.fromAccount?.accountType ?? '';
      lastFour = tx.fromAccount?.lastFourDigits ?? '';
      toAccountName = tx.toAccount?.bankName ?? '';
      toAccountLastFour = tx.toAccount?.lastFourDigits ?? '';
    }

    return GestureDetector(
      onTap: () {
        setState(() {
          if (expandedIndices.contains(tileIndex)) {
            expandedIndices.remove(tileIndex);
          } else {
            expandedIndices.add(tileIndex);
          }
        });
      },
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.85),
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Payment type icon
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(iconData, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              // Details (description + category)
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (tx.transaction.description != null && tx.transaction.description!.isNotEmpty)
                      Text(
                        tx.transaction.description!,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    if (tx.category != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface.withOpacity(0.25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            tx.category?.name ?? '',
                            style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 11),
                          ),
                        ),
                      ),
                    // Expanded content: account info (on tap)
                    AnimatedCrossFade(
                      firstChild: const SizedBox.shrink(),
                      secondChild: _buildAccountDetails(tx),
                      crossFadeState: expandedIndices.contains(tileIndex) ? CrossFadeState.showSecond : CrossFadeState.showFirst,
                      duration: const Duration(milliseconds: 220),
                    ),
                  ],
                ),
              ),
              // Amount and time
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    (isIncome ? '+' : isExpense ? '-' : '') + '₹${tx.transaction.amount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: color,
                    ),
                  ),
                  Text(
                    dateStr,
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.outline.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper to build account details for expanded tile
  Widget _buildAccountDetails(dynamic tx) {
    final isIncome = tx.transaction.type == 'income';
    final isTransfer = tx.transaction.type == 'transfer';
    final fromAccount = tx.fromAccount;
    final toAccount = tx.toAccount;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final Color black = Colors.black;
    final Color white = Colors.white;

    List<Widget> details = [];
    if (isTransfer) {
      if (fromAccount != null) {
        details.add(Row(
          children: [
            Icon(Icons.arrow_upward, size: 13, color: isDark ? white : black),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                'From: ${fromAccount.bankName ?? ''} • ****${fromAccount.lastFourDigits ?? ''}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(color: isDark ? white : black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ));
      }
      if (toAccount != null) {
        details.add(Row(
          children: [
            Icon(Icons.arrow_downward, size: 13, color: isDark ? white : black),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                'To: ${toAccount.bankName ?? ''} • ****${toAccount.lastFourDigits ?? ''}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(color: isDark ? white : black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ));
      }
    } else if (isIncome) {
      if (toAccount != null) {
        details.add(Row(
          children: [
            Icon(Icons.account_balance, size: 13, color: isDark ? white : black),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                '${toAccount.bankName ?? ''} • ****${toAccount.lastFourDigits ?? ''}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(color: isDark ? white : black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ));
      }
    } else {
      if (fromAccount != null) {
        details.add(Row(
          children: [
            Icon(Icons.account_balance, size: 13, color: isDark ? white : black),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                '${fromAccount.bankName ?? ''} • ****${fromAccount.lastFourDigits ?? ''}',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(color: isDark ? white : black),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ));
      }
    }
    return details.isEmpty
        ? const SizedBox.shrink()
        : Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: details,
            ),
          );
  }

  IconData _getCategoryIcon(String? iconName) {
    // Add more mappings as needed
    switch (iconName) {
      case 'restaurant':
        return Icons.restaurant;
      case 'directions_car':
        return Icons.directions_car;
      case 'shopping_bag':
        return Icons.shopping_bag;
      case 'movie':
        return Icons.movie;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'account_balance_wallet':
        return Icons.account_balance_wallet;
      case 'work':
        return Icons.work;
      case 'trending_up':
        return Icons.trending_up;
      default:
        return Icons.category;
    }
  }
} 