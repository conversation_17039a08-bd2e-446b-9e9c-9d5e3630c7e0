import 'package:flutter/material.dart';
import 'app_drawer.dart';

class UniversalScaffold extends StatelessWidget {
  final String title;
  final Widget body;
  final Widget? floatingActionButton;
  final int? currentTab;
  final ValueChanged<int>? onTabChanged;
  final bool showBottomNav;
  const UniversalScaffold({
    super.key,
    required this.title,
    required this.body,
    this.floatingActionButton,
    this.currentTab,
    this.onTabChanged,
    this.showBottomNav = true,
  });

  @override
  Widget build(BuildContext context) {
    final canPop = Navigator.of(context).canPop();
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: canPop
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => Navigator.of(context).maybePop(),
              )
            : Builder(
                builder: (context) => IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () => Scaffold.of(context).openDrawer(),
                ),
              ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.account_circle_outlined),
            onPressed: () {},
          ),
        ],
      ),
      drawer: canPop ? null : const AppDrawer(),
      body: body,
      floatingActionButton: floatingActionButton,
      bottomNavigationBar: showBottomNav && currentTab != null && onTabChanged != null
          ? Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: BottomNavigationBar(
                currentIndex: currentTab!,
                onTap: onTabChanged,
                type: BottomNavigationBarType.fixed,
                selectedItemColor: Theme.of(context).colorScheme.primary,
                unselectedItemColor: Colors.grey,
                backgroundColor: Theme.of(context).colorScheme.surface,
                elevation: 0,
                items: const [
                  BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Dashboard'),
                  BottomNavigationBarItem(icon: Icon(Icons.receipt_long), label: 'Expenses'),
                  BottomNavigationBarItem(icon: Icon(Icons.pie_chart), label: 'Budgets'),
                  BottomNavigationBarItem(icon: Icon(Icons.account_balance), label: 'Accounts'),
                  BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'Settings'),
                ],
              ),
            )
          : null,
    );
  }
} 