import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

/// Reusable form field with validation support
class ValidatedTextField extends StatelessWidget {
  final String label;
  final String? hint;
  final String? initialValue;
  final String? errorText;
  final ValueChanged<String>? onChanged;
  final TextInputType? keyboardType;
  final int? maxLines;
  final String? prefixText;
  final Widget? prefixIcon;

  const ValidatedTextField({
    super.key,
    required this.label,
    this.hint,
    this.initialValue,
    this.errorText,
    this.onChanged,
    this.keyboardType,
    this.maxLines = 1,
    this.prefixText,
    this.prefixIcon,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: initialValue,
      onChanged: onChanged,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixText: prefixText,
        prefixIcon: prefixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        errorText: errorText,
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
      style: Theme.of(context).textTheme.bodyLarge,
    );
  }
}

/// Reusable dropdown field with validation support
class ValidatedDropdownField<T> extends StatelessWidget {
  final String label;
  final T? value;
  final List<T> items;
  final String Function(T) itemText;
  final ValueChanged<T?>? onChanged;
  final String? errorText;

  const ValidatedDropdownField({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    required this.itemText,
    this.onChanged,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure items list is unique and not empty
    final uniqueItems = items.toSet().toList();
    
    // Ensure the value exists in the items list
    final validValue = uniqueItems.contains(value) ? value : null;
    
    
    return DropdownButtonFormField<T>(
      value: validValue,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Theme.of(context).colorScheme.surfaceContainerHighest,
        errorText: errorText,
      ),
      items: uniqueItems.map((item) => DropdownMenuItem(
        value: item,
        child: Text(itemText(item)),
      )).toList(),
      onChanged: onChanged,
    );
  }
}

/// Reusable date picker field
class DatePickerField extends StatelessWidget {
  final String label;
  final DateTime? selectedDate;
  final ValueChanged<DateTime?>? onDateChanged;
  final bool allowNull;

  const DatePickerField({
    super.key,
    required this.label,
    required this.selectedDate,
    this.onDateChanged,
    this.allowNull = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime(2000),
          lastDate: DateTime(2100),
        );
        if (onDateChanged != null) {
          onDateChanged!(picked);
        }
      },
      child: Container(
         padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
         decoration: BoxDecoration(
           color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.7),
           borderRadius: BorderRadius.circular(20),
         ),
         child: Row(
           mainAxisSize: MainAxisSize.min,
           children: [
             Icon(
               Icons.calendar_today,
               color: Theme.of(context).colorScheme.primary,
               size: 18,
             ),
             const SizedBox(width: 6),
            if (selectedDate != null)
              Text(
                DateFormat('MMM dd, yyyy').format(selectedDate!),
                style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              )
            else
              Text(
                label,
                style: TextStyle(
                  fontSize: 13,
                  color: Theme.of(context).colorScheme.outline,
                  fontWeight: FontWeight.w400,
                  fontStyle: FontStyle.italic,
                ),
              ),
            if (allowNull && selectedDate != null)
              Padding(
                padding: const EdgeInsets.only(left: 4),
                child: GestureDetector(
                  onTap: () {
                    if (onDateChanged != null) onDateChanged!(null);
                  },
                  child: Icon(Icons.clear, size: 16, color: Theme.of(context).colorScheme.outline),
                ),
              ),
           ],
         ),
       ),
     );
   }
 }

/// Reusable switch tile for toggle options
class SwitchTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final bool value;
  final ValueChanged<bool>? onChanged;

  const SwitchTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onChanged == null ? null : () {
          onChanged!(!value);
        },
        borderRadius: BorderRadius.circular(12),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: value 
                ? Theme.of(context).colorScheme.primaryContainer
                : Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: value 
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline.withOpacity(0.2),
              width: value ? 2 : 1,
            ),
            boxShadow: value ? [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Column(
            children: [
              AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: Icon(
                  icon,
                  color: value 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                  size: value ? 24 : 20,
                ),
              ),
              const SizedBox(height: 8),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  fontWeight: value ? FontWeight.bold : FontWeight.w600,
                  color: value 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurface,
                ),
                child: Text(title),
              ),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  fontSize: 12,
                  color: value 
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                ),
                child: Text(
                  subtitle,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Reusable form card wrapper
class FormCard extends StatelessWidget {
  final String title;
  final Widget child;
  final EdgeInsetsGeometry? padding;

  const FormCard({
    super.key,
    required this.title,
    required this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            child,
          ],
        ),
      ),
    );
  }
}

/// Reusable insight card for displaying statistics
class InsightCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final List<InsightItem> items;
  final Color? gradientStartColor;
  final Color? gradientEndColor;

  const InsightCard({
    super.key,
    required this.title,
    required this.icon,
    required this.items,
    this.gradientStartColor,
    this.gradientEndColor,
  });

  @override
  Widget build(BuildContext context) {
    final startColor = gradientStartColor ?? Theme.of(context).colorScheme.primaryContainer;
    final endColor = gradientEndColor ?? startColor.withOpacity(0.8);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [startColor, endColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: items.map((item) => Expanded(
              child: _buildInsightItem(item),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(InsightItem item) {
    return Column(
      children: [
        Icon(
          item.icon,
          size: 16,
          color: Colors.white.withOpacity(0.7),
        ),
        const SizedBox(height: 2),
        Text(
          item.value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          item.label,
          style: TextStyle(
            fontSize: 9,
            color: Colors.white.withOpacity(0.7),
          ),
        ),
      ],
    );
  }
}

/// Data class for insight items
class InsightItem {
  final String label;
  final String value;
  final IconData icon;

  const InsightItem({
    required this.label,
    required this.value,
    required this.icon,
  });
}

/// Reusable search and filter bar
class SearchFilterBar extends StatelessWidget {
  final TextEditingController searchController;
  final String searchHint;
  final String? selectedFilter;
  final List<String> filterOptions;
  final ValueChanged<String?> onFilterChanged;
  final ValueChanged<String> onSearchChanged;

  const SearchFilterBar({
    super.key,
    required this.searchController,
    required this.searchHint,
    this.selectedFilter,
    required this.filterOptions,
    required this.onFilterChanged,
    required this.onSearchChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // Search
        Expanded(
          child: Container(
            height: 36,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextField(
              controller: searchController,
              onChanged: onSearchChanged,
              decoration: InputDecoration(
                hintText: searchHint,
                prefixIcon: const Icon(Icons.search, size: 18),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                hintStyle: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.outline,
                ),
              ),
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ),
        const SizedBox(width: 8),
        // Filter Button
        Container(
          height: 36,
          child: PopupMenuButton<String?>(
            initialValue: selectedFilter,
            onSelected: onFilterChanged,
            itemBuilder: (context) => filterOptions.map((option) => 
              PopupMenuItem<String?>(value: option, child: Text(option))
            ).toList(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    selectedFilter ?? 'All',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
} 