import 'package:flutter/material.dart';
import 'form_components.dart';

class TransactionForm extends StatelessWidget {
  final String type; // 'income' or 'expense'
  const TransactionForm({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    final isIncome = type == 'income';
    final color = isIncome ? Colors.green : Colors.red;
    return Card(
      color: color.withOpacity(0.05),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isIncome ? 'Add Income' : 'Add Expense',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 16),
            ValidatedTextField(
              label: 'Amount',
              prefixText: '₹',
              keyboardType: TextInputType.number,
              onChanged: (v) {},
            ),
            const SizedBox(height: 16),
            ValidatedTextField(
              label: isIncome ? 'Source' : 'Category',
              onChanged: (v) {},
            ),
            const SizedBox(height: 16),
            ValidatedTextField(
              label: 'Description',
              onChanged: (v) {},
            ),
            const SizedBox(height: 16),
            DatePickerField(
              label: 'Date',
              selectedDate: DateTime.now(),
              onDateChanged: (d) {},
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                minimumSize: const Size.fromHeight(48),
              ),
              onPressed: () {},
              child: Text(isIncome ? 'Add Income' : 'Add Expense'),
            ),
          ],
        ),
      ),
    );
  }
} 