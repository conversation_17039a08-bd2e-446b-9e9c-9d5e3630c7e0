import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class AnalyticsSection extends StatelessWidget {
  final Map<String, double> categoryTotals;
  final double totalSpent;
  final String currencySymbol;
  const AnalyticsSection({
    super.key,
    required this.categoryTotals,
    required this.totalSpent,
    this.currencySymbol = '₹',
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final topCategories = categoryTotals.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (topCategories.isNotEmpty)
          Card(
            elevation: 0,
            color: theme.colorScheme.surfaceContainerHighest,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
              child: Row(
                children: [
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: <PERSON><PERSON><PERSON>(
                      PieChartData(
                        sections: topCategories.take(4).map((entry) {
                          final color = _colorFromHex('#${entry.key.hashCode.toRadixString(16).padLeft(6, '0')}');
                          return PieChartSectionData(
                            color: color,
                            value: entry.value,
                            title: '',
                            radius: 30,
                          );
                        }).toList(),
                        sectionsSpace: 0,
                        centerSpaceRadius: 22,
                      ),
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Top Categories', style: theme.textTheme.bodyMedium),
                        ...topCategories.take(4).map((entry) {
                          final color = _colorFromHex('#${entry.key.hashCode.toRadixString(16).padLeft(6, '0')}');
                          return Padding(
                            padding: const EdgeInsets.symmetric(vertical: 2),
                            child: Row(
                              children: [
                                Container(width: 12, height: 12, decoration: BoxDecoration(color: color, shape: BoxShape.circle)),
                                const SizedBox(width: 8),
                                Expanded(child: Text(entry.key, style: theme.textTheme.bodySmall)),
                                Text('$currencySymbol${entry.value.toStringAsFixed(0)}', style: theme.textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold)),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        const SizedBox(height: 16),
        Card(
          elevation: 0,
          color: theme.colorScheme.surfaceContainerHighest,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.trending_down, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Text('Total Spent This Month', style: theme.textTheme.bodyMedium),
                  ],
                ),
                const SizedBox(height: 8),
                Text('$currencySymbol${totalSpent.toStringAsFixed(2)}', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _colorFromHex(String hexColor) {
    final hex = hexColor.replaceAll('#', '');
    return Color(int.parse('FF$hex', radix: 16));
  }
} 