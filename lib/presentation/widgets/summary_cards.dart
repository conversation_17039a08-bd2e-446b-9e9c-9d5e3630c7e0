import 'package:flutter/material.dart';

class SummaryCards extends StatelessWidget {
  final double totalBalance;
  final int accountsCount;
  final int savingsCount;
  final int currentCount;
  const SummaryCards({
    super.key,
    required this.totalBalance,
    required this.accountsCount,
    required this.savingsCount,
    required this.currentCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                  child: Icon(Icons.account_balance_wallet, color: theme.colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Total Balance', style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600])),
                      const SizedBox(height: 8),
                      Text(
                        '₹${totalBalance.toStringAsFixed(2)}',
                        style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _iconProgress(context, icon: Icons.account_balance_wallet, label: 'Accounts', value: accountsCount, color: theme.colorScheme.primary),
                _iconProgress(context, icon: Icons.savings, label: 'Savings', value: savingsCount, color: Colors.green),
                _iconProgress(context, icon: Icons.account_balance, label: 'Current', value: currentCount, color: Colors.blue),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _iconProgress(BuildContext context, {required IconData icon, required String label, required int value, required Color color}) {
    return Column(
      children: [
        Icon(icon, color: color),
        const SizedBox(height: 4),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
        Text('$value', style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold)),
      ],
    );
  }
} 