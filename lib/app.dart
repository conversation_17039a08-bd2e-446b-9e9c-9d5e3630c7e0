import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'presentation/pages/lock/lock_screen.dart';
import 'presentation/providers/biometric_providers.dart';
import 'presentation/providers/firebase_auth_providers.dart';
import 'presentation/pages/main_scaffold.dart';
import 'presentation/pages/auth/signing_page.dart';
import 'core/design/design.dart';
import 'core/services/background_alert_service.dart';
import 'presentation/providers/recurring_income_providers.dart';

class App extends ConsumerStatefulWidget {
  const App({super.key});

  @override
  ConsumerState<App> createState() => _AppState();
}

class _AppState extends ConsumerState<App> {
  @override
  void initState() {
    super.initState();
    // Start background services
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Start background alert checking
      final backgroundService = ref.read(backgroundAlertServiceProvider);
      backgroundService.startAlertChecking();
      
      // Start recurring income service
      final recurringIncomeService = ref.read(recurringIncomeServiceProvider);
      recurringIncomeService.startDailyCheck();
    });
  }

  @override
  void dispose() {
    // Stop background services
    final backgroundService = ref.read(backgroundAlertServiceProvider);
    backgroundService.stopAlertChecking();
    
    final recurringIncomeService = ref.read(recurringIncomeServiceProvider);
    recurringIncomeService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authStateProvider);
    final biometricLock = ref.watch(biometricLockProvider);
    
    return MaterialApp(
      title: 'Financial App',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      home: authState.when(
        data: (state) {
          if (!state.isAuthenticated) {
            return const SigningPage();
          }
          return biometricLock.when(
            data: (isLocked) => isLocked ? const LockScreen() : const MainScaffold(),
            loading: () => const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            ),
            error: (_, __) => const MainScaffold(),
          );
        },
        loading: () => const Scaffold(
          body: Center(child: CircularProgressIndicator()),
        ),
        error: (error, stack) => const SigningPage(),
      ),
      routes: {
        '/main': (context) => const MainScaffold(),
        '/signing': (context) => const SigningPage(),
      },
      debugShowCheckedModeBanner: false,
    );
  }
} 