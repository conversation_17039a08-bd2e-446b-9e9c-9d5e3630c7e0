import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../domain/repositories/recurring_income_repository.dart';

class AddRecurringIncomeUseCase {
  final RecurringIncomeRepository repository;

  AddRecurringIncomeUseCase(this.repository);

  Future<int> call(RecurringIncomesCompanion recurringIncome) async {
    return await repository.addRecurringIncome(recurringIncome);
  }
} 