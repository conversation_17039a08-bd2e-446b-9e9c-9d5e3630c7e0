import '../../domain/repositories/bank_account_repository.dart';
import '../../domain/repositories/credit_card_repository.dart';
import '../../domain/repositories/personal_creditor_repository.dart';
import '../../domain/models/payment_source.dart';

class GetPaymentSourcesUseCase {
  final BankAccountRepository bankAccountRepository;
  final CreditCardRepository creditCardRepository;
  final PersonalCreditorRepository personalCreditorRepository;

  GetPaymentSourcesUseCase({
    required this.bankAccountRepository,
    required this.creditCardRepository,
    required this.personalCreditorRepository,
  });

  Future<List<PaymentSource>> call() async {
    final bankAccounts = await bankAccountRepository.getBankAccounts();
    final creditCards = await creditCardRepository.getAllCreditCards();
    final creditors = await personalCreditorRepository.getAllCreditors();

    return [
      ...bankAccounts.map((a) => PaymentSource(
            type: PaymentSourceType.bankAccount,
            sourceId: a.id,
            name: a.bankName,
            details: a.accountNumber,
          )),
      ...creditCards.map((c) => PaymentSource(
            type: PaymentSourceType.creditCard,
            sourceId: c.id,
            name: c.cardHolderName,
            details: c.cardNumber,
          )),
      ...creditors.map((p) => PaymentSource(
            type: PaymentSourceType.personalCreditor,
            sourceId: p.id,
            name: p.name,
            details: p.phone,
          )),
    ];
  }
} 