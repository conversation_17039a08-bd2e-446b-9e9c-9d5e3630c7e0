import '../../core/services/loan_calculation_service.dart';

class CalculateEmiUseCase {
  double call({
    required double principal,
    required double interestRate,
    required int months,
    String interestType = 'compound',
  }) {
    return LoanCalculationService.calculateEmi(
      principal: principal,
      interestRate: interestRate,
      months: months,
      interestType: interestType,
    );
  }
} 