import '../../domain/repositories/category_repository.dart';
import '../../domain/models/category.dart';

class GetCategoriesUseCase {
  final CategoryRepository repository;
  GetCategoriesUseCase(this.repository);

  Future<List<Category>> call() async {
    final dbCategories = await repository.getCategories();
    return dbCategories.map((cat) => Category(
      id: cat.id,
      name: cat.name,
      type: cat.type,
      color: int.tryParse(cat.color) ?? 0xFF000000,
      icon: cat.icon,
    )).toList();
  }
} 