import '../../core/services/loan_calculation_service.dart';

class GenerateEmiScheduleUseCase {
  List<Map<String, dynamic>> call({
    required double principal,
    required double emiAmount,
    required double interestRate,
    required int months,
    required DateTime startDate,
  }) {
    return LoanCalculationService.generateEmiSchedule(
      principal: principal,
      emiAmount: emiAmount,
      interestRate: interestRate,
      months: months,
      startDate: startDate,
    );
  }
} 