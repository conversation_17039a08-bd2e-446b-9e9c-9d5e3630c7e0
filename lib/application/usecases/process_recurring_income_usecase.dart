import 'package:drift/drift.dart';
import '../../core/database/app_database.dart';
import '../../domain/repositories/recurring_income_repository.dart';
import '../../domain/repositories/transaction_repository.dart';

class ProcessRecurringIncomeUseCase {
  final RecurringIncomeRepository recurringIncomeRepository;
  final TransactionRepository transactionRepository;

  ProcessRecurringIncomeUseCase(this.recurringIncomeRepository, this.transactionRepository);

  Future<void> call(RecurringIncome recurringIncome, DateTime dueDate) async {
    // Calculate next due date based on frequency
    final nextDueDate = _calculateNextDueDate(recurringIncome, dueDate);
    
    // Only create transactions for automatic recurring incomes
    if (recurringIncome.isAutomatic) {
      print('Processing recurring income: ${recurringIncome.name}, isAutomatic: true, will create status: active');
      final companion = TransactionsCompanion.insert(
        description: Value(recurringIncome.description ?? recurringIncome.name),
        amount: recurringIncome.amount,
        date: dueDate,
        type: const Value('income'),
        categoryId: Value(recurringIncome.categoryId),
        toAccountId: Value(recurringIncome.toAccountId),
        note: Value('${recurringIncome.note ?? ''} | Recurring: ${recurringIncome.name}'.trim()),
        status: const Value('active'), // Automatic incomes are always active
      );
      
      await transactionRepository.addTransaction(companion);
    } else {
      print('Processing recurring income: ${recurringIncome.name}, isAutomatic: false, will create status: pending');
      // For manual recurring incomes, create pending transactions
      final companion = TransactionsCompanion.insert(
        description: Value(recurringIncome.description ?? recurringIncome.name),
        amount: recurringIncome.amount,
        date: dueDate,
        type: const Value('income'),
        categoryId: Value(recurringIncome.categoryId),
        toAccountId: Value(recurringIncome.toAccountId),
        note: Value('${recurringIncome.note ?? ''} | Recurring: ${recurringIncome.name} | Pending Approval'.trim()),
        status: const Value('pending'), // Manual incomes require approval
      );
      
      await transactionRepository.addTransaction(companion);
    }
    
    // Update the recurring income record
    await recurringIncomeRepository.updateLastProcessedDate(recurringIncome.id, dueDate);
    await recurringIncomeRepository.updateNextDueDate(recurringIncome.id, nextDueDate);
  }

  DateTime _calculateNextDueDate(RecurringIncome recurringIncome, DateTime currentDueDate) {
    switch (recurringIncome.frequency) {
      case 'Weekly':
        return currentDueDate.add(const Duration(days: 7));
      case 'Bi-weekly':
        return currentDueDate.add(const Duration(days: 14));
      case 'Monthly':
        return _addMonths(currentDueDate, 1);
      case 'Quarterly':
        return _addMonths(currentDueDate, 3);
      case 'Yearly':
        return DateTime(currentDueDate.year + 1, currentDueDate.month, currentDueDate.day);
      default:
        return currentDueDate;
    }
  }

  DateTime _addMonths(DateTime date, int months) {
    int year = date.year;
    int month = date.month + months;
    
    while (month > 12) {
      month -= 12;
      year += 1;
    }
    
    // Handle day of month (e.g., 31st in February)
    final lastDayOfMonth = DateTime(year, month + 1, 0).day;
    final day = date.day > lastDayOfMonth ? lastDayOfMonth : date.day;
    
    return DateTime(year, month, day);
  }
} 