import '../../domain/repositories/credit_card_repository.dart';

class GetCreditCardsUseCase {
  final CreditCardRepository repository;
  GetCreditCardsUseCase(this.repository);

  Future<List<CreditCardWithTransactions>> call() async {
    final cards = await repository.getCreditCards();
    final result = <CreditCardWithTransactions>[];
    
    for (final card in cards) {
      final transactions = await repository.getTransactionsByCardId(card.id);
      result.add(CreditCardWithTransactions(card, transactions));
    }
    
    return result;
  }
} 