package com.example.financial_app

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.provider.Telephony
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : FlutterActivity() {
    private val CHANNEL = "sms_channel"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler {
            call, result ->
            if (call.method == "getSmsMessages") {
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_SMS) != PackageManager.PERMISSION_GRANTED) {
                    ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.READ_SMS), 1)
                    result.error("PERMISSION_DENIED", "SMS permission not granted", null)
                    return@setMethodCallHandler
                }
                val smsList = mutableListOf<String>()
                val uri = Telephony.Sms.Inbox.CONTENT_URI
                val projection = arrayOf(Telephony.Sms.BODY)
                val cursor = contentResolver.query(uri, projection, null, null, null)
                if (cursor != null) {
                    val bodyIndex = cursor.getColumnIndex(Telephony.Sms.BODY)
                    while (cursor.moveToNext()) {
                        val body = cursor.getString(bodyIndex)
                        smsList.add(body)
                    }
                    cursor.close()
                }
                result.success(smsList)
            } else {
                result.notImplemented()
            }
        }
    }
}
